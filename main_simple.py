#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试系统主程序 - 简化版本
作者：AI Assistant
版本：2.0 - 简化版
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from src.ui.main_window import MainWindow
from src.utils.config_manager import ConfigManager
from src.utils.database_manager import DatabaseManager
from src.utils.backup_manager import BackupManager

def main():
    """主函数 - 简化启动流程"""
    startup_time = time.time()

    try:
        # 启动信息
        print("🚀 启动考试系统 v2.0...")

        # 创建根窗口
        root = tk.Tk()
        root.title("考试系统 v2.0")

        # 初始化配置管理器
        print("⚙️ 初始化配置...")
        config_manager = ConfigManager()

        # 初始化数据库
        print("💾 初始化数据库...")
        db_manager = DatabaseManager()

        # 初始化备份系统
        print("📦 初始化备份系统...")
        backup_manager = BackupManager()

        # 创建主窗口
        print("🖥️ 创建主界面...")
        app = MainWindow(root, config_manager, db_manager, backup_manager, None)

        # 设置窗口图标
        try:
            root.iconbitmap("assets/icon.ico")
        except:
            pass  # 图标文件不存在时忽略

        # 启动完成统计
        elapsed_time = time.time() - startup_time
        print(f"✅ 系统启动完成！耗时: {elapsed_time:.2f}秒")

        # 启动主循环
        root.mainloop()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("启动错误", f"系统启动失败：{str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    main()
