#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试系统测试启动
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    try:
        print("测试导入 ConfigManager...")
        from src.utils.config_manager import ConfigManager
        print("✅ ConfigManager 导入成功")
        
        print("测试导入 DatabaseManager...")
        from src.utils.database_manager import DatabaseManager
        print("✅ DatabaseManager 导入成功")
        
        print("测试导入 BackupManager...")
        from src.utils.backup_manager import BackupManager
        print("✅ BackupManager 导入成功")
        
        print("测试导入 MainWindow...")
        from src.ui.main_window import MainWindow
        print("✅ MainWindow 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_window():
    """测试基本窗口"""
    try:
        print("创建基本窗口...")
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        label = tk.Label(root, text="考试系统测试", font=("Arial", 16))
        label.pack(pady=50)
        
        button = tk.Button(root, text="关闭", command=root.destroy)
        button.pack(pady=20)
        
        print("✅ 基本窗口创建成功")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 基本窗口创建失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始测试...")
    
    # 测试导入
    if not test_imports():
        return
    
    # 测试基本窗口
    test_basic_window()
    
    print("✅ 测试完成")

if __name__ == "__main__":
    main()
