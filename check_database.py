#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库内容
"""

import sqlite3
import json
import os

def check_database():
    """检查数据库内容"""
    db_path = "data/exam_system.db"
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 数据库内容检查 ===")
        
        # 检查表结构
        print("\n1. 数据库表:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查考试数据
        print("\n2. 考试数据:")
        cursor.execute("SELECT COUNT(*) FROM exams")
        exam_count = cursor.fetchone()[0]
        print(f"  考试总数: {exam_count}")
        
        if exam_count > 0:
            cursor.execute("SELECT id, title, description, time_limit, created_at FROM exams ORDER BY created_at DESC LIMIT 5")
            exams = cursor.fetchall()
            print("  最近的考试:")
            for exam in exams:
                print(f"    ID: {exam[0]}, 标题: '{exam[1]}', 时间: {exam[3]}分钟, 创建时间: {exam[4]}")
        
        # 检查材料数据
        print("\n3. 材料数据:")
        cursor.execute("SELECT COUNT(*) FROM materials")
        material_count = cursor.fetchone()[0]
        print(f"  材料总数: {material_count}")
        
        if material_count > 0:
            cursor.execute("SELECT id, title, type, created_at FROM materials ORDER BY created_at DESC LIMIT 3")
            materials = cursor.fetchall()
            print("  最近的材料:")
            for material in materials:
                print(f"    ID: {material[0]}, 标题: '{material[1]}', 类型: {material[2]}")
        
        # 检查考试记录
        print("\n4. 考试记录:")
        cursor.execute("SELECT COUNT(*) FROM exam_records")
        record_count = cursor.fetchone()[0]
        print(f"  考试记录总数: {record_count}")
        
        # 检查最新考试的题目内容
        if exam_count > 0:
            print("\n5. 最新考试的题目内容:")
            cursor.execute("SELECT questions FROM exams ORDER BY created_at DESC LIMIT 1")
            questions_json = cursor.fetchone()[0]
            try:
                questions = json.loads(questions_json)
                print(f"  题目数量: {len(questions)}")
                for i, q in enumerate(questions[:2]):  # 只显示前2题
                    print(f"  题目{i+1}: {q.get('question', 'N/A')[:50]}...")
                    print(f"    类型: {q.get('type', 'N/A')}")
                    print(f"    答案: {q.get('correct_answer', 'N/A')}")
            except json.JSONDecodeError as e:
                print(f"  ❌ 题目JSON解析失败: {e}")
        
        conn.close()
        
        print("\n=== 检查完成 ===")
        
        if exam_count == 0:
            print("\n⚠️  没有找到任何考试数据")
            print("建议:")
            print("1. 使用'生成试卷'功能创建考试")
            print("2. 确保试卷生成后点击'保存试卷'")
            print("3. 检查控制台是否有错误信息")
        else:
            print(f"\n✅ 数据库中有 {exam_count} 个考试")
            print("如果考试界面看不到试卷，请:")
            print("1. 重新启动程序")
            print("2. 点击'开始考试'")
            print("3. 检查控制台输出")
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
