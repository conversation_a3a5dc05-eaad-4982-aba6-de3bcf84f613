#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱可视化窗口
提供知识图谱的可视化展示和交互功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import networkx as nx
import numpy as np
from src.core.knowledge_graph_manager import KnowledgeGraphManager

class KnowledgeGraphWindow:
    def __init__(self, parent, db_manager):
        """初始化知识图谱窗口"""
        self.parent = parent
        self.db = db_manager
        self.kg_manager = KnowledgeGraphManager(db_manager)
        
        self.window = tk.Toplevel(parent)
        self.window.title("知识图谱")
        self.window.geometry("1200x800")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_knowledge_points()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="知识图谱可视化", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 创建分割面板
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        self.create_control_panel(paned)
        
        # 右侧图谱显示面板
        self.create_graph_panel(paned)
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding=10)
        parent.add(control_frame, weight=1)
        
        # 图谱生成选项
        generation_frame = ttk.LabelFrame(control_frame, text="图谱生成", padding=10)
        generation_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(generation_frame, text="从数据库构建", 
                  command=self.build_from_database).pack(fill=tk.X, pady=2)
        ttk.Button(generation_frame, text="从材料构建", 
                  command=self.build_from_materials).pack(fill=tk.X, pady=2)
        ttk.Button(generation_frame, text="清空图谱", 
                  command=self.clear_graph).pack(fill=tk.X, pady=2)
        
        # 布局选项
        layout_frame = ttk.LabelFrame(control_frame, text="布局算法", padding=10)
        layout_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.layout_var = tk.StringVar(value="spring")
        layouts = [
            ("弹簧布局", "spring"),
            ("圆形布局", "circular"),
            ("随机布局", "random"),
            ("层次布局", "hierarchical")
        ]
        
        for text, value in layouts:
            ttk.Radiobutton(layout_frame, text=text, variable=self.layout_var, 
                          value=value, command=self.update_graph).pack(anchor=tk.W)
        
        # 显示选项
        display_frame = ttk.LabelFrame(control_frame, text="显示选项", padding=10)
        display_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.show_labels_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(display_frame, text="显示标签", 
                       variable=self.show_labels_var, 
                       command=self.update_graph).pack(anchor=tk.W)
        
        self.show_edges_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(display_frame, text="显示连线", 
                       variable=self.show_edges_var, 
                       command=self.update_graph).pack(anchor=tk.W)
        
        # 节点大小
        ttk.Label(display_frame, text="节点大小:").pack(anchor=tk.W, pady=(10, 0))
        self.node_size_var = tk.IntVar(value=500)
        node_size_scale = ttk.Scale(display_frame, from_=100, to=2000, 
                                   variable=self.node_size_var, 
                                   orient=tk.HORIZONTAL,
                                   command=lambda x: self.update_graph())
        node_size_scale.pack(fill=tk.X, pady=2)
        
        # 统计信息
        stats_frame = ttk.LabelFrame(control_frame, text="统计信息", padding=10)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.stats_text = tk.Text(stats_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)
        
        # 操作按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="添加知识点", 
                  command=self.add_knowledge_point).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="编辑知识点", 
                  command=self.edit_knowledge_point).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="导出图谱", 
                  command=self.export_graph).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="导入图谱", 
                  command=self.import_graph).pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="关闭", 
                  command=self.window.destroy).pack(fill=tk.X, pady=(10, 0))
    
    def create_graph_panel(self, parent):
        """创建右侧图谱显示面板"""
        graph_frame = ttk.LabelFrame(parent, text="知识图谱", padding=10)
        parent.add(graph_frame, weight=3)
        
        # 创建matplotlib图形
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.canvas = FigureCanvasTkAgg(self.fig, graph_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        toolbar = NavigationToolbar2Tk(self.canvas, graph_frame)
        toolbar.update()
        
        # 绑定点击事件
        self.canvas.mpl_connect('button_press_event', self.on_node_click)
        
        # 初始化空图
        self.ax.text(0.5, 0.5, '请选择图谱生成方式', 
                    horizontalalignment='center', verticalalignment='center',
                    transform=self.ax.transAxes, fontsize=16)
        self.canvas.draw()
    
    def load_knowledge_points(self):
        """加载知识点数据"""
        self.update_statistics()
    
    def build_from_database(self):
        """从数据库构建图谱"""
        try:
            self.kg_manager.build_graph_from_database()
            self.update_graph()
            self.update_statistics()
            messagebox.showinfo("成功", "从数据库构建图谱完成！")
        except Exception as e:
            messagebox.showerror("错误", f"构建图谱失败：{str(e)}")
    
    def build_from_materials(self):
        """从材料构建图谱"""
        try:
            self.kg_manager.build_graph_from_materials()
            self.update_graph()
            self.update_statistics()
            messagebox.showinfo("成功", "从材料构建图谱完成！")
        except Exception as e:
            messagebox.showerror("错误", f"构建图谱失败：{str(e)}")
    
    def clear_graph(self):
        """清空图谱"""
        if messagebox.askyesno("确认", "确定要清空当前图谱吗？"):
            self.kg_manager.graph.clear()
            self.update_graph()
            self.update_statistics()
    
    def update_graph(self):
        """更新图谱显示"""
        self.ax.clear()
        
        if not self.kg_manager.graph.nodes():
            self.ax.text(0.5, 0.5, '图谱为空\n请先构建图谱', 
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=16)
            self.canvas.draw()
            return
        
        # 选择布局算法
        layout_type = self.layout_var.get()
        
        if layout_type == "spring":
            pos = nx.spring_layout(self.kg_manager.graph, k=1, iterations=50)
        elif layout_type == "circular":
            pos = nx.circular_layout(self.kg_manager.graph)
        elif layout_type == "random":
            pos = nx.random_layout(self.kg_manager.graph)
        elif layout_type == "hierarchical":
            pos = nx.nx_agraph.graphviz_layout(self.kg_manager.graph, prog='dot') if hasattr(nx, 'nx_agraph') else nx.spring_layout(self.kg_manager.graph)
        else:
            pos = nx.spring_layout(self.kg_manager.graph)
        
        # 绘制边
        if self.show_edges_var.get():
            nx.draw_networkx_edges(self.kg_manager.graph, pos, ax=self.ax, 
                                 alpha=0.5, edge_color='gray', width=1)
        
        # 绘制节点
        node_size = self.node_size_var.get()
        nx.draw_networkx_nodes(self.kg_manager.graph, pos, ax=self.ax,
                             node_color='lightblue', node_size=node_size,
                             alpha=0.8)
        
        # 绘制标签
        if self.show_labels_var.get():
            labels = {}
            for node_id in self.kg_manager.graph.nodes():
                node_data = self.kg_manager.graph.nodes[node_id]
                name = node_data.get('name', f'Node {node_id}')
                # 限制标签长度
                labels[node_id] = name[:8] + '...' if len(name) > 8 else name
            
            nx.draw_networkx_labels(self.kg_manager.graph, pos, labels, ax=self.ax,
                                  font_size=8)
        
        self.ax.set_title("知识图谱", fontsize=16, fontweight='bold')
        self.ax.axis('off')
        
        # 保存位置信息用于点击检测
        self.node_positions = pos
        
        self.canvas.draw()
    
    def update_statistics(self):
        """更新统计信息"""
        stats = self.kg_manager.get_graph_statistics()
        
        stats_text = f"节点数量: {stats['nodes']}\n"
        stats_text += f"边数量: {stats['edges']}\n"
        stats_text += f"图密度: {stats['density']:.3f}\n"
        stats_text += f"连通分量: {stats['components']}\n"
        
        # 获取中心节点
        if stats['nodes'] > 0:
            central_nodes = self.kg_manager.get_central_nodes(5)
            stats_text += "\n中心节点:\n"
            for node_id, centrality in central_nodes:
                node_data = self.kg_manager.graph.nodes[node_id]
                name = node_data.get('name', f'Node {node_id}')
                stats_text += f"  {name}: {centrality:.3f}\n"
        
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
        self.stats_text.config(state=tk.DISABLED)
    
    def on_node_click(self, event):
        """节点点击事件"""
        if event.inaxes != self.ax or not hasattr(self, 'node_positions'):
            return
        
        # 查找最近的节点
        min_distance = float('inf')
        clicked_node = None
        
        for node_id, (x, y) in self.node_positions.items():
            distance = ((event.xdata - x) ** 2 + (event.ydata - y) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                clicked_node = node_id
        
        # 如果点击距离足够近，显示节点信息
        if min_distance < 0.1 and clicked_node is not None:
            self.show_node_info(clicked_node)
    
    def show_node_info(self, node_id):
        """显示节点信息"""
        node_data = self.kg_manager.graph.nodes[node_id]
        name = node_data.get('name', f'Node {node_id}')
        description = node_data.get('description', '无描述')
        
        neighbors = self.kg_manager.get_node_neighbors(node_id)
        neighbor_names = []
        for neighbor_id in neighbors:
            neighbor_data = self.kg_manager.graph.nodes[neighbor_id]
            neighbor_names.append(neighbor_data.get('name', f'Node {neighbor_id}'))
        
        info_text = f"知识点: {name}\n\n"
        info_text += f"描述: {description}\n\n"
        info_text += f"相关知识点: {', '.join(neighbor_names) if neighbor_names else '无'}"
        
        messagebox.showinfo("知识点信息", info_text)
    
    def add_knowledge_point(self):
        """添加知识点"""
        KnowledgePointDialog(self.window, self.kg_manager, None, self.refresh_graph)
    
    def edit_knowledge_point(self):
        """编辑知识点"""
        # 这里可以添加选择知识点的逻辑
        messagebox.showinfo("提示", "请在图谱中点击节点查看信息，或使用数据库管理功能编辑知识点")
    
    def export_graph(self):
        """导出图谱"""
        filename = filedialog.asksaveasfilename(
            title="导出知识图谱",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                self.kg_manager.save_graph_to_file(filename)
                messagebox.showinfo("成功", "图谱导出成功！")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败：{str(e)}")
    
    def import_graph(self):
        """导入图谱"""
        filename = filedialog.askopenfilename(
            title="导入知识图谱",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                if self.kg_manager.load_graph_from_file(filename):
                    self.update_graph()
                    self.update_statistics()
                    messagebox.showinfo("成功", "图谱导入成功！")
                else:
                    messagebox.showerror("错误", "导入失败！")
            except Exception as e:
                messagebox.showerror("错误", f"导入失败：{str(e)}")
    
    def refresh_graph(self):
        """刷新图谱显示"""
        self.build_from_database()


class KnowledgePointDialog:
    def __init__(self, parent, kg_manager, point_data, refresh_callback):
        """知识点编辑对话框"""
        self.kg_manager = kg_manager
        self.point_data = point_data
        self.refresh_callback = refresh_callback
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("编辑知识点" if point_data else "添加知识点")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 名称输入
        ttk.Label(main_frame, text="名称:").pack(anchor=tk.W)
        self.name_var = tk.StringVar(value=self.point_data[1] if self.point_data else "")
        name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=40)
        name_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 描述输入
        ttk.Label(main_frame, text="描述:").pack(anchor=tk.W)
        self.description_text = tk.Text(main_frame, height=8, wrap=tk.WORD)
        self.description_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        if self.point_data:
            self.description_text.insert(1.0, self.point_data[2] or "")
        
        # 父节点选择
        ttk.Label(main_frame, text="父知识点:").pack(anchor=tk.W)
        self.parent_var = tk.StringVar()
        parent_combo = ttk.Combobox(main_frame, textvariable=self.parent_var, state="readonly")
        parent_combo.pack(fill=tk.X, pady=(0, 10))
        
        # 加载父节点选项
        points = self.kg_manager.get_all_knowledge_points()
        parent_options = ["无"] + [f"{p[0]} - {p[1]}" for p in points if not self.point_data or p[0] != self.point_data[0]]
        parent_combo['values'] = parent_options
        parent_combo.current(0)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="保存", command=self.save_point).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)
    
    def save_point(self):
        """保存知识点"""
        name = self.name_var.get().strip()
        description = self.description_text.get(1.0, tk.END).strip()
        
        if not name:
            messagebox.showwarning("警告", "请输入知识点名称！")
            return
        
        # 解析父节点ID
        parent_id = None
        parent_text = self.parent_var.get()
        if parent_text and parent_text != "无":
            parent_id = int(parent_text.split(' - ')[0])
        
        try:
            if self.point_data:
                # 更新
                self.kg_manager.update_knowledge_point(self.point_data[0], name, description, parent_id)
                messagebox.showinfo("成功", "知识点更新成功！")
            else:
                # 添加
                self.kg_manager.add_knowledge_point(name, description, parent_id)
                messagebox.showinfo("成功", "知识点添加成功！")
            
            self.refresh_callback()
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存失败：{str(e)}")
