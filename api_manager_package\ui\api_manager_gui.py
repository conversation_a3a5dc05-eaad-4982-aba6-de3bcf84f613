#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API管理器GUI界面 - 简化版
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.universal_api_manager import UniversalAPIManager

class APIManagerGUI:
    def __init__(self):
        self.api_manager = UniversalAPIManager()
        self.root = tk.Tk()
        self.root.title("🚀 通用API管理器")
        self.root.geometry("600x400")
        
        self.setup_ui()
        self.refresh_provider_list()
    
    def setup_ui(self):
        """设置用户界面"""
        
        # 主标题
        title_label = ttk.Label(self.root, text="🚀 通用API管理器", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 提供商列表
        frame = ttk.LabelFrame(self.root, text="API提供商列表")
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Treeview
        columns = ("名称", "状态", "URL")
        self.tree = ttk.Treeview(frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        self.tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="➕ 添加API", command=self.add_api).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧪 测试连接", command=self.test_api).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 刷新", command=self.refresh_provider_list).pack(side=tk.LEFT, padx=5)
    
    def refresh_provider_list(self):
        """刷新提供商列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加提供商
        for name, provider in self.api_manager.providers.items():
            status_icon = "✅" if provider.status == "正常" else "❌" if provider.status == "异常" else "⚪"
            status_text = f"{status_icon} {provider.status}"
            
            self.tree.insert("", tk.END, values=(
                provider.display_name,
                status_text,
                provider.base_url
            ), tags=(name,))
    
    def add_api(self):
        """添加API（简化版）"""
        messagebox.showinfo("提示", "请使用代码方式添加自定义API\n\n示例：\napi_manager.add_custom_provider(\n    name='my_api',\n    display_name='我的API',\n    base_url='https://api.example.com/v1',\n    api_key='your_key'\n)")
    
    def test_api(self):
        """测试API"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个API提供商")
            return
        
        item = self.tree.item(selection[0])
        provider_name = item['tags'][0]
        provider = self.api_manager.providers[provider_name]
        
        if not provider.api_key:
            messagebox.showwarning("警告", f"{provider.display_name} 未配置API密钥")
            return
        
        messagebox.showinfo("提示", f"开始测试 {provider.display_name}...")
        success = self.api_manager.test_provider_connection(provider_name)
        
        if success:
            messagebox.showinfo("成功", f"{provider.display_name} 连接测试成功！")
        else:
            messagebox.showerror("失败", f"{provider.display_name} 连接测试失败\n\n{provider.error_message}")
        
        self.refresh_provider_list()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = APIManagerGUI()
    app.run()
