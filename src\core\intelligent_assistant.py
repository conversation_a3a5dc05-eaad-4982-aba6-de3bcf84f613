#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能学习助手
提供AI驱动的学习指导、答疑和个性化建议
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import random

class IntelligentAssistant:
    def __init__(self, learning_analytics, ai_manager=None):
        """初始化智能助手"""
        self.learning_analytics = learning_analytics
        self.ai_manager = ai_manager
        self.conversation_history = []
        self.user_profile = {}
        
        # 预定义回答模板
        self.response_templates = {
            'greeting': [
                "你好！我是你的智能学习助手，很高兴为你服务！",
                "欢迎回来！让我们继续你的学习之旅吧！",
                "嗨！准备好开始今天的学习了吗？"
            ],
            'encouragement': [
                "你做得很棒！继续保持这个势头！",
                "每一次练习都是进步，加油！",
                "学习是一个过程，你正在稳步前进！"
            ],
            'study_tips': [
                "建议采用番茄工作法，25分钟专注学习，5分钟休息。",
                "复习时可以使用费曼学习法，试着向别人解释你学到的内容。",
                "制作思维导图可以帮助你更好地理解和记忆知识点。"
            ]
        }
        
    def process_user_query(self, query: str, context: Dict = None) -> Dict:
        """处理用户查询"""
        try:
            # 记录对话历史
            self.conversation_history.append({
                'timestamp': datetime.now(),
                'user_query': query,
                'context': context or {}
            })
            
            # 分析查询意图
            intent = self.analyze_intent(query)
            
            # 根据意图生成回答
            response = self.generate_response(intent, query, context)
            
            # 记录助手回答
            self.conversation_history[-1]['assistant_response'] = response
            
            return response
            
        except Exception as e:
            print(f"❌ 处理用户查询失败: {e}")
            return {
                'type': 'error',
                'content': '抱歉，我现在无法处理你的问题，请稍后再试。',
                'suggestions': ['尝试重新表述问题', '查看帮助文档', '联系技术支持']
            }
            
    def analyze_intent(self, query: str) -> str:
        """分析用户查询意图"""
        try:
            query_lower = query.lower()
            
            # 学习状态查询
            if any(word in query_lower for word in ['进度', '成绩', '表现', '统计', '分析']):
                return 'learning_status'
            
            # 学习建议请求
            elif any(word in query_lower for word in ['建议', '推荐', '怎么学', '如何提高', '方法']):
                return 'learning_advice'
            
            # 知识点解释
            elif any(word in query_lower for word in ['什么是', '解释', '含义', '定义', '原理']):
                return 'knowledge_explanation'
            
            # 学习计划
            elif any(word in query_lower for word in ['计划', '安排', '规划', '时间表']):
                return 'study_planning'
            
            # 错题分析
            elif any(word in query_lower for word in ['错题', '错误', '失误', '问题']):
                return 'error_analysis'
            
            # 激励鼓励
            elif any(word in query_lower for word in ['鼓励', '加油', '动力', '坚持']):
                return 'encouragement'
            
            # 问候
            elif any(word in query_lower for word in ['你好', '嗨', 'hello', '早上好', '下午好']):
                return 'greeting'
            
            # 默认为一般咨询
            else:
                return 'general_inquiry'
                
        except Exception as e:
            print(f"❌ 分析查询意图失败: {e}")
            return 'general_inquiry'
            
    def generate_response(self, intent: str, query: str, context: Dict = None) -> Dict:
        """根据意图生成回答"""
        try:
            if intent == 'learning_status':
                return self.get_learning_status_response()
            elif intent == 'learning_advice':
                return self.get_learning_advice_response(query)
            elif intent == 'knowledge_explanation':
                return self.get_knowledge_explanation_response(query)
            elif intent == 'study_planning':
                return self.get_study_planning_response()
            elif intent == 'error_analysis':
                return self.get_error_analysis_response()
            elif intent == 'encouragement':
                return self.get_encouragement_response()
            elif intent == 'greeting':
                return self.get_greeting_response()
            else:
                return self.get_general_response(query)
                
        except Exception as e:
            print(f"❌ 生成回答失败: {e}")
            return {
                'type': 'error',
                'content': '抱歉，我无法生成合适的回答。',
                'suggestions': []
            }
            
    def get_learning_status_response(self) -> Dict:
        """获取学习状态回答"""
        try:
            stats = self.learning_analytics.get_learning_statistics()
            mastery_data = self.learning_analytics.get_knowledge_mastery_overview()
            
            if not stats:
                return {
                    'type': 'info',
                    'content': '你还没有开始学习记录，建议先完成一些练习或考试。',
                    'suggestions': ['开始一次练习', '查看可用题目', '设置学习目标']
                }
            
            overall = stats.get('overall', {})
            recent = stats.get('recent_week', {})
            knowledge = stats.get('knowledge', {})
            
            content = f"📊 你的学习状态分析：\n\n"
            content += f"🎯 整体表现：\n"
            content += f"• 总学习次数：{overall.get('total_sessions', 0)} 次\n"
            content += f"• 总学习时长：{overall.get('total_time', 0)//3600:.1f} 小时\n"
            content += f"• 整体正确率：{overall.get('overall_accuracy', 0):.1%}\n\n"
            
            content += f"📈 最近一周：\n"
            content += f"• 学习次数：{recent.get('sessions', 0)} 次\n"
            content += f"• 学习时长：{recent.get('time', 0)//3600:.1f} 小时\n"
            content += f"• 正确率：{recent.get('accuracy', 0):.1%}\n\n"
            
            # 分析表现趋势
            if recent.get('accuracy', 0) > overall.get('overall_accuracy', 0):
                content += "🎉 太棒了！你的表现正在稳步提升！"
            elif recent.get('accuracy', 0) < overall.get('overall_accuracy', 0):
                content += "💪 最近表现有所波动，但这是学习过程的正常现象，继续努力！"
            else:
                content += "📊 你的表现保持稳定，可以尝试挑战更高难度的内容。"
            
            suggestions = ['查看详细分析', '生成学习建议', '开始新的练习']
            
            return {
                'type': 'analysis',
                'content': content,
                'suggestions': suggestions,
                'data': stats
            }
            
        except Exception as e:
            print(f"❌ 获取学习状态失败: {e}")
            return {
                'type': 'error',
                'content': '无法获取学习状态，请稍后再试。',
                'suggestions': []
            }
            
    def get_learning_advice_response(self, query: str) -> Dict:
        """获取学习建议回答"""
        try:
            recommendations = self.learning_analytics.generate_personalized_recommendations()
            mastery_data = self.learning_analytics.get_knowledge_mastery_overview()
            
            content = "💡 基于你的学习情况，我为你准备了以下建议：\n\n"
            
            if recommendations:
                for i, rec in enumerate(recommendations[:3], 1):
                    priority_icon = "🔴" if rec['priority'] == 1 else "🟡" if rec['priority'] == 2 else "🟢"
                    content += f"{priority_icon} {i}. {rec['title']}\n"
                    content += f"   {rec['content']}\n"
                    content += f"   💭 {rec['reason']}\n\n"
            
            # 添加通用学习建议
            content += "📚 通用学习技巧：\n"
            content += "• 制定明确的学习目标和时间表\n"
            content += "• 采用主动学习方法，如总结和提问\n"
            content += "• 定期复习，利用间隔重复原理\n"
            content += "• 保持良好的学习环境和作息\n"
            
            # 根据查询内容提供特定建议
            if '时间' in query:
                content += "\n⏰ 时间管理建议：\n"
                content += "• 使用番茄工作法，25分钟专注+5分钟休息\n"
                content += "• 优先处理重要且紧急的学习任务\n"
                content += "• 避免多任务处理，专注于当前学习内容\n"
            
            suggestions = ['查看详细学习计划', '开始推荐练习', '设置学习提醒']
            
            return {
                'type': 'advice',
                'content': content,
                'suggestions': suggestions,
                'recommendations': recommendations
            }
            
        except Exception as e:
            print(f"❌ 获取学习建议失败: {e}")
            return {
                'type': 'error',
                'content': '无法生成学习建议，请稍后再试。',
                'suggestions': []
            }
            
    def get_knowledge_explanation_response(self, query: str) -> Dict:
        """获取知识点解释回答"""
        try:
            # 提取查询中的关键词
            keywords = self.extract_keywords(query)
            
            content = f"🤔 关于 '{' '.join(keywords)}' 的解释：\n\n"
            
            # 如果有AI管理器，尝试使用AI生成解释
            if self.ai_manager:
                try:
                    ai_explanation = self.get_ai_explanation(keywords)
                    if ai_explanation:
                        content += ai_explanation
                    else:
                        content += self.get_default_explanation(keywords)
                except:
                    content += self.get_default_explanation(keywords)
            else:
                content += self.get_default_explanation(keywords)
            
            suggestions = ['查看相关练习题', '深入学习该主题', '查看知识图谱']
            
            return {
                'type': 'explanation',
                'content': content,
                'suggestions': suggestions,
                'keywords': keywords
            }
            
        except Exception as e:
            print(f"❌ 获取知识解释失败: {e}")
            return {
                'type': 'error',
                'content': '无法提供知识点解释，请稍后再试。',
                'suggestions': []
            }
            
    def get_study_planning_response(self) -> Dict:
        """获取学习计划回答"""
        try:
            mastery_data = self.learning_analytics.get_knowledge_mastery_overview()
            
            content = "📅 为你制定个性化学习计划：\n\n"
            
            if mastery_data:
                # 识别薄弱环节
                weak_topics = []
                strong_topics = []
                
                for topic, data in mastery_data.items():
                    if data['average_mastery'] < 0.6:
                        weak_topics.append(topic)
                    elif data['average_mastery'] >= 0.8:
                        strong_topics.append(topic)
                
                # 制定计划
                content += "🎯 本周学习重点：\n"
                if weak_topics:
                    content += f"• 重点攻克：{', '.join(weak_topics[:3])}\n"
                    content += f"• 建议每天花30-45分钟练习薄弱知识点\n"
                
                if strong_topics:
                    content += f"• 保持优势：{', '.join(strong_topics[:2])}\n"
                    content += f"• 每周复习一次，保持熟练度\n"
                
                content += "\n📚 每日学习建议：\n"
                content += "• 周一至周五：重点知识点学习（30-45分钟）\n"
                content += "• 周六：综合练习和模拟考试（60分钟）\n"
                content += "• 周日：复习和总结（30分钟）\n"
            else:
                content += "由于还没有学习数据，建议从以下步骤开始：\n"
                content += "1. 完成一次基础测试，了解当前水平\n"
                content += "2. 根据测试结果制定学习重点\n"
                content += "3. 每天保持30-60分钟的学习时间\n"
                content += "4. 定期进行自我评估和调整\n"
            
            suggestions = ['开始今日学习', '设置学习提醒', '查看学习进度']
            
            return {
                'type': 'planning',
                'content': content,
                'suggestions': suggestions
            }
            
        except Exception as e:
            print(f"❌ 获取学习计划失败: {e}")
            return {
                'type': 'error',
                'content': '无法制定学习计划，请稍后再试。',
                'suggestions': []
            }
            
    def get_error_analysis_response(self) -> Dict:
        """获取错题分析回答"""
        try:
            content = "🔍 错题分析和改进建议：\n\n"
            
            # 这里可以集成错题管理器的数据
            content += "📊 常见错误类型：\n"
            content += "• 概念理解不清：建议重新学习基础概念\n"
            content += "• 计算错误：注意细心检查，多做练习\n"
            content += "• 题目理解偏差：提高阅读理解能力\n"
            content += "• 时间管理不当：练习在限定时间内完成\n\n"
            
            content += "💡 改进策略：\n"
            content += "• 建立错题本，定期复习错题\n"
            content += "• 分析错误原因，针对性改进\n"
            content += "• 寻找相似题型进行强化练习\n"
            content += "• 请教老师或同学，获得帮助\n"
            
            suggestions = ['查看错题本', '开始错题练习', '分析错误模式']
            
            return {
                'type': 'analysis',
                'content': content,
                'suggestions': suggestions
            }
            
        except Exception as e:
            print(f"❌ 获取错题分析失败: {e}")
            return {
                'type': 'error',
                'content': '无法进行错题分析，请稍后再试。',
                'suggestions': []
            }
            
    def get_encouragement_response(self) -> Dict:
        """获取鼓励回答"""
        try:
            encouragement = random.choice(self.response_templates['encouragement'])
            
            content = f"💪 {encouragement}\n\n"
            content += "🌟 记住：\n"
            content += "• 每一次练习都是进步的机会\n"
            content += "• 错误是学习过程中的正常现象\n"
            content += "• 坚持是成功的关键\n"
            content += "• 你已经在正确的道路上了！\n"
            
            suggestions = ['继续学习', '查看学习进度', '设置新目标']
            
            return {
                'type': 'encouragement',
                'content': content,
                'suggestions': suggestions
            }
            
        except Exception as e:
            print(f"❌ 获取鼓励回答失败: {e}")
            return {
                'type': 'encouragement',
                'content': '加油！你一定可以的！',
                'suggestions': []
            }
            
    def get_greeting_response(self) -> Dict:
        """获取问候回答"""
        try:
            greeting = random.choice(self.response_templates['greeting'])
            
            # 根据时间添加个性化问候
            current_hour = datetime.now().hour
            if 6 <= current_hour < 12:
                time_greeting = "早上好！"
            elif 12 <= current_hour < 18:
                time_greeting = "下午好！"
            else:
                time_greeting = "晚上好！"
            
            content = f"{time_greeting} {greeting}\n\n"
            
            # 添加今日学习建议
            stats = self.learning_analytics.get_learning_statistics()
            if stats and stats.get('recent_week', {}).get('sessions', 0) > 0:
                content += "今天准备学习什么呢？我可以为你：\n"
                content += "• 分析你的学习进度\n"
                content += "• 推荐适合的练习内容\n"
                content += "• 制定学习计划\n"
                content += "• 回答学习问题\n"
            else:
                content += "看起来你是新用户，建议先：\n"
                content += "• 完成一次基础测试\n"
                content += "• 了解系统功能\n"
                content += "• 设置学习目标\n"
            
            suggestions = ['查看学习状态', '开始练习', '获取学习建议', '了解系统功能']
            
            return {
                'type': 'greeting',
                'content': content,
                'suggestions': suggestions
            }
            
        except Exception as e:
            print(f"❌ 获取问候回答失败: {e}")
            return {
                'type': 'greeting',
                'content': '你好！我是你的学习助手，有什么可以帮助你的吗？',
                'suggestions': []
            }
            
    def get_general_response(self, query: str) -> Dict:
        """获取一般回答"""
        try:
            content = "🤖 我理解你的问题，让我为你提供一些帮助：\n\n"
            
            # 尝试从查询中提取关键信息
            if '学习' in query:
                content += "关于学习，我建议：\n"
                content += "• 制定明确的学习目标\n"
                content += "• 保持规律的学习习惯\n"
                content += "• 及时复习和总结\n"
            elif '考试' in query:
                content += "关于考试，我建议：\n"
                content += "• 充分准备，制定复习计划\n"
                content += "• 多做模拟练习\n"
                content += "• 保持良好的心态\n"
            else:
                content += "我可以帮助你：\n"
                content += "• 分析学习进度和表现\n"
                content += "• 提供个性化学习建议\n"
                content += "• 解答学习相关问题\n"
                content += "• 制定学习计划\n"
            
            suggestions = ['查看学习分析', '获取学习建议', '开始练习', '查看帮助']
            
            return {
                'type': 'general',
                'content': content,
                'suggestions': suggestions
            }
            
        except Exception as e:
            print(f"❌ 获取一般回答失败: {e}")
            return {
                'type': 'general',
                'content': '我会尽力帮助你学习，请告诉我你需要什么帮助。',
                'suggestions': []
            }
            
    def extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        try:
            # 移除常见的停用词
            stop_words = {'什么是', '解释', '告诉我', '我想知道', '请问', '的', '了', '吗', '呢', '吧'}
            
            # 简单的关键词提取
            words = re.findall(r'\w+', text)
            keywords = [word for word in words if word not in stop_words and len(word) > 1]
            
            return keywords[:5]  # 返回前5个关键词
            
        except Exception as e:
            print(f"❌ 提取关键词失败: {e}")
            return []
            
    def get_ai_explanation(self, keywords: List[str]) -> Optional[str]:
        """使用AI生成知识点解释"""
        try:
            if not self.ai_manager or not keywords:
                return None
            
            # 构建AI查询
            query = f"请解释以下概念：{', '.join(keywords)}"
            
            # 调用AI接口（这里需要根据实际的AI管理器接口调整）
            # response = self.ai_manager.generate_response(query)
            # return response
            
            # 暂时返回None，使用默认解释
            return None
            
        except Exception as e:
            print(f"❌ AI解释生成失败: {e}")
            return None
            
    def get_default_explanation(self, keywords: List[str]) -> str:
        """获取默认知识点解释"""
        try:
            if not keywords:
                return "请提供更具体的问题，我会尽力为你解答。"
            
            content = f"关于 '{', '.join(keywords)}'，这是一个重要的学习概念。\n\n"
            content += "建议你：\n"
            content += "• 查阅相关教材和资料\n"
            content += "• 寻找具体的例子和应用\n"
            content += "• 通过练习题加深理解\n"
            content += "• 与同学或老师讨论\n\n"
            content += "如果需要更详细的解释，建议查看专业资料或咨询专业人士。"
            
            return content
            
        except Exception as e:
            print(f"❌ 获取默认解释失败: {e}")
            return "抱歉，我无法提供详细解释，请查阅相关资料。"
            
    def get_conversation_summary(self) -> Dict:
        """获取对话摘要"""
        try:
            if not self.conversation_history:
                return {
                    'total_conversations': 0,
                    'main_topics': [],
                    'user_interests': []
                }
            
            # 分析对话历史
            total_conversations = len(self.conversation_history)
            
            # 提取主要话题
            all_queries = [conv['user_query'] for conv in self.conversation_history]
            main_topics = self.extract_main_topics(all_queries)
            
            # 分析用户兴趣
            user_interests = self.analyze_user_interests(all_queries)
            
            return {
                'total_conversations': total_conversations,
                'main_topics': main_topics,
                'user_interests': user_interests,
                'last_conversation': self.conversation_history[-1]['timestamp'] if self.conversation_history else None
            }
            
        except Exception as e:
            print(f"❌ 获取对话摘要失败: {e}")
            return {}
            
    def extract_main_topics(self, queries: List[str]) -> List[str]:
        """提取主要话题"""
        try:
            topic_keywords = {
                '学习进度': ['进度', '成绩', '表现', '统计'],
                '学习方法': ['方法', '技巧', '建议', '怎么学'],
                '知识点': ['什么是', '解释', '概念', '原理'],
                '学习计划': ['计划', '安排', '规划', '时间'],
                '错题分析': ['错题', '错误', '失误', '问题']
            }
            
            topic_counts = {}
            for query in queries:
                query_lower = query.lower()
                for topic, keywords in topic_keywords.items():
                    if any(keyword in query_lower for keyword in keywords):
                        topic_counts[topic] = topic_counts.get(topic, 0) + 1
            
            # 返回出现频率最高的话题
            sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)
            return [topic for topic, count in sorted_topics[:5]]
            
        except Exception as e:
            print(f"❌ 提取主要话题失败: {e}")
            return []
            
    def analyze_user_interests(self, queries: List[str]) -> List[str]:
        """分析用户兴趣"""
        try:
            interests = []
            
            # 简单的兴趣分析
            if any('数学' in query for query in queries):
                interests.append('数学')
            if any('英语' in query for query in queries):
                interests.append('英语')
            if any('科学' in query for query in queries):
                interests.append('科学')
            
            return interests
            
        except Exception as e:
            print(f"❌ 分析用户兴趣失败: {e}")
            return []
