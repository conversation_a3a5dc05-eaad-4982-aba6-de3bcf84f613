#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型API获取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.relay_api_client import RelayAPIClient

def test_qwen_models():
    """测试通义千问模型获取"""
    print("=" * 50)
    print("测试通义千问模型获取")
    print("=" * 50)
    
    # 使用示例配置（请替换为您的真实API Key）
    client = RelayAPIClient(
        api_key="your_api_key_here",  # 请替换为真实的API Key
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        model="qwen-turbo"
    )
    
    print("1. 测试连接...")
    if client.test_connection():
        print("✅ 连接测试成功")
    else:
        print("❌ 连接测试失败")
    
    print("\n2. 获取模型列表...")
    models = client.get_models_from_api()
    
    print(f"\n获取到的模型列表 ({len(models)} 个):")
    for i, model in enumerate(models, 1):
        print(f"  {i:2d}. {model}")
    
    return models

def test_generic_api():
    """测试通用API"""
    print("\n" + "=" * 50)
    print("测试通用API模型获取")
    print("=" * 50)
    
    client = RelayAPIClient(
        api_key="test_key",
        base_url="https://api.example.com/v1",
        model="test-model"
    )
    
    models = client.get_models_from_api()
    print(f"通用API模型列表 ({len(models)} 个):")
    for i, model in enumerate(models, 1):
        print(f"  {i:2d}. {model}")
    
    return models

def main():
    """主函数"""
    print("🚀 开始测试模型API获取功能")
    
    # 测试通义千问
    qwen_models = test_qwen_models()
    
    # 测试通用API
    generic_models = test_generic_api()
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"通义千问模型数量: {len(qwen_models)}")
    print(f"通用API模型数量: {len(generic_models)}")
    
    print("\n💡 使用说明:")
    print("1. 在系统设置中配置真实的API Key")
    print("2. 点击'获取模型列表'按钮")
    print("3. 查看控制台输出的详细调试信息")
    print("4. 如果API调用失败，会显示预设的模型列表")

if __name__ == "__main__":
    main()
