#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据备份管理窗口
提供备份创建、恢复和管理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from datetime import datetime
import threading

class BackupWindow:
    def __init__(self, parent, backup_manager):
        """初始化备份管理窗口"""
        self.parent = parent
        self.backup_manager = backup_manager
        
        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("📦 数据备份管理")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_ui()
        
        # 加载备份列表
        self.refresh_backup_list()
        
    def center_window(self):
        """窗口居中显示"""
        self.window.update_idletasks()
        width = 800
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="📦 数据备份管理", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 备份列表标签页
        self.create_backup_list_tab(notebook)
        
        # 备份设置标签页
        self.create_backup_settings_tab(notebook)
        
        # 备份统计标签页
        self.create_backup_stats_tab(notebook)
        
        # 底部按钮
        self.create_bottom_buttons(main_frame)
        
    def create_backup_list_tab(self, notebook):
        """创建备份列表标签页"""
        list_frame = ttk.Frame(notebook)
        notebook.add(list_frame, text="📋 备份列表")
        
        # 工具栏
        toolbar = ttk.Frame(list_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # 创建备份按钮
        create_btn = ttk.Button(toolbar, text="📦 创建备份", command=self.create_backup)
        create_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        refresh_btn = ttk.Button(toolbar, text="🔄 刷新", command=self.refresh_backup_list)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 导入备份按钮
        import_btn = ttk.Button(toolbar, text="📥 导入备份", command=self.import_backup)
        import_btn.pack(side=tk.LEFT)
        
        # 备份列表
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建树形视图
        columns = ("name", "size", "type", "created", "actions")
        self.backup_tree = ttk.Treeview(list_container, columns=columns, show="headings", height=15)
        
        # 设置列标题
        self.backup_tree.heading("name", text="备份名称")
        self.backup_tree.heading("size", text="大小")
        self.backup_tree.heading("type", text="类型")
        self.backup_tree.heading("created", text="创建时间")
        self.backup_tree.heading("actions", text="操作")
        
        # 设置列宽
        self.backup_tree.column("name", width=200)
        self.backup_tree.column("size", width=100)
        self.backup_tree.column("type", width=80)
        self.backup_tree.column("created", width=150)
        self.backup_tree.column("actions", width=150)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.backup_tree.yview)
        self.backup_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.backup_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.backup_tree.bind("<Double-1>", self.on_backup_double_click)
        
        # 右键菜单
        self.create_context_menu()
        
    def create_backup_settings_tab(self, notebook):
        """创建备份设置标签页"""
        settings_frame = ttk.Frame(notebook)
        notebook.add(settings_frame, text="⚙️ 备份设置")
        
        # 自动备份设置
        auto_frame = ttk.LabelFrame(settings_frame, text="自动备份设置", padding=15)
        auto_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 启用自动备份
        self.auto_backup_var = tk.BooleanVar(value=True)
        auto_check = ttk.Checkbutton(auto_frame, text="启用自动备份", 
                                    variable=self.auto_backup_var,
                                    command=self.update_auto_backup_settings)
        auto_check.pack(anchor=tk.W, pady=(0, 10))
        
        # 备份间隔
        interval_frame = ttk.Frame(auto_frame)
        interval_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(interval_frame, text="备份间隔:").pack(side=tk.LEFT)
        self.interval_var = tk.StringVar(value="24")
        interval_spin = ttk.Spinbox(interval_frame, from_=1, to=168, width=10,
                                   textvariable=self.interval_var,
                                   command=self.update_auto_backup_settings)
        interval_spin.pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(interval_frame, text="小时").pack(side=tk.LEFT)
        
        # 最大备份数
        max_frame = ttk.Frame(auto_frame)
        max_frame.pack(fill=tk.X)
        
        ttk.Label(max_frame, text="最大备份数:").pack(side=tk.LEFT)
        self.max_backups_var = tk.StringVar(value="30")
        max_spin = ttk.Spinbox(max_frame, from_=5, to=100, width=10,
                              textvariable=self.max_backups_var,
                              command=self.update_auto_backup_settings)
        max_spin.pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(max_frame, text="个").pack(side=tk.LEFT)
        
        # 备份路径设置
        path_frame = ttk.LabelFrame(settings_frame, text="备份路径设置", padding=15)
        path_frame.pack(fill=tk.X, pady=(0, 15))
        
        path_entry_frame = ttk.Frame(path_frame)
        path_entry_frame.pack(fill=tk.X)
        
        ttk.Label(path_entry_frame, text="备份目录:").pack(side=tk.LEFT)
        self.backup_path_var = tk.StringVar(value=self.backup_manager.backup_dir)
        path_entry = ttk.Entry(path_entry_frame, textvariable=self.backup_path_var, width=40)
        path_entry.pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)
        
        browse_btn = ttk.Button(path_entry_frame, text="浏览", command=self.browse_backup_path)
        browse_btn.pack(side=tk.RIGHT)
        
    def create_backup_stats_tab(self, notebook):
        """创建备份统计标签页"""
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="📊 备份统计")
        
        # 统计信息框架
        info_frame = ttk.LabelFrame(stats_frame, text="备份统计信息", padding=15)
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建统计标签
        self.stats_labels = {}
        stats_items = [
            ("total_backups", "总备份数:"),
            ("auto_backups", "自动备份:"),
            ("manual_backups", "手动备份:"),
            ("total_size_mb", "总大小(MB):"),
            ("latest_backup", "最新备份:"),
            ("oldest_backup", "最旧备份:")
        ]
        
        for i, (key, label) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            frame = ttk.Frame(info_frame)
            frame.grid(row=row, column=col, sticky="w", padx=10, pady=5)
            
            ttk.Label(frame, text=label, font=("Microsoft YaHei", 10, "bold")).pack(side=tk.LEFT)
            self.stats_labels[key] = ttk.Label(frame, text="--", font=("Microsoft YaHei", 10))
            self.stats_labels[key].pack(side=tk.LEFT, padx=(10, 0))
            
        # 配置网格权重
        for i in range(2):
            info_frame.grid_columnconfigure(i, weight=1)
            
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="📋 查看详情", command=self.view_backup_details)
        self.context_menu.add_command(label="🔄 恢复备份", command=self.restore_backup)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📤 导出备份", command=self.export_backup)
        self.context_menu.add_command(label="🗑️ 删除备份", command=self.delete_backup)
        
        # 绑定右键事件
        self.backup_tree.bind("<Button-3>", self.show_context_menu)
        
    def create_bottom_buttons(self, parent):
        """创建底部按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 关闭按钮
        close_btn = ttk.Button(button_frame, text="关闭", command=self.window.destroy)
        close_btn.pack(side=tk.RIGHT)
        
        # 应用设置按钮
        apply_btn = ttk.Button(button_frame, text="应用设置", command=self.apply_settings)
        apply_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
    def refresh_backup_list(self):
        """刷新备份列表"""
        try:
            # 清空现有项目
            for item in self.backup_tree.get_children():
                self.backup_tree.delete(item)
                
            # 获取备份列表
            backups = self.backup_manager.get_backup_list()
            
            # 添加备份项目
            for backup in backups:
                created_time = backup["created_time"].strftime("%Y-%m-%d %H:%M:%S")
                self.backup_tree.insert("", tk.END, values=(
                    backup["filename"],
                    f"{backup['size_mb']} MB",
                    backup["backup_type"],
                    created_time,
                    "恢复 | 删除"
                ))
                
            # 更新统计信息
            self.update_stats()
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新备份列表失败：{str(e)}")
            
    def update_stats(self):
        """更新统计信息"""
        try:
            stats = self.backup_manager.get_backup_stats()
            
            for key, label_widget in self.stats_labels.items():
                value = stats.get(key, "--")
                if key in ["latest_backup", "oldest_backup"] and value:
                    if hasattr(value, 'strftime'):
                        value = value.strftime("%Y-%m-%d %H:%M:%S")
                label_widget.config(text=str(value))
                
        except Exception as e:
            print(f"更新统计信息失败: {e}")
            
    def create_backup(self):
        """创建新备份"""
        try:
            # 在后台线程中创建备份
            def backup_worker():
                try:
                    backup_path = self.backup_manager.create_backup()
                    self.window.after(0, lambda: self.on_backup_created(backup_path))
                except Exception as e:
                    self.window.after(0, lambda: messagebox.showerror("错误", f"创建备份失败：{str(e)}"))
                    
            # 显示进度对话框
            progress_window = self.show_progress_dialog("正在创建备份...")
            
            # 启动备份线程
            backup_thread = threading.Thread(target=backup_worker, daemon=True)
            backup_thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动备份失败：{str(e)}")
            
    def on_backup_created(self, backup_path):
        """备份创建完成回调"""
        messagebox.showinfo("成功", f"备份创建成功：\n{backup_path}")
        self.refresh_backup_list()
        
    def show_progress_dialog(self, message):
        """显示进度对话框"""
        progress_window = tk.Toplevel(self.window)
        progress_window.title("请稍候")
        progress_window.geometry("300x100")
        progress_window.transient(self.window)
        progress_window.grab_set()
        
        # 居中显示
        x = self.window.winfo_x() + 250
        y = self.window.winfo_y() + 250
        progress_window.geometry(f"300x100+{x}+{y}")
        
        ttk.Label(progress_window, text=message, font=("Microsoft YaHei", 12)).pack(pady=20)
        
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10)
        progress_bar.start()
        
        return progress_window
        
    def on_backup_double_click(self, event):
        """备份双击事件"""
        self.view_backup_details()
        
    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.backup_tree.identify_row(event.y)
        if item:
            self.backup_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
            
    def view_backup_details(self):
        """查看备份详情"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择一个备份")
            return
            
        # 这里可以实现备份详情查看功能
        messagebox.showinfo("备份详情", "备份详情功能待实现")
        
    def restore_backup(self):
        """恢复备份"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择一个备份")
            return
            
        # 确认恢复
        result = messagebox.askyesno("确认恢复", 
                                   "恢复备份将覆盖当前数据，确定要继续吗？\n\n"
                                   "建议在恢复前先创建当前数据的备份。")
        if not result:
            return
            
        # 获取选中的备份
        item = selection[0]
        backup_name = self.backup_tree.item(item)["values"][0]
        backup_path = os.path.join(self.backup_manager.backup_dir, backup_name)
        
        try:
            # 恢复备份
            success = self.backup_manager.restore_backup(backup_path)
            if success:
                messagebox.showinfo("成功", "备份恢复成功！\n请重启程序以应用更改。")
            else:
                messagebox.showerror("失败", "备份恢复失败！")
                
        except Exception as e:
            messagebox.showerror("错误", f"恢复备份失败：{str(e)}")
            
    def delete_backup(self):
        """删除备份"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择一个备份")
            return
            
        # 确认删除
        result = messagebox.askyesno("确认删除", "确定要删除选中的备份吗？")
        if not result:
            return
            
        # 获取选中的备份
        item = selection[0]
        backup_name = self.backup_tree.item(item)["values"][0]
        backup_path = os.path.join(self.backup_manager.backup_dir, backup_name)
        
        try:
            # 删除备份
            success = self.backup_manager.delete_backup(backup_path)
            if success:
                self.refresh_backup_list()
                messagebox.showinfo("成功", "备份删除成功！")
            else:
                messagebox.showerror("失败", "备份删除失败！")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除备份失败：{str(e)}")
            
    def export_backup(self):
        """导出备份"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择一个备份")
            return
            
        # 选择导出路径
        export_path = filedialog.asksaveasfilename(
            title="导出备份",
            defaultextension=".zip",
            filetypes=[("ZIP文件", "*.zip"), ("所有文件", "*.*")]
        )
        
        if export_path:
            try:
                # 获取选中的备份
                item = selection[0]
                backup_name = self.backup_tree.item(item)["values"][0]
                backup_path = os.path.join(self.backup_manager.backup_dir, backup_name)
                
                # 复制文件
                import shutil
                shutil.copy2(backup_path, export_path)
                
                messagebox.showinfo("成功", f"备份导出成功：\n{export_path}")
                
            except Exception as e:
                messagebox.showerror("错误", f"导出备份失败：{str(e)}")
                
    def import_backup(self):
        """导入备份"""
        file_path = filedialog.askopenfilename(
            title="导入备份",
            filetypes=[("ZIP文件", "*.zip"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 复制到备份目录
                import shutil
                filename = os.path.basename(file_path)
                dest_path = os.path.join(self.backup_manager.backup_dir, filename)
                
                shutil.copy2(file_path, dest_path)
                
                # 刷新列表
                self.refresh_backup_list()
                
                messagebox.showinfo("成功", "备份导入成功！")
                
            except Exception as e:
                messagebox.showerror("错误", f"导入备份失败：{str(e)}")
                
    def browse_backup_path(self):
        """浏览备份路径"""
        path = filedialog.askdirectory(title="选择备份目录")
        if path:
            self.backup_path_var.set(path)
            
    def update_auto_backup_settings(self):
        """更新自动备份设置"""
        try:
            enabled = self.auto_backup_var.get()
            interval = int(self.interval_var.get())
            
            self.backup_manager.set_auto_backup(enabled, interval)
            
        except Exception as e:
            print(f"更新自动备份设置失败: {e}")
            
    def apply_settings(self):
        """应用设置"""
        try:
            # 更新备份路径
            new_path = self.backup_path_var.get()
            if new_path != self.backup_manager.backup_dir:
                self.backup_manager.backup_dir = new_path
                os.makedirs(new_path, exist_ok=True)
                
            # 更新自动备份设置
            self.update_auto_backup_settings()
            
            # 更新最大备份数
            self.backup_manager.max_backups = int(self.max_backups_var.get())
            
            messagebox.showinfo("成功", "设置已应用！")
            
        except Exception as e:
            messagebox.showerror("错误", f"应用设置失败：{str(e)}")
