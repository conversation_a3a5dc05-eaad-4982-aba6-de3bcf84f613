#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清晰UI - 最简版本
"""

import sys
import os

# 设置高DPI支持
os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
os.environ['QT_SCALE_FACTOR'] = '1'

from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFrame)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class TestClearUI(QMainWindow):
    """测试清晰UI"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧠 AI考试生成系统 - 测试清晰版")
        self.setGeometry(200, 200, 800, 600)
        
        # 设置背景色
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            
            QLabel {
                color: #333;
                font-family: 'Microsoft YaHei';
            }
            
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 6px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                min-width: 100px;
            }
            
            QPushButton:hover {
                background-color: #1976D2;
            }
            
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("🧠 AI考试生成系统")
        title.setFont(QFont("Microsoft YaHei", 24, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: #2196F3; margin: 20px;")
        layout.addWidget(title)
        
        # 内容框
        content_frame = QFrame()
        layout.addWidget(content_frame)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(15)
        
        # 说明文字
        info_label = QLabel("""
        ✅ 这是清晰版AI考试生成系统界面测试
        
        🎯 特点：
        • 无模糊效果
        • 高DPI优化
        • 清晰字体渲染
        • 简洁现代设计
        
        📝 功能：
        • 智能试卷生成
        • 多种题型支持
        • 难度自动调节
        • 实时预览功能
        """)
        info_label.setFont(QFont("Microsoft YaHei", 12))
        info_label.setStyleSheet("line-height: 1.6; padding: 20px;")
        content_layout.addWidget(info_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        buttons = [
            ("🚀 开始生成", self.start_generate),
            ("📊 查看统计", self.show_stats),
            ("⚙️ 系统设置", self.show_settings),
            ("❌ 退出程序", self.close)
        ]
        
        for text, callback in buttons:
            btn = QPushButton(text)
            btn.clicked.connect(callback)
            button_layout.addWidget(btn)
        
        content_layout.addLayout(button_layout)
        
        # 状态栏
        status_label = QLabel("✅ 系统就绪 - 界面清晰度测试通过")
        status_label.setFont(QFont("Microsoft YaHei", 10))
        status_label.setStyleSheet("color: #4CAF50; padding: 10px; background: #e8f5e8; border-radius: 4px;")
        layout.addWidget(status_label)
        
    def start_generate(self):
        """开始生成"""
        print("🚀 开始生成考试")
        
    def show_stats(self):
        """显示统计"""
        print("📊 显示统计信息")
        
    def show_settings(self):
        """显示设置"""
        print("⚙️ 显示系统设置")

def main():
    """主函数"""
    # 设置高DPI支持
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = TestClearUI()
    window.show()
    
    print("🎨 清晰UI测试界面已启动")
    print("📱 请检查界面是否清晰，无模糊现象")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
