#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全启动脚本 - 处理线程问题和异常情况
"""

import sys
import os
import signal
import atexit
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def signal_handler(signum, frame):
    """信号处理器 - 优雅退出"""
    print("\n👋 接收到退出信号，正在安全关闭...")
    sys.exit(0)

def cleanup():
    """清理函数"""
    print("🧹 清理资源...")

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup)
    
    print("🚀 安全启动考试系统...")
    print("=" * 50)
    
    startup_time = time.time()
    
    try:
        print("⚡ 启动性能优化...")
        from src.utils.performance_optimizer import get_performance_optimizer
        optimizer = get_performance_optimizer()
        optimizer.optimize_startup()
        
        print("⚙️ 初始化配置...")
        from src.utils.config_manager import ConfigManager
        config = ConfigManager()
        
        print("💾 初始化数据库...")
        from src.utils.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        db_manager.init_database()
        
        print("📦 初始化备份系统...")
        from src.utils.backup_manager import BackupManager
        backup_manager = BackupManager(db_manager.db_path)
        
        print("🖥️ 创建主界面...")
        import tkinter as tk
        root = tk.Tk()
        
        print("🎨 初始化主题系统...")
        from src.ui.theme_manager import get_theme_manager
        theme_manager = get_theme_manager()
        theme_manager.apply_theme()
        
        print("🧠 初始化智能功能...")
        from src.ui.main_window import MainWindow
        app = MainWindow(root, config, db_manager)
        
        # 集成优化功能
        app.performance_optimizer = optimizer
        app.backup_manager = backup_manager
        app.theme_manager = theme_manager
        
        # 初始化智能功能（安全模式）
        try:
            app.initialize_intelligent_features()
            print("✅ 智能功能初始化完成")
        except Exception as e:
            print(f"⚠️ 智能功能初始化失败: {e}")
            print("💡 系统将以基础模式运行")
        
        # 记录启动时间
        total_startup_time = time.time() - startup_time
        print(f"✅ 系统启动完成！耗时: {total_startup_time:.2f}秒")
        
        print("\n🎉 欢迎使用智能化考试系统！")
        print("💡 新功能提示：")
        print("   📊 学习仪表板: 菜单栏 → 智能功能 → 学习仪表板")
        print("   🤖 智能助手: 菜单栏 → 智能功能 → 智能助手")
        print("   🎯 智能出题: 菜单栏 → 智能功能 → 智能出题")
        print("   ⌨️ 快捷键: 按 F1 查看所有快捷键")
        print("\n💡 按 Ctrl+C 可以安全退出程序")
        print("=" * 50)
        
        # 启动事件循环
        try:
            root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 用户中断，正在安全退出...")
        except Exception as e:
            print(f"\n❌ 运行时错误: {e}")
        finally:
            # 确保窗口被销毁
            try:
                root.quit()
                root.destroy()
            except:
                pass
                
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查文件路径是否正确")
        print("2. 确保所有必要的模块都存在")
        print("3. 尝试重新安装依赖包")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        print("\n详细错误信息:")
        import traceback
        traceback.print_exc()
    finally:
        print("\n👋 程序已安全退出")

if __name__ == "__main__":
    main()
