#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Doubao API客户端
支持豆包（字节跳动）的OpenAI兼容API
"""

import requests
import json
import time
from typing import List, Dict, Any
from src.api.base_ai_client import BaseAIClient

class DoubaoClient(BaseAIClient):
    """Doubao API客户端"""

    def __init__(self, api_key: str, endpoint: str = "https://ark.cn-beijing.volces.com/api/v3", model: str = "doubao-pro-4k"):
        """初始化Doubao客户端"""
        super().__init__(api_key)
        self.endpoint = endpoint.rstrip('/')
        self.model = model
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

    def set_model(self, model: str):
        """设置使用的模型"""
        self.model = model

    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        # 豆包模型实际上是用户创建的推理接入点ID
        # 这里提供一些常见的示例，用户需要根据自己的接入点ID进行配置
        return [
            "doubao-seed-1-6-250615",  # 示例推理接入点ID
            "doubao-pro-4k",           # 通用模型名（可能不可用）
            "doubao-pro-32k",
            "doubao-pro-128k",
            "doubao-lite-4k",
            "doubao-lite-32k"
        ]
    
    def generate_questions(self, material: str, question_types: List[str],
                          num_questions: int = 20) -> List[Dict[str, Any]]:
        """使用Doubao生成题目"""
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                prompt = self.create_prompt_for_questions(material, question_types, num_questions)

                # 构建请求数据（使用OpenAI兼容格式）
                data = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "你是一个专业的教育工作者，擅长根据学习材料生成高质量的考试题目。请严格按照要求的JSON格式返回题目。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": 0.7,
                    "max_tokens": 4000
                }

                # 发送请求到豆包API，增加超时时间
                response = requests.post(
                    f"{self.endpoint}/chat/completions",
                    headers=self.headers,
                    json=data,
                    timeout=(10, 120)  # 连接超时10秒，读取超时120秒
                )
            
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']

                    # 解析题目
                    questions = self.parse_questions_response(content)
                    return self.validate_questions(questions)
                else:
                    error_msg = f"API请求失败: {response.status_code}"
                    try:
                        error_detail = response.json()
                        if 'error' in error_detail:
                            error_msg += f" - {error_detail['error'].get('message', '')}"
                    except:
                        error_msg += f" - {response.text}"

                    if attempt < max_retries - 1:
                        print(f"豆包API请求失败，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue
                    else:
                        raise Exception(error_msg)

            except requests.exceptions.Timeout as e:
                if attempt < max_retries - 1:
                    print(f"豆包API连接超时，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"豆包API连接超时，请检查网络连接或稍后重试: {str(e)}")
            except requests.exceptions.ConnectionError as e:
                if attempt < max_retries - 1:
                    print(f"豆包API连接错误，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"豆包API连接失败，请检查网络连接: {str(e)}")
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"豆包API请求出错，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"Doubao生成题目失败: {str(e)}")

        raise Exception("Doubao生成题目失败: 超过最大重试次数")
    
    def evaluate_answer(self, question: str, answer: str, correct_answer: str) -> Dict[str, Any]:
        """使用Doubao评估主观题答案"""
        try:
            prompt = f"""
请评估以下主观题的答案：

题目：{question}

参考答案：{correct_answer}

学生答案：{answer}

请从以下几个方面进行评估：
1. 内容准确性（是否回答了问题的核心）
2. 完整性（是否涵盖了主要要点）
3. 逻辑性（答案是否有条理）
4. 表达清晰度

请给出0-10分的评分，并提供具体的反馈意见。

请按照以下JSON格式返回：
{{
    "score": 实际得分,
    "max_score": 10,
    "feedback": "详细的反馈意见，包括优点和需要改进的地方"
}}
"""
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的教师，擅长评估学生的答案并给出建设性的反馈。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }

            response = requests.post(
                f"{self.endpoint}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                try:
                    evaluation = json.loads(content)
                    return evaluation
                except json.JSONDecodeError:
                    return {
                        "score": 5,
                        "max_score": 10,
                        "feedback": "评估失败，请人工评分"
                    }
            else:
                raise Exception(f"API请求失败: {response.status_code}")
                
        except Exception as e:
            return {
                "score": 5,
                "max_score": 10,
                "feedback": f"评估失败: {str(e)}"
            }
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello"
                    }
                ],
                "max_tokens": 10
            }

            response = requests.post(
                f"{self.endpoint}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=(5, 30)  # 连接超时5秒，读取超时30秒
            )

            if response.status_code == 200:
                return True
            else:
                print(f"豆包API测试失败: {response.status_code} - {response.text[:100]}")
                return False

        except requests.exceptions.Timeout:
            print("豆包API连接超时")
            return False
        except requests.exceptions.ConnectionError:
            print("豆包API连接失败，请检查网络")
            return False
        except Exception as e:
            print(f"豆包API测试出错: {str(e)}")
            return False

    def get_models_from_api(self) -> List[str]:
        """从API获取可用模型列表"""
        try:
            # 豆包API可能不支持模型列表接口，返回预定义列表
            return self.get_available_models()
        except:
            return self.get_available_models()
