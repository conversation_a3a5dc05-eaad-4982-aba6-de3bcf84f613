#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试系统工具启动器
"""

import os
import sys
import subprocess

def show_menu():
    """显示菜单"""2
    print("=" * 50)
    print("        考试系统工具启动器")
    print("=" * 50)
    print("1. 启动考试系统")
    print("2. 快速插入测试考试")
    print("3. 检查数据库内容")
    print("4. 创建测试数据")
    print("5. 测试豆包API")
    print("6. 网络诊断")
    print("0. 退出")
    print("=" * 50)

def run_script(script_name):
    """运行脚本"""
    try:
        print(f"\n正在运行 {script_name}...")
        print("-" * 40)
        
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True)
        
        print("-" * 40)
        if result.returncode == 0:
            print(f"✅ {script_name} 运行完成")
        else:
            print(f"❌ {script_name} 运行失败")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 运行 {script_name} 时出错: {e}")
        return False

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-6): ").strip()
            
            if choice == "0":
                print("再见！")
                break
            elif choice == "1":
                print("\n启动考试系统...")
                run_script("main.py")
            elif choice == "2":
                print("\n快速插入测试考试...")
                success = run_script("quick_insert_exam.py")
                if success:
                    print("\n✅ 测试考试插入成功！")
                    print("现在可以启动考试系统查看考试列表了。")
            elif choice == "3":
                print("\n检查数据库内容...")
                run_script("check_database.py")
            elif choice == "4":
                print("\n创建测试数据...")
                run_script("create_test_data.py")
            elif choice == "5":
                print("\n测试豆包API...")
                run_script("test_doubao_correct_model.py")
            elif choice == "6":
                print("\n网络诊断...")
                run_script("src/utils/network_test.py")
            else:
                print("❌ 无效选择，请输入 0-6")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()
