#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试错题功能修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wrong_question_manager():
    """测试错题管理器"""
    try:
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database import Database
        
        print("=== 测试错题管理器 ===")
        
        db = Database()
        wq_manager = WrongQuestionManager(db)
        
        # 测试添加错题
        print("1. 测试添加错题...")
        question_id = wq_manager.add_wrong_question(
            question_text="测试错题：Python中哪个关键字用于定义函数？",
            question_type="single_choice",
            correct_answer="B",
            user_answer="A",
            explanation="Python中使用def关键字来定义函数，而不是function。"
        )
        print(f"✅ 错题添加成功，ID: {question_id}")
        
        # 测试获取统计信息
        print("\n2. 测试统计信息...")
        stats = wq_manager.get_wrong_question_stats()
        print(f"✅ 统计信息获取成功:")
        print(f"   总错题数: {stats['total_count']}")
        print(f"   收藏题目: {stats['favorite_count']}")
        print(f"   最近7天: {stats['recent_count']}")
        
        # 测试获取所有错题
        print("\n3. 测试获取错题列表...")
        wrong_questions = wq_manager.get_all_wrong_questions()
        print(f"✅ 获取到 {len(wrong_questions)} 道错题")
        
        if wrong_questions:
            print("最新错题:")
            latest = wrong_questions[0]
            print(f"   ID: {latest[0]}")
            print(f"   题目: {latest[1][:50]}...")
            print(f"   类型: {latest[2]}")
            print(f"   正确答案: {latest[3]}")
            print(f"   我的答案: {latest[4]}")
            print(f"   解析: {latest[5][:50]}...")
        
        # 测试收藏功能
        if wrong_questions:
            print("\n4. 测试收藏功能...")
            test_id = wrong_questions[0][0]
            new_status = wq_manager.toggle_favorite(test_id)
            print(f"✅ 收藏状态切换成功: {new_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错题管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wrong_question_data_structure():
    """测试错题数据结构"""
    try:
        print("\n=== 测试错题数据结构 ===")
        
        # 模拟错题数据
        sample_question = [
            1,  # id
            "测试题目：Python是什么类型的编程语言？",  # question_text
            "single_choice",  # question_type
            "B",  # correct_answer
            "A",  # user_answer
            "Python是一种解释型编程语言，不是编译型语言。",  # explanation
            0,  # is_favorite
            "2024-01-15 10:30:00"  # created_at
        ]
        
        print("错题数据结构:")
        print(f"  ID: {sample_question[0]}")
        print(f"  题目: {sample_question[1]}")
        print(f"  类型: {sample_question[2]}")
        print(f"  正确答案: {sample_question[3]}")
        print(f"  我的答案: {sample_question[4]}")
        print(f"  解析: {sample_question[5]}")
        print(f"  收藏状态: {'是' if sample_question[6] else '否'}")
        print(f"  创建时间: {sample_question[7]}")
        
        # 测试解析窗口数据处理
        print("\n解析窗口数据处理:")
        
        def get_question_type_name(question_type):
            type_names = {
                'single_choice': '单选题',
                'multiple_choice': '多选题',
                'true_false': '判断题',
                'short_answer': '简答题',
                'case_analysis': '案例分析题'
            }
            return type_names.get(question_type, '未知题型')
        
        type_name = get_question_type_name(sample_question[2])
        print(f"  题目类型名称: {type_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        return False

def test_exam_result_data():
    """测试考试结果数据"""
    try:
        print("\n=== 测试考试结果数据 ===")
        
        # 模拟考试记录数据
        sample_record = {
            'id': 1,
            'exam_id': 1,
            'answers': {'0': 'A', '1': 'B', '2': '对'},
            'score': 2.0,
            'total_score': 3.0,
            'start_time': '2024-01-15 10:00:00',
            'end_time': '2024-01-15 10:30:00',
            'created_at': '2024-01-15 10:30:00',
            'exam_title': '测试考试',
            'questions': [
                {
                    'type': 'single_choice',
                    'question': '测试题目1',
                    'options': ['选项A', '选项B', '选项C', '选项D'],
                    'correct_answer': 'B',
                    'explanation': '这是测试解析1',
                    'score': 1
                },
                {
                    'type': 'single_choice',
                    'question': '测试题目2',
                    'options': ['选项A', '选项B', '选项C', '选项D'],
                    'correct_answer': 'B',
                    'explanation': '这是测试解析2',
                    'score': 1
                },
                {
                    'type': 'true_false',
                    'question': '测试判断题',
                    'correct_answer': '对',
                    'explanation': '这是判断题解析',
                    'score': 1
                }
            ]
        }
        
        print("考试记录数据结构:")
        print(f"  考试ID: {sample_record['exam_id']}")
        print(f"  考试标题: {sample_record['exam_title']}")
        print(f"  题目数量: {len(sample_record['questions'])}")
        print(f"  答案数量: {len(sample_record['answers'])}")
        print(f"  得分: {sample_record['score']}/{sample_record['total_score']}")
        
        # 测试解析窗口数据获取
        print("\n解析窗口数据获取测试:")
        for i, question in enumerate(sample_record['questions']):
            user_answer = sample_record['answers'].get(str(i), '未作答')
            print(f"  题目{i+1}: {question['question'][:30]}...")
            print(f"    我的答案: {user_answer}")
            print(f"    正确答案: {question['correct_answer']}")
            print(f"    解析: {question.get('explanation', '暂无解析')[:30]}...")
            
            # 检查选项
            if question['type'] in ['single_choice', 'multiple_choice']:
                options = question.get('options', [])
                print(f"    选项数量: {len(options)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 考试结果数据测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始测试错题功能修复...\n")
    
    # 测试错题管理器
    manager_ok = test_wrong_question_manager()
    
    # 测试数据结构
    structure_ok = test_wrong_question_data_structure()
    
    # 测试考试结果数据
    result_ok = test_exam_result_data()
    
    print("\n=== 修复验证总结 ===")
    
    results = [
        ("错题管理器", manager_ok),
        ("数据结构", structure_ok),
        ("考试结果", result_ok)
    ]
    
    all_passed = True
    for name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 错题功能修复完成！")
        print("\n现在您可以：")
        print("1. 启动考试系统")
        print("2. 完成一次考试（故意答错几题）")
        print("3. 在考试结果中点击'查看解析'")
        print("4. 点击'添加错题到错题本'")
        print("5. 在主界面点击'错题本'查看错题")
        print("6. 双击错题或点击'查看解析'查看详细解析")
        
        print("\n✨ 新增功能：")
        print("• 错题本界面增加了交互按钮")
        print("• 支持查看错题解析")
        print("• 支持切换收藏状态")
        print("• 支持删除错题")
        print("• 双击错题直接查看解析")
        print("• 完整的错题统计信息")
    else:
        print("\n⚠️ 部分功能仍有问题，请检查错误信息")

if __name__ == "__main__":
    main()
