# 专业API配置界面使用说明

## 🎯 新功能介绍

我已经为您创建了一个专业的API配置界面，类似于您展示的第二张图片的风格。这个界面提供了更加直观和专业的API配置体验。

## 🚀 如何使用

### 1. 打开专业配置界面

1. 启动考试系统
2. 打开**系统设置** → **API设置** → **中转API**
3. 点击**"专业配置"**按钮（新增功能）
4. 专业配置窗口将会打开

### 2. 界面布局

**左侧面板 - API选择**
- 🤖 OpenAI
- 🔄 中转API  
- 🧠 DeepSeek
- 🌟 通义千问

**右侧面板 - 配置详情**
- API Key输入（带密码保护）
- Base URL配置
- 模型选择下拉框
- 自定义模型输入
- 连接状态指示器

**底部面板 - 全局选项**
- 自动连接复选框
- 查看隐藏API密钥按钮

### 3. 配置步骤

#### 步骤1: 选择API类型
- 点击左侧的API类型按钮
- 选中的API会高亮显示
- 右侧会显示对应的配置表单

#### 步骤2: 填写配置信息
- **API Key**: 输入您的API密钥（自动隐藏显示）
- **Base URL**: 输入API的基础URL
- **可用模型**: 从下拉列表选择模型

#### 步骤3: 添加自定义模型
- 在"输入模型名"框中输入模型名称
- 点击**"添加"**按钮
- 模型会自动添加到下拉列表

#### 步骤4: 测试连接
- 点击**"🔗 连接"**按钮测试连接
- 查看右上角的连接状态指示器
- 绿色●表示连接成功，红色●表示连接失败

### 4. 高级功能

#### 连接状态监控
- 实时显示连接状态
- 颜色编码：
  - 🔴 红色：未连接/连接失败
  - 🟡 黄色：连接中
  - 🟢 绿色：已连接

#### 操作按钮
- **🔗 连接**: 测试API连接
- **📋 附加参数**: 配置高级参数（开发中）
- **📤 发送测试消息**: 发送测试消息（开发中）

#### 安全功能
- API Key自动隐藏显示
- 点击"查看隐藏的API密钥"可切换显示/隐藏

## 🎨 界面特色

### 深色主题
- 专业的深色配色方案
- 护眼的界面设计
- 现代化的视觉效果

### 直观操作
- 左右分栏布局
- 图标化的API类型显示
- 实时状态反馈

### 响应式设计
- 自适应窗口大小
- 滚动支持
- 流畅的交互体验

## 🔧 针对您的中转API配置

### 预设配置
系统已经为您的中转API预设了配置：
- **名称**: 中转API 🔄
- **Base URL**: `http://14.103.135.19:3000/v1`
- **默认模型**: `gemini-2.5-flash`

### 使用建议

1. **首次使用**:
   - 选择"中转API"
   - 输入您的API Key
   - 确认Base URL正确
   - 点击"连接"测试

2. **模型配置**:
   - 如果默认模型不可用，尝试添加其他模型
   - 常用模型名称：`gpt-3.5-turbo`, `gpt-4`, `claude-3-haiku`

3. **故障排除**:
   - 查看连接状态指示器
   - 如果连接失败，返回原设置界面使用"网络诊断"功能

## 🚀 立即体验

现在就打开系统设置，点击"专业配置"按钮，体验全新的专业API配置界面！

### 快速操作流程
1. 系统设置 → API设置 → 中转API
2. 点击"专业配置"
3. 选择"中转API"
4. 输入API Key
5. 点击"连接"测试
6. 开始使用！

这个专业界面将让您的API配置体验更加直观和高效！🎉
