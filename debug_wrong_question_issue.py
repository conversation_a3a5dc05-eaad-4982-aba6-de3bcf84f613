#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试错题添加问题
"""

import sys
import os
import sqlite3
from datetime import datetime
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_database_structure():
    """检查数据库结构"""
    print("=== 检查数据库结构 ===")
    
    db_path = "data/exam_system.db"
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
        
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查错题表结构
        cursor.execute("PRAGMA table_info(wrong_questions)")
        columns = cursor.fetchall()
        
        print("错题表结构:")
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
            
        # 检查是否有新字段
        column_names = [col[1] for col in columns]
        required_fields = ['exam_id', 'exam_title', 'exam_record_id']
        
        for field in required_fields:
            if field in column_names:
                print(f"✅ 字段 {field} 存在")
            else:
                print(f"❌ 字段 {field} 不存在")
                
        # 检查错题数量
        cursor.execute("SELECT COUNT(*) FROM wrong_questions")
        count = cursor.fetchone()[0]
        print(f"当前错题数量: {count}")
        
        # 检查最近的错题
        cursor.execute("SELECT * FROM wrong_questions ORDER BY created_at DESC LIMIT 3")
        recent_questions = cursor.fetchall()
        
        if recent_questions:
            print("最近的错题:")
            for i, q in enumerate(recent_questions, 1):
                print(f"  {i}. ID:{q[0]} - {q[1][:50]}...")
        else:
            print("没有错题记录")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
        return False
    finally:
        conn.close()

def check_exam_records():
    """检查考试记录"""
    print("\n=== 检查考试记录 ===")
    
    db_path = "data/exam_system.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查考试记录数量
        cursor.execute("SELECT COUNT(*) FROM exam_records")
        count = cursor.fetchone()[0]
        print(f"考试记录数量: {count}")
        
        # 检查最近的考试记录
        cursor.execute("""
            SELECT er.id, er.exam_id, er.score, er.total_score, er.created_at, e.title
            FROM exam_records er
            LEFT JOIN exams e ON er.exam_id = e.id
            ORDER BY er.created_at DESC LIMIT 3
        """)
        
        recent_records = cursor.fetchall()
        
        if recent_records:
            print("最近的考试记录:")
            for i, record in enumerate(recent_records, 1):
                print(f"  {i}. 记录ID:{record[0]} 考试ID:{record[1]} 分数:{record[2]}/{record[3]} 试卷:{record[5]} 时间:{record[4]}")
                
                # 检查这个记录的答案
                cursor.execute("SELECT answers FROM exam_records WHERE id = ?", (record[0],))
                answers_json = cursor.fetchone()[0]
                try:
                    answers = json.loads(answers_json)
                    print(f"    答案数量: {len(answers)}")
                    print(f"    答案内容: {answers}")
                except:
                    print(f"    答案解析失败: {answers_json}")
        else:
            print("没有考试记录")
            
        return recent_records
        
    except Exception as e:
        print(f"❌ 检查考试记录失败: {e}")
        return []
    finally:
        conn.close()

def test_wrong_question_add():
    """测试错题添加功能"""
    print("\n=== 测试错题添加功能 ===")
    
    try:
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database_manager import DatabaseManager
        
        db = DatabaseManager()
        wq_manager = WrongQuestionManager(db)
        
        # 测试直接添加错题
        print("1. 测试直接添加错题...")
        question_id = wq_manager.add_wrong_question(
            question_text="调试测试错题：1+1等于多少？",
            question_type="single_choice",
            correct_answer="2",
            user_answer="3",
            explanation="1+1=2，这是基本的数学运算。",
            exam_id=1,
            exam_title="调试测试试卷",
            exam_record_id=1
        )
        
        print(f"✅ 直接添加错题成功，ID: {question_id}")
        
        # 验证添加结果
        questions = wq_manager.get_all_wrong_questions()
        print(f"当前错题总数: {len(questions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试错题添加失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_add_with_mock_data():
    """使用模拟数据测试批量添加"""
    print("\n=== 测试批量添加错题 ===")
    
    try:
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database_manager import DatabaseManager
        
        db = DatabaseManager()
        wq_manager = WrongQuestionManager(db)
        
        # 创建模拟考试记录
        mock_record = {
            'id': 999,
            'exam_id': 999,
            'exam_title': '调试测试试卷',
            'questions': [
                {
                    'type': 'single_choice',
                    'question': '调试题目1：Python是什么？',
                    'options': ['动物', '编程语言', '食物', '植物'],
                    'correct_answer': 'B',
                    'explanation': 'Python是一种编程语言',
                    'score': 1
                },
                {
                    'type': 'true_false',
                    'question': '调试题目2：Python是开源的',
                    'correct_answer': '对',
                    'explanation': 'Python确实是开源的',
                    'score': 1
                }
            ],
            'answers': {
                '0': 'A',  # 错误答案
                '1': '错'   # 错误答案
            }
        }
        
        print("使用模拟数据测试批量添加...")
        print(f"模拟记录包含 {len(mock_record['questions'])} 道题目")
        print(f"模拟答案: {mock_record['answers']}")
        
        added_count = wq_manager.batch_add_wrong_questions_from_exam(mock_record)
        print(f"✅ 批量添加完成，添加了 {added_count} 道错题")
        
        # 验证结果
        questions = wq_manager.get_all_wrong_questions()
        print(f"当前错题总数: {len(questions)}")
        
        if questions:
            print("最新错题:")
            for i, q in enumerate(questions[:2], 1):
                print(f"  {i}. {q[1][:50]}...")
                print(f"     正确答案: {q[3]}, 我的答案: {q[4]}")
                if len(q) > 9:
                    print(f"     来源试卷: {q[9]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量添加测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_recent_exam_and_add_wrong_questions():
    """检查最近的考试记录并尝试添加错题"""
    print("\n=== 检查最近考试并添加错题 ===")
    
    try:
        from src.core.exam_manager import ExamManager
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database_manager import DatabaseManager
        
        db = DatabaseManager()
        exam_manager = ExamManager(db)
        wq_manager = WrongQuestionManager(db)
        
        # 获取最近的考试记录
        all_records = exam_manager.get_exam_records()
        
        if not all_records:
            print("没有找到考试记录")
            return False
            
        print(f"找到 {len(all_records)} 条考试记录")
        
        # 取最新的记录
        latest_record_data = all_records[0]
        record_id = latest_record_data[0]
        
        print(f"最新考试记录ID: {record_id}")
        
        # 获取完整的考试记录
        record = exam_manager.get_exam_record_by_id(record_id)
        
        if record:
            print("考试记录详情:")
            print(f"  记录ID: {record['id']}")
            print(f"  考试ID: {record['exam_id']}")
            print(f"  试卷标题: {record['exam_title']}")
            print(f"  分数: {record['score']}/{record['total_score']}")
            print(f"  题目数量: {len(record['questions'])}")
            print(f"  答案数量: {len(record['answers'])}")
            print(f"  答案内容: {record['answers']}")
            
            # 尝试添加错题
            print("\n尝试从这个记录添加错题...")
            added_count = wq_manager.batch_add_wrong_questions_from_exam(record)
            print(f"✅ 添加了 {added_count} 道错题")
            
            return True
        else:
            print("无法获取考试记录详情")
            return False
            
    except Exception as e:
        print(f"❌ 检查最近考试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 开始调试错题添加问题...")
    
    # 1. 检查数据库结构
    if not check_database_structure():
        return
    
    # 2. 检查考试记录
    exam_records = check_exam_records()
    
    # 3. 测试错题添加功能
    test_wrong_question_add()
    
    # 4. 测试批量添加
    test_batch_add_with_mock_data()
    
    # 5. 检查最近的考试记录并尝试添加错题
    if exam_records:
        check_recent_exam_and_add_wrong_questions()
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
