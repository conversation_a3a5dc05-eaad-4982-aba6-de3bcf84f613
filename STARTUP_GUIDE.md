# 🚀 智能化考试系统启动指南

## 🎯 快速启动（推荐方法）

### 方法1: 使用启动菜单 (最简单)
```bash
# Windows用户
双击 run_menu.bat

# 或命令行
run_menu.bat
```

### 方法2: 稳定版启动 (推荐)
```bash
python main_stable.py
```

### 方法3: 安全模式启动
```bash
python run_safe.py
```

## 🔧 遇到问题？

### ⚡ 快速解决方案

#### 如果遇到 "ModuleNotFoundError: No module named 'PyQt6'"
```bash
# 方案1: 快速修复工具（推荐）
python quick_fix.py

# 方案2: 直接安装PyQt6
pip install PyQt6

# 方案3: 使用依赖安装工具
python install_dependencies.py

# 方案4: 双击批处理文件
install_dependencies.bat
```

### 第一步：运行诊断工具
```bash
python diagnose_and_fix.py
```
这个工具会：
- 检查Python版本和依赖
- 检查关键文件是否完整
- 诊断常见问题
- 提供具体的解决方案

### 第二步：根据问题选择启动方式

#### 🧵 如果遇到线程问题 (RuntimeError: main thread is not in main loop)
**推荐解决方案：**
```bash
# 方案1: 使用稳定版（无启动画面）
python main_stable.py

# 方案2: 使用安全模式
python run_safe.py

# 方案3: 使用启动菜单选择
run_menu.bat
```

#### 📦 如果缺少依赖包
```bash
# 安装基础依赖
pip install requests

# 安装可选依赖（完整功能）
pip install numpy matplotlib pandas

# 安装现代UI依赖
pip install PyQt6
```

#### 📁 如果文件缺失
1. 检查是否在正确的目录中
2. 重新下载完整的项目文件
3. 确保没有文件被意外删除

## 🎨 不同启动方式对比

| 启动方式 | 特点 | 适用场景 | 稳定性 |
|---------|------|----------|--------|
| `main_stable.py` | 无启动画面，快速启动 | 日常使用，避免线程问题 | ⭐⭐⭐⭐⭐ |
| `run_safe.py` | 异常处理，信号捕获 | 调试和问题排查 | ⭐⭐⭐⭐ |
| `main_optimized.py` | 完整功能，启动画面 | 演示和完整体验 | ⭐⭐⭐ |
| `main.py` | 标准版本 | 基础功能使用 | ⭐⭐⭐⭐ |
| `demo_modern_ui.py` | 现代UI演示 | 体验新界面 | ⭐⭐⭐ |

## 🎯 功能对比

### 基础功能 (所有版本都支持)
- ✅ 材料管理和导入
- ✅ 试卷生成和考试
- ✅ 成绩统计和分析
- ✅ 错题管理和复习
- ✅ 数据备份和恢复

### 智能化功能 (需要完整版本)
- 🧠 学习数据分析
- 🤖 智能助手对话
- 🎯 个性化出题
- 📊 数据可视化仪表板
- 💡 学习建议和推荐

### 现代化界面 (需要PyQt6)
- 🎨 灵动风格UI
- 💫 涟漪动效按钮
- 🌈 毛玻璃背景
- 📱 响应式布局

## 🚨 常见问题解决

### Q1: 程序启动后立即崩溃
**解决方案：**
```bash
# 1. 运行诊断工具
python diagnose_and_fix.py

# 2. 使用稳定版启动
python main_stable.py

# 3. 检查Python版本
python --version  # 需要3.7+
```

### Q2: 出现线程错误 (RuntimeError)
**解决方案：**
```bash
# 使用无启动画面的稳定版
python main_stable.py

# 或使用安全模式
python run_safe.py
```

### Q3: 智能功能不可用
**解决方案：**
```bash
# 安装可选依赖
pip install numpy matplotlib pandas

# 检查文件完整性
python diagnose_and_fix.py
```

### Q4: 现代UI无法启动
**解决方案：**
```bash
# 安装PyQt6
pip install PyQt6

# 启动现代UI演示
python demo_modern_ui.py
```

### Q5: 数据库错误
**解决方案：**
```bash
# 删除损坏的数据库文件
del exam_system.db

# 重新启动程序，会自动创建新数据库
python main_stable.py
```

## 🎉 推荐使用流程

### 新用户首次使用
1. **运行诊断**: `python diagnose_and_fix.py`
2. **安装依赖**: 根据诊断结果安装缺失的包
3. **稳定启动**: `python main_stable.py`
4. **体验功能**: 熟悉基础功能后尝试智能功能

### 日常使用
1. **快速启动**: `python main_stable.py`
2. **或使用菜单**: 双击 `run_menu.bat`

### 演示和展示
1. **现代UI**: `python demo_modern_ui.py`
2. **完整功能**: `python main_optimized.py`

### 问题排查
1. **诊断工具**: `python diagnose_and_fix.py`
2. **安全模式**: `python run_safe.py`

## 📞 获取帮助

如果仍然遇到问题：

1. **查看诊断报告**: 运行 `python diagnose_and_fix.py`
2. **检查错误日志**: 查看控制台输出的错误信息
3. **尝试不同启动方式**: 使用启动菜单测试各种方式
4. **重新安装依赖**: `pip install --upgrade -r requirements.txt`

## 🎊 享受使用！

选择适合您的启动方式，开始使用智能化考试系统吧！

- 🏠 **日常使用**: `python main_stable.py`
- 🎨 **体验新界面**: `python demo_modern_ui.py`
- 🔧 **遇到问题**: `python diagnose_and_fix.py`
- 📋 **选择启动**: 双击 `run_menu.bat`

祝您使用愉快！🚀
