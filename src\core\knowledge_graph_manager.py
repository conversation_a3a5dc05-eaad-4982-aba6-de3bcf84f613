#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱管理器
负责知识点的管理和关系构建
"""

import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime
import json
import re
from typing import List, Dict, Tuple, Set

class KnowledgeGraphManager:
    def __init__(self, db_manager):
        """初始化知识图谱管理器"""
        self.db = db_manager
        self.graph = nx.Graph()
        
        # 设置中文字体
        self.setup_chinese_font()
    
    def setup_chinese_font(self):
        """设置中文字体"""
        try:
            # 尝试使用系统中文字体
            font_list = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            for font_name in font_list:
                try:
                    plt.rcParams['font.sans-serif'] = [font_name]
                    plt.rcParams['axes.unicode_minus'] = False
                    break
                except:
                    continue
        except:
            pass
    
    def add_knowledge_point(self, name: str, description: str = "", parent_id: int = None):
        """添加知识点"""
        try:
            query = """
                INSERT INTO knowledge_points (name, description, parent_id, created_at)
                VALUES (?, ?, ?, ?)
            """
            params = (name, description, parent_id, datetime.now())
            
            point_id = self.db.execute_update(query, params)
            return point_id
            
        except Exception as e:
            raise Exception(f"添加知识点失败: {str(e)}")
    
    def get_all_knowledge_points(self):
        """获取所有知识点"""
        query = """
            SELECT id, name, description, parent_id, created_at
            FROM knowledge_points 
            ORDER BY name
        """
        return self.db.execute_query(query)
    
    def get_knowledge_point_by_id(self, point_id: int):
        """根据ID获取知识点"""
        query = "SELECT * FROM knowledge_points WHERE id = ?"
        result = self.db.execute_query(query, (point_id,))
        return result[0] if result else None
    
    def update_knowledge_point(self, point_id: int, name: str, description: str, parent_id: int = None):
        """更新知识点"""
        query = """
            UPDATE knowledge_points 
            SET name = ?, description = ?, parent_id = ?
            WHERE id = ?
        """
        self.db.execute_update(query, (name, description, parent_id, point_id))
    
    def delete_knowledge_point(self, point_id: int):
        """删除知识点"""
        # 先删除子节点的父节点引用
        self.db.execute_update("UPDATE knowledge_points SET parent_id = NULL WHERE parent_id = ?", (point_id,))
        # 删除知识点
        self.db.execute_update("DELETE FROM knowledge_points WHERE id = ?", (point_id,))
    
    def extract_knowledge_from_material(self, material_content: str) -> List[str]:
        """从材料中提取知识点"""
        # 简单的关键词提取算法
        # 实际应用中可以使用更复杂的NLP技术
        
        # 分句
        sentences = re.split(r'[。！？\n]', material_content)
        
        # 提取可能的知识点（名词短语）
        knowledge_points = set()
        
        # 简单的模式匹配
        patterns = [
            r'([A-Za-z\u4e00-\u9fa5]{2,10})[的是]',  # "XXX是"、"XXX的"
            r'([A-Za-z\u4e00-\u9fa5]{2,10})概念',    # "XXX概念"
            r'([A-Za-z\u4e00-\u9fa5]{2,10})原理',    # "XXX原理"
            r'([A-Za-z\u4e00-\u9fa5]{2,10})方法',    # "XXX方法"
            r'([A-Za-z\u4e00-\u9fa5]{2,10})理论',    # "XXX理论"
        ]
        
        for sentence in sentences:
            for pattern in patterns:
                matches = re.findall(pattern, sentence)
                for match in matches:
                    if len(match) >= 2:
                        knowledge_points.add(match)
        
        return list(knowledge_points)[:20]  # 限制数量
    
    def build_graph_from_database(self):
        """从数据库构建知识图谱"""
        self.graph.clear()
        
        # 获取所有知识点
        points = self.get_all_knowledge_points()
        
        # 添加节点
        for point in points:
            self.graph.add_node(point[0], name=point[1], description=point[2])
        
        # 添加边（父子关系）
        for point in points:
            if point[3]:  # 有父节点
                self.graph.add_edge(point[3], point[0])
    
    def build_graph_from_materials(self):
        """从学习材料构建知识图谱"""
        from src.core.material_manager import MaterialManager
        
        material_manager = MaterialManager(self.db)
        materials = material_manager.get_all_materials()
        
        all_knowledge_points = set()
        
        # 从所有材料中提取知识点
        for material in materials:
            material_data = material_manager.get_material_by_id(material[0])
            if material_data:
                points = self.extract_knowledge_from_material(material_data[2])
                all_knowledge_points.update(points)
        
        # 构建图谱
        self.graph.clear()
        
        # 添加节点
        for i, point in enumerate(all_knowledge_points):
            self.graph.add_node(i, name=point, description="")
        
        # 简单的关系推断（基于共现）
        points_list = list(all_knowledge_points)
        for i in range(len(points_list)):
            for j in range(i + 1, len(points_list)):
                # 如果两个知识点在名称上有相似性，添加边
                if self.calculate_similarity(points_list[i], points_list[j]) > 0.3:
                    self.graph.add_edge(i, j)
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        # 简单的字符级相似度计算
        set1 = set(text1)
        set2 = set(text2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0
    
    def get_graph_statistics(self) -> Dict:
        """获取图谱统计信息"""
        if not self.graph.nodes():
            return {
                'nodes': 0,
                'edges': 0,
                'density': 0,
                'components': 0
            }
        
        return {
            'nodes': self.graph.number_of_nodes(),
            'edges': self.graph.number_of_edges(),
            'density': nx.density(self.graph),
            'components': nx.number_connected_components(self.graph)
        }
    
    def find_shortest_path(self, start_node: int, end_node: int) -> List[int]:
        """查找两个知识点之间的最短路径"""
        try:
            return nx.shortest_path(self.graph, start_node, end_node)
        except nx.NetworkXNoPath:
            return []
    
    def get_node_neighbors(self, node_id: int) -> List[int]:
        """获取节点的邻居"""
        if node_id in self.graph:
            return list(self.graph.neighbors(node_id))
        return []
    
    def get_central_nodes(self, top_k: int = 10) -> List[Tuple[int, float]]:
        """获取中心性最高的节点"""
        if not self.graph.nodes():
            return []
        
        centrality = nx.degree_centrality(self.graph)
        sorted_nodes = sorted(centrality.items(), key=lambda x: x[1], reverse=True)
        
        return sorted_nodes[:top_k]
    
    def export_graph_data(self) -> Dict:
        """导出图谱数据"""
        nodes = []
        edges = []
        
        # 导出节点
        for node_id in self.graph.nodes():
            node_data = self.graph.nodes[node_id]
            nodes.append({
                'id': node_id,
                'name': node_data.get('name', f'Node {node_id}'),
                'description': node_data.get('description', '')
            })
        
        # 导出边
        for edge in self.graph.edges():
            edges.append({
                'source': edge[0],
                'target': edge[1]
            })
        
        return {
            'nodes': nodes,
            'edges': edges,
            'statistics': self.get_graph_statistics()
        }
    
    def save_graph_to_file(self, filename: str):
        """保存图谱到文件"""
        graph_data = self.export_graph_data()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, ensure_ascii=False, indent=2)
    
    def load_graph_from_file(self, filename: str):
        """从文件加载图谱"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                graph_data = json.load(f)
            
            self.graph.clear()
            
            # 添加节点
            for node in graph_data['nodes']:
                self.graph.add_node(node['id'], 
                                  name=node['name'], 
                                  description=node['description'])
            
            # 添加边
            for edge in graph_data['edges']:
                self.graph.add_edge(edge['source'], edge['target'])
            
            return True
            
        except Exception as e:
            print(f"加载图谱失败: {e}")
            return False
