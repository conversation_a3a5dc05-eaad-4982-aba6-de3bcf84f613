#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试系统主程序 - 稳定版本
作者：AI Assistant
版本：2.0 - 稳定版（修复线程问题）
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数 - 稳定启动流程"""
    startup_time = time.time()
    
    try:
        print("🚀 启动考试系统 v2.0 (稳定版)...")
        
        # 第一步：性能优化（在GUI之前）
        print("⚡ 启动性能优化...")
        from src.utils.performance_optimizer import get_performance_optimizer
        optimizer = get_performance_optimizer()
        optimizer.optimize_startup()
        
        # 第二步：初始化配置管理器
        print("⚙️ 初始化配置...")
        from src.utils.config_manager import ConfigManager
        config = ConfigManager()
        
        # 第三步：初始化数据库
        print("💾 初始化数据库...")
        from src.utils.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        db_manager.init_database()
        
        # 优化数据库性能
        if hasattr(db_manager, 'connection'):
            optimizer.optimize_database_queries(db_manager.connection)
        
        # 第四步：初始化备份管理器
        print("📦 初始化备份系统...")
        from src.utils.backup_manager import BackupManager
        backup_manager = BackupManager(db_manager.db_path)
        
        # 第五步：创建主窗口
        print("🖥️ 创建主界面...")
        root = tk.Tk()
        
        # 初始化主题系统
        print("🎨 初始化主题系统...")
        from src.ui.theme_manager import get_theme_manager
        theme_manager = get_theme_manager()
        theme_manager.apply_theme()
        
        # 第六步：创建主应用（不使用启动画面）
        print("🧠 初始化智能功能...")
        from src.ui.main_window import MainWindow
        app = MainWindow(root, config, db_manager)
        
        # 集成优化功能
        app.performance_optimizer = optimizer
        app.backup_manager = backup_manager
        app.theme_manager = theme_manager
        
        # 初始化智能功能
        try:
            app.initialize_intelligent_features()
            print("✅ 智能功能初始化完成")
        except Exception as e:
            print(f"⚠️ 智能功能初始化失败: {e}")
            print("💡 系统将以基础模式运行")
        
        # 记录启动时间
        total_startup_time = time.time() - startup_time
        optimizer.performance_stats["startup_time"] = total_startup_time
        
        print(f"✅ 系统启动完成！耗时: {total_startup_time:.2f}秒")
        print("🎉 欢迎使用智能化考试系统！")
        print()
        print("💡 新功能提示：")
        print("   📊 学习仪表板: 菜单栏 → 智能功能 → 学习仪表板")
        print("   🤖 智能助手: 菜单栏 → 智能功能 → 智能助手")
        print("   🎯 智能出题: 菜单栏 → 智能功能 → 智能出题")
        print("   ⌨️ 快捷键: 按 F1 查看所有快捷键")
        print()
        
        # 启动事件循环
        root.mainloop()
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        messagebox.showerror("启动错误", f"程序启动失败：{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
