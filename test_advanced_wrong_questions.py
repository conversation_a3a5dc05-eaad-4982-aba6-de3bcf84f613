#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级错题本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from src.utils.database_manager import DatabaseManager
from src.core.wrong_question_manager import WrongQuestionManager
from src.core.exam_manager import ExamManager
from src.ui.advanced_wrong_questions_window import AdvancedWrongQuestionsWindow

def test_advanced_wrong_questions():
    """测试高级错题本功能"""
    print("🧪 开始测试高级错题本功能...")
    
    try:
        # 初始化数据库和管理器
        db_manager = DatabaseManager()
        wrong_question_manager = WrongQuestionManager(db_manager)
        exam_manager = ExamManager(db_manager)
        
        print("✅ 数据库和管理器初始化成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("✅ 主窗口创建成功")
        
        # 创建高级错题本窗口
        advanced_window = AdvancedWrongQuestionsWindow(root, wrong_question_manager, exam_manager)
        
        print("✅ 高级错题本窗口创建成功")
        
        # 显示窗口
        advanced_window.show()
        
        print("✅ 高级错题本窗口显示成功")
        print("🎉 测试完成！请在弹出的窗口中体验高级错题本功能")
        
        # 启动事件循环
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_advanced_wrong_questions()
