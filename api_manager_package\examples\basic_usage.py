#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.universal_api_manager import UniversalAPIManager

def main():
    print("🚀 API管理系统基础示例")
    print("=" * 50)
    
    # 创建管理器
    api_manager = UniversalAPIManager()
    
    # 显示默认提供商
    print("\n📋 默认API提供商:")
    for name, provider in api_manager.providers.items():
        print(f"  • {provider.display_name} ({name})")
        print(f"    URL: {provider.base_url}")
        print(f"    类型: {provider.api_type}")
        print(f"    状态: {provider.status}")
        print()
    
    print("✅ 示例完成")

if __name__ == "__main__":
    main()
