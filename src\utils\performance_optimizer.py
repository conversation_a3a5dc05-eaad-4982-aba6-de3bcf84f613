#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化器
提供启动优化、响应速度优化和内存管理功能
"""

import gc
import threading
import time
import os
import sys
from typing import Dict, List, Callable
from functools import wraps
import sqlite3

# 尝试导入psutil，如果不可用则使用替代方案
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ psutil不可用，使用基础内存监控")

class PerformanceOptimizer:
    def __init__(self):
        """初始化性能优化器"""
        self.cache = {}
        self.cache_max_size = 1000
        self.cache_ttl = 300  # 5分钟缓存过期
        self.monitoring_enabled = True
        self.performance_stats = {
            "startup_time": 0,
            "memory_usage": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "db_queries": 0,
            "ui_updates": 0
        }
        
        # 启动性能监控
        self.start_monitoring()
        
    def optimize_startup(self):
        """优化启动性能"""
        try:
            # 预编译正则表达式
            self.precompile_patterns()
            
            # 预加载常用模块
            self.preload_modules()
            
            # 优化垃圾回收
            self.optimize_gc()
            
            # 预热数据库连接
            self.warmup_database()
            
            print("✅ 启动优化完成")
            
        except Exception as e:
            print(f"❌ 启动优化失败: {e}")
            
    def precompile_patterns(self):
        """预编译正则表达式"""
        import re
        
        # 常用的正则表达式模式
        patterns = [
            r'\d+',  # 数字
            r'[a-zA-Z]+',  # 字母
            r'\w+@\w+\.\w+',  # 邮箱
            r'^\s*$',  # 空行
            r'[^\w\s]'  # 特殊字符
        ]
        
        self.compiled_patterns = {}
        for pattern in patterns:
            self.compiled_patterns[pattern] = re.compile(pattern)
            
    def preload_modules(self):
        """预加载常用模块"""
        try:
            # 预加载常用的标准库模块
            import json
            import datetime
            import sqlite3
            import threading
            import queue
            
            # 预加载项目模块（延迟导入）
            threading.Thread(target=self._preload_project_modules, daemon=True).start()
            
        except Exception as e:
            print(f"❌ 预加载模块失败: {e}")
            
    def _preload_project_modules(self):
        """后台预加载项目模块"""
        try:
            time.sleep(1)  # 等待主程序启动
            
            # 预加载UI模块
            from src.ui import theme_manager
            from src.ui import shortcut_manager
            
            # 预加载核心模块
            from src.core import exam_manager
            from src.core import wrong_question_manager
            
        except Exception as e:
            print(f"❌ 后台预加载失败: {e}")
            
    def optimize_gc(self):
        """优化垃圾回收"""
        try:
            # 设置垃圾回收阈值
            gc.set_threshold(700, 10, 10)
            
            # 启用垃圾回收调试（开发模式）
            if __debug__:
                gc.set_debug(gc.DEBUG_STATS)
                
            # 立即执行一次垃圾回收
            gc.collect()
            
        except Exception as e:
            print(f"❌ 垃圾回收优化失败: {e}")
            
    def warmup_database(self):
        """预热数据库连接"""
        try:
            # 这里可以添加数据库预热逻辑
            # 例如：执行一些简单查询来建立连接池
            pass
        except Exception as e:
            print(f"❌ 数据库预热失败: {e}")
            
    def cache_decorator(self, ttl: int = 300):
        """缓存装饰器"""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = f"{func.__name__}_{hash(str(args) + str(kwargs))}"
                
                # 检查缓存
                if cache_key in self.cache:
                    cache_data = self.cache[cache_key]
                    if time.time() - cache_data["timestamp"] < ttl:
                        self.performance_stats["cache_hits"] += 1
                        return cache_data["value"]
                    else:
                        # 缓存过期，删除
                        del self.cache[cache_key]
                
                # 缓存未命中，执行函数
                self.performance_stats["cache_misses"] += 1
                result = func(*args, **kwargs)
                
                # 存储到缓存
                if len(self.cache) < self.cache_max_size:
                    self.cache[cache_key] = {
                        "value": result,
                        "timestamp": time.time()
                    }
                    
                return result
            return wrapper
        return decorator
        
    def clear_cache(self):
        """清理缓存"""
        self.cache.clear()
        gc.collect()
        print("✅ 缓存已清理")
        
    def optimize_ui_updates(self, root):
        """优化UI更新性能"""
        try:
            # 批量UI更新
            self.pending_updates = []
            self.update_timer = None
            
            def batch_update():
                """批量执行UI更新"""
                if self.pending_updates:
                    for update_func in self.pending_updates:
                        try:
                            update_func()
                        except Exception as e:
                            print(f"UI更新错误: {e}")
                    
                    self.pending_updates.clear()
                    self.performance_stats["ui_updates"] += 1
                    
                self.update_timer = None
                
            def schedule_update(update_func):
                """调度UI更新"""
                self.pending_updates.append(update_func)
                
                if self.update_timer is None:
                    self.update_timer = root.after(16, batch_update)  # 60 FPS
                    
            return schedule_update
            
        except Exception as e:
            print(f"❌ UI优化失败: {e}")
            return lambda x: x()
            
    def monitor_memory(self):
        """监控内存使用"""
        try:
            if PSUTIL_AVAILABLE:
                import os
                process = psutil.Process(os.getpid())
                memory_info = process.memory_info()
                self.performance_stats["memory_usage"] = memory_info.rss / 1024 / 1024  # MB
            else:
                # 使用基础内存监控
                try:
                    import resource
                    memory_usage = resource.getrusage(resource.RUSAGE_SELF).ru_maxrss
                    # 在Windows上，ru_maxrss以字节为单位；在Unix上以KB为单位
                    if sys.platform == "win32":
                        self.performance_stats["memory_usage"] = memory_usage / 1024 / 1024  # MB
                    else:
                        self.performance_stats["memory_usage"] = memory_usage / 1024  # MB
                except ImportError:
                    # Windows上resource模块不可用，使用替代方案
                    try:
                        # 使用Windows特定的内存获取方法
                        if sys.platform == "win32":
                            import ctypes
                            from ctypes import wintypes

                            # 获取进程内存信息
                            kernel32 = ctypes.windll.kernel32
                            process_handle = kernel32.GetCurrentProcess()

                            class PROCESS_MEMORY_COUNTERS(ctypes.Structure):
                                _fields_ = [
                                    ("cb", wintypes.DWORD),
                                    ("PageFaultCount", wintypes.DWORD),
                                    ("PeakWorkingSetSize", ctypes.c_size_t),
                                    ("WorkingSetSize", ctypes.c_size_t),
                                    ("QuotaPeakPagedPoolUsage", ctypes.c_size_t),
                                    ("QuotaPagedPoolUsage", ctypes.c_size_t),
                                    ("QuotaPeakNonPagedPoolUsage", ctypes.c_size_t),
                                    ("QuotaNonPagedPoolUsage", ctypes.c_size_t),
                                    ("PagefileUsage", ctypes.c_size_t),
                                    ("PeakPagefileUsage", ctypes.c_size_t),
                                ]

                            pmc = PROCESS_MEMORY_COUNTERS()
                            pmc.cb = ctypes.sizeof(PROCESS_MEMORY_COUNTERS)

                            psapi = ctypes.windll.psapi
                            if psapi.GetProcessMemoryInfo(process_handle, ctypes.byref(pmc), pmc.cb):
                                self.performance_stats["memory_usage"] = pmc.WorkingSetSize / 1024 / 1024  # MB
                            else:
                                raise Exception("无法获取内存信息")
                        else:
                            # 非Windows系统的估算值
                            self.performance_stats["memory_usage"] = len(self.cache) * 0.001  # 估算值
                    except Exception:
                        # 最后的备用方案：使用估算值
                        self.performance_stats["memory_usage"] = max(50, len(self.cache) * 0.001)  # 最小50MB估算值

            # 如果内存使用过高，触发垃圾回收
            if self.performance_stats["memory_usage"] > 500:  # 500MB
                gc.collect()
                print("🧹 触发内存清理")

        except Exception:
            # 设置默认值，不打印错误信息
            self.performance_stats["memory_usage"] = 100
            
    def start_monitoring(self):
        """启动性能监控"""
        def monitoring_worker():
            while self.monitoring_enabled:
                try:
                    self.monitor_memory()
                    self.cleanup_expired_cache()
                    time.sleep(30)  # 每30秒监控一次
                except Exception as e:
                    print(f"❌ 性能监控错误: {e}")
                    time.sleep(30)
                    
        if self.monitoring_enabled:
            monitor_thread = threading.Thread(target=monitoring_worker, daemon=True)
            monitor_thread.start()
            
    def cleanup_expired_cache(self):
        """清理过期缓存"""
        try:
            current_time = time.time()
            expired_keys = []
            
            for key, data in self.cache.items():
                if current_time - data["timestamp"] > self.cache_ttl:
                    expired_keys.append(key)
                    
            for key in expired_keys:
                del self.cache[key]
                
            if expired_keys:
                print(f"🧹 清理了 {len(expired_keys)} 个过期缓存项")
                
        except Exception as e:
            print(f"❌ 缓存清理失败: {e}")
            
    def optimize_database_queries(self, db_connection):
        """优化数据库查询"""
        try:
            # 启用WAL模式
            db_connection.execute("PRAGMA journal_mode=WAL")
            
            # 设置缓存大小
            db_connection.execute("PRAGMA cache_size=10000")
            
            # 启用外键约束
            db_connection.execute("PRAGMA foreign_keys=ON")
            
            # 设置同步模式
            db_connection.execute("PRAGMA synchronous=NORMAL")
            
            # 设置临时存储
            db_connection.execute("PRAGMA temp_store=MEMORY")
            
            print("✅ 数据库优化完成")
            
        except Exception as e:
            print(f"❌ 数据库优化失败: {e}")
            
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        try:
            # 更新内存使用
            self.monitor_memory()
            
            # 计算缓存命中率
            total_cache_requests = self.performance_stats["cache_hits"] + self.performance_stats["cache_misses"]
            cache_hit_rate = (self.performance_stats["cache_hits"] / total_cache_requests * 100) if total_cache_requests > 0 else 0
            
            stats = self.performance_stats.copy()
            stats["cache_hit_rate"] = round(cache_hit_rate, 2)
            stats["cache_size"] = len(self.cache)
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取性能统计失败: {e}")
            return {}
            
    def optimize_for_low_memory(self):
        """低内存模式优化"""
        try:
            # 减少缓存大小
            self.cache_max_size = 100
            self.cache_ttl = 60  # 1分钟过期
            
            # 清理当前缓存
            self.clear_cache()
            
            # 更频繁的垃圾回收
            gc.set_threshold(100, 5, 5)
            
            # 立即执行垃圾回收
            gc.collect()
            
            print("✅ 低内存模式已启用")
            
        except Exception as e:
            print(f"❌ 低内存优化失败: {e}")
            
    def create_async_executor(self, max_workers: int = 4):
        """创建异步执行器"""
        import concurrent.futures
        
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        
        def async_execute(func, *args, **kwargs):
            """异步执行函数"""
            return self.executor.submit(func, *args, **kwargs)
            
        return async_execute
        
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring_enabled = False
        
        # 关闭执行器
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
            
    def __del__(self):
        """析构函数"""
        self.stop_monitoring()

# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()

def get_performance_optimizer():
    """获取全局性能优化器"""
    return performance_optimizer
