#!/bin/bash

echo "启动考试系统..."
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python3，请先安装Python 3.7或更高版本"
    exit 1
fi

# 检查依赖是否安装
echo "检查依赖包..."
if ! python3 -c "import requests" &> /dev/null; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误：依赖包安装失败"
        exit 1
    fi
fi

# 启动程序
echo "启动考试系统..."
python3 main.py

if [ $? -ne 0 ]; then
    echo "程序运行出错"
    read -p "按任意键继续..."
fi
