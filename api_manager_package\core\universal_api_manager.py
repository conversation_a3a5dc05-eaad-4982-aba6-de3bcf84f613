#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用API管理器 - 从<PERSON><PERSON>考试系统提取
支持多种API提供商和自动模型检测
"""

import requests
import json
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class APIProvider:
    """API提供商配置"""
    name: str
    display_name: str
    base_url: str
    api_key: str
    api_type: str  # openai, claude, gemini, custom
    headers: Dict[str, str]
    models: List[str]
    test_model: str  # 用于测试的默认模型
    active: bool = True
    last_tested: Optional[str] = None
    status: str = "未测试"  # 未测试, 正常, 异常
    error_message: str = ""

class UniversalAPIManager:
    """通用API管理器"""
    
    def __init__(self):
        self.providers: Dict[str, APIProvider] = {}
        self.load_default_providers()
    
    def load_default_providers(self):
        """加载默认API提供商"""
        
        # OpenAI官方
        self.add_provider(APIProvider(
            name="openai_official",
            display_name="OpenAI官方",
            base_url="https://api.openai.com/v1",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=[],
            test_model="gpt-3.5-turbo"
        ))
        
        # DeepSeek
        self.add_provider(APIProvider(
            name="deepseek",
            display_name="DeepSeek",
            base_url="https://api.deepseek.com/v1",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=[],
            test_model="deepseek-chat"
        ))
        
        # 豆包API
        self.add_provider(APIProvider(
            name="doubao",
            display_name="豆包API",
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=["doubao-seed-1-6-flash-250615"],
            test_model="doubao-seed-1-6-flash-250615"
        ))
    
    def add_provider(self, provider: APIProvider):
        """添加API提供商"""
        self.providers[provider.name] = provider
    
    def add_custom_provider(self, name: str, display_name: str, base_url: str, 
                           api_key: str, api_type: str = "openai", test_model: str = ""):
        """添加自定义API提供商"""
        
        headers = {"Content-Type": "application/json"}
        if api_key and api_type == "openai":
            headers["Authorization"] = f"Bearer {api_key}"
        
        provider = APIProvider(
            name=name,
            display_name=display_name,
            base_url=base_url.rstrip('/'),
            api_key=api_key,
            api_type=api_type,
            headers=headers,
            models=[],
            test_model=test_model or "gpt-3.5-turbo"
        )
        
        self.add_provider(provider)
        return provider
    
    def test_provider_connection(self, name: str) -> bool:
        """测试API提供商连接"""
        if name not in self.providers:
            return False
        
        provider = self.providers[name]
        
        try:
            return self._test_openai_connection(provider)
        except Exception as e:
            provider.status = "异常"
            provider.error_message = str(e)
            provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return False
    
    def _test_openai_connection(self, provider: APIProvider) -> bool:
        """测试OpenAI兼容API连接"""
        
        headers = provider.headers.copy()
        if provider.api_key:
            headers["Authorization"] = f"Bearer {provider.api_key}"
        
        # 测试聊天接口
        chat_url = f"{provider.base_url}/chat/completions"
        
        test_data = {
            "model": provider.test_model,
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        try:
            response = requests.post(chat_url, headers=headers, json=test_data, timeout=30)
            
            if response.status_code == 200:
                provider.status = "正常"
                provider.error_message = ""
                provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                return True
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                provider.status = "异常"
                provider.error_message = error_msg
                provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                return False
                
        except Exception as e:
            provider.status = "异常"
            provider.error_message = str(e)
            provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return False
    
    def save_config(self, config_path: str):
        """保存配置到文件"""
        config_data = {}
        for name, provider in self.providers.items():
            config_data[name] = {
                'display_name': provider.display_name,
                'base_url': provider.base_url,
                'api_key': provider.api_key,
                'api_type': provider.api_type,
                'test_model': provider.test_model,
                'active': provider.active,
                'models': provider.models
            }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    def load_config(self, config_path: str):
        """从文件加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            for name, config in config_data.items():
                if name in self.providers:
                    # 更新现有提供商
                    provider = self.providers[name]
                    for key, value in config.items():
                        if hasattr(provider, key):
                            setattr(provider, key, value)
                            
        except FileNotFoundError:
            print("配置文件不存在，使用默认配置")
        except Exception as e:
            print(f"加载配置失败: {e}")
