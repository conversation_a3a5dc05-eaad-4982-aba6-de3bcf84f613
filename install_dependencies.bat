@echo off
chcp 65001 >nul
title 依赖包安装工具

echo.
echo ========================================
echo 🔧 智能化考试系统 - 依赖包安装工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ 检测到Python版本：
python --version
echo.

echo 🚀 启动依赖包安装工具...
echo.

python install_dependencies.py

if errorlevel 1 (
    echo.
    echo ❌ 安装工具运行出错
    echo.
    echo 🔧 手动安装命令：
    echo.
    echo 基础依赖：
    echo pip install requests
    echo.
    echo 智能功能依赖：
    echo pip install numpy matplotlib pandas
    echo.
    echo 现代UI依赖：
    echo pip install PyQt6
    echo.
    echo 一键安装所有：
    echo pip install requests numpy matplotlib pandas PyQt6
    echo.
    pause
) else (
    echo.
    echo ✅ 依赖包安装完成！
    echo.
    echo 🚀 现在可以启动系统了：
    echo • 双击 run_menu.bat 选择启动方式
    echo • 或运行 python main_stable.py
    echo.
)

echo 👋 感谢使用！
pause
