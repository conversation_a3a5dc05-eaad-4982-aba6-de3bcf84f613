#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
材料管理器
负责学习材料的导入、存储和管理
"""

import os
import PyPDF2
from datetime import datetime
import tkinter as tk
from tkinter import filedialog, messagebox

class MaterialManager:
    def __init__(self, db_manager):
        """初始化材料管理器"""
        self.db = db_manager
    
    def import_text_file(self, file_path):
        """导入文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            title = os.path.basename(file_path)
            return self.save_material(title, content, 'text')
            
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    content = file.read()
                title = os.path.basename(file_path)
                return self.save_material(title, content, 'text')
            except Exception as e:
                raise Exception(f"无法读取文件编码: {str(e)}")
        except Exception as e:
            raise Exception(f"导入文本文件失败: {str(e)}")
    
    def import_pdf_file(self, file_path):
        """导入PDF文件"""
        try:
            content = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    content += page.extract_text() + "\n"
            
            title = os.path.basename(file_path)
            return self.save_material(title, content, 'pdf')
            
        except Exception as e:
            raise Exception(f"导入PDF文件失败: {str(e)}")
    
    def save_material(self, title, content, file_type):
        """保存材料到数据库"""
        try:
            query = """
                INSERT INTO materials (title, content, file_type, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """
            now = datetime.now()
            params = (title, content, file_type, now, now)
            
            material_id = self.db.execute_update(query, params)
            return material_id
            
        except Exception as e:
            raise Exception(f"保存材料失败: {str(e)}")
    
    def get_all_materials(self):
        """获取所有材料"""
        query = "SELECT id, title, file_type, created_at FROM materials ORDER BY created_at DESC"
        return self.db.execute_query(query)
    
    def get_material_by_id(self, material_id):
        """根据ID获取材料"""
        query = "SELECT * FROM materials WHERE id = ?"
        result = self.db.execute_query(query, (material_id,))
        return result[0] if result else None
    
    def delete_material(self, material_id):
        """删除材料"""
        query = "DELETE FROM materials WHERE id = ?"
        self.db.execute_update(query, (material_id,))
    
    def update_material(self, material_id, title, content):
        """更新材料"""
        query = """
            UPDATE materials 
            SET title = ?, content = ?, updated_at = ?
            WHERE id = ?
        """
        now = datetime.now()
        self.db.execute_update(query, (title, content, now, material_id))
    
    def search_materials(self, keyword):
        """搜索材料"""
        query = """
            SELECT id, title, file_type, created_at 
            FROM materials 
            WHERE title LIKE ? OR content LIKE ?
            ORDER BY created_at DESC
        """
        search_term = f"%{keyword}%"
        return self.db.execute_query(query, (search_term, search_term))
    
    def get_material_stats(self):
        """获取材料统计信息"""
        stats = {}
        
        # 总材料数
        query = "SELECT COUNT(*) FROM materials"
        result = self.db.execute_query(query)
        stats['total_count'] = result[0][0] if result else 0
        
        # 按类型统计
        query = "SELECT file_type, COUNT(*) FROM materials GROUP BY file_type"
        result = self.db.execute_query(query)
        stats['by_type'] = {row[0]: row[1] for row in result}
        
        return stats
