#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错题本管理器
负责错题的收集、管理和复习
"""

from datetime import datetime
from typing import List, Dict

class WrongQuestionManager:
    def __init__(self, db_manager):
        """初始化错题本管理器"""
        self.db = db_manager
    
    def add_wrong_question(self, question_text: str, question_type: str,
                          correct_answer: str, user_answer: str, explanation: str = "",
                          exam_id: int = None, exam_title: str = None, exam_record_id: int = None,
                          question_number: int = None):
        """添加错题"""
        try:
            print(f"🔍 准备添加错题: {question_text[:30]}...")
            print(f"  参数: exam_id={exam_id}, exam_title={exam_title}, exam_record_id={exam_record_id}, question_number={question_number}")

            # 先尝试检查表结构
            try:
                # 检查表是否有新字段（包括question_number）
                test_query = "SELECT exam_id, exam_title, exam_record_id, question_number FROM wrong_questions LIMIT 1"
                self.db.execute_query(test_query)
                print("✅ 表结构包含所有新字段")

                # 使用完整的插入语句
                query = """
                    INSERT INTO wrong_questions (question_text, question_type, correct_answer,
                                               user_answer, explanation, exam_id, exam_title,
                                               exam_record_id, question_number, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (question_text, question_type, correct_answer, user_answer,
                         explanation, exam_id, exam_title, exam_record_id, question_number, datetime.now())

            except Exception as e:
                print(f"⚠️ 表结构可能缺少question_number字段，尝试不包含该字段: {e}")

                try:
                    # 尝试不包含question_number的插入
                    test_query = "SELECT exam_id, exam_title, exam_record_id FROM wrong_questions LIMIT 1"
                    self.db.execute_query(test_query)
                    print("✅ 表结构包含基本新字段")

                    query = """
                        INSERT INTO wrong_questions (question_text, question_type, correct_answer,
                                                   user_answer, explanation, exam_id, exam_title,
                                                   exam_record_id, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (question_text, question_type, correct_answer, user_answer,
                             explanation, exam_id, exam_title, exam_record_id, datetime.now())

                except Exception as e2:
                    print(f"⚠️ 表结构缺少新字段，使用兼容模式: {e2}")

                    # 使用兼容的插入语句（不包含新字段）
                    query = """
                        INSERT INTO wrong_questions (question_text, question_type, correct_answer,
                                                   user_answer, explanation, created_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """
                    params = (question_text, question_type, correct_answer, user_answer,
                             explanation, datetime.now())

            print(f"🚀 执行插入操作...")
            question_id = self.db.execute_update(query, params)
            print(f"✅ 错题添加成功，ID: {question_id}")
            return question_id

        except Exception as e:
            print(f"❌ 添加错题失败: {str(e)}")
            raise Exception(f"添加错题失败: {str(e)}")
    
    def get_all_wrong_questions(self):
        """获取所有错题"""
        query = """
            SELECT id, question_text, question_type, correct_answer, user_answer,
                   explanation, is_favorite, created_at, exam_id, exam_title, exam_record_id
            FROM wrong_questions
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query)
    
    def get_wrong_question_by_id(self, question_id: int):
        """根据ID获取错题"""
        query = "SELECT * FROM wrong_questions WHERE id = ?"
        result = self.db.execute_query(query, (question_id,))
        return result[0] if result else None
    
    def get_favorite_questions(self):
        """获取收藏的题目"""
        query = """
            SELECT id, question_text, question_type, correct_answer, user_answer, 
                   explanation, is_favorite, created_at
            FROM wrong_questions 
            WHERE is_favorite = 1
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query)
    
    def toggle_favorite(self, question_id: int):
        """切换收藏状态"""
        # 先获取当前状态
        current = self.get_wrong_question_by_id(question_id)
        if current:
            new_status = not bool(current[6])  # is_favorite字段
            query = "UPDATE wrong_questions SET is_favorite = ? WHERE id = ?"
            self.db.execute_update(query, (new_status, question_id))
            return new_status
        return False
    
    def delete_wrong_question(self, question_id: int):
        """删除错题"""
        query = "DELETE FROM wrong_questions WHERE id = ?"
        self.db.execute_update(query, (question_id,))
    
    def search_wrong_questions(self, keyword: str):
        """搜索错题"""
        query = """
            SELECT id, question_text, question_type, correct_answer, user_answer, 
                   explanation, is_favorite, created_at
            FROM wrong_questions 
            WHERE question_text LIKE ? OR explanation LIKE ?
            ORDER BY created_at DESC
        """
        search_term = f"%{keyword}%"
        return self.db.execute_query(query, (search_term, search_term))
    
    def get_questions_by_type(self, question_type: str):
        """根据题目类型获取错题"""
        query = """
            SELECT id, question_text, question_type, correct_answer, user_answer, 
                   explanation, is_favorite, created_at
            FROM wrong_questions 
            WHERE question_type = ?
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query, (question_type,))
    
    def update_wrong_question(self, question_id: int, question_text: str, 
                             correct_answer: str, user_answer: str, explanation: str):
        """更新错题"""
        query = """
            UPDATE wrong_questions 
            SET question_text = ?, correct_answer = ?, user_answer = ?, explanation = ?
            WHERE id = ?
        """
        self.db.execute_update(query, (question_text, correct_answer, user_answer,
                                     explanation, question_id))

    def get_wrong_question_stats(self):
        """获取错题统计信息"""
        try:
            # 总错题数
            total_query = "SELECT COUNT(*) FROM wrong_questions"
            total_result = self.db.execute_query(total_query)
            total_count = total_result[0][0] if total_result else 0

            # 收藏题目数
            favorite_query = "SELECT COUNT(*) FROM wrong_questions WHERE is_favorite = 1"
            favorite_result = self.db.execute_query(favorite_query)
            favorite_count = favorite_result[0][0] if favorite_result else 0

            # 最近7天错题数
            recent_query = """
                SELECT COUNT(*) FROM wrong_questions
                WHERE created_at >= datetime('now', '-7 days')
            """
            recent_result = self.db.execute_query(recent_query)
            recent_count = recent_result[0][0] if recent_result else 0

            return {
                'total_count': total_count,
                'favorite_count': favorite_count,
                'recent_count': recent_count
            }
        except Exception as e:
            print(f"获取错题统计失败: {e}")
            return {
                'total_count': 0,
                'favorite_count': 0,
                'recent_count': 0
            }
    
    def batch_add_wrong_questions_from_exam(self, exam_record):
        """从考试记录批量添加错题"""
        print(f"🔍 开始批量添加错题...")
        print(f"考试记录类型: {type(exam_record)}")
        print(f"考试记录键: {exam_record.keys() if isinstance(exam_record, dict) else 'Not a dict'}")

        try:
            questions = exam_record['questions']
            answers = exam_record['answers']
            exam_id = exam_record.get('exam_id')
            exam_title = exam_record.get('exam_title', '未知试卷')
            exam_record_id = exam_record.get('id')

            added_count = 0

            print(f"📊 考试信息:")
            print(f"  考试ID: {exam_id}")
            print(f"  试卷标题: {exam_title}")
            print(f"  记录ID: {exam_record_id}")
            print(f"  题目数量: {len(questions)}")
            print(f"  答案数量: {len(answers)}")
            print(f"  答案内容: {answers}")

            if not questions:
                print("❌ 没有题目数据")
                return 0

            if not answers:
                print("❌ 没有答案数据")
                return 0

        except KeyError as e:
            print(f"❌ 考试记录缺少必要字段: {e}")
            return 0
        except Exception as e:
            print(f"❌ 解析考试记录失败: {e}")
            return 0

        for i, question in enumerate(questions):
            question_id = str(i)
            user_answer = answers.get(question_id, '')
            correct_answer = question.get('correct_answer', '')

            print(f"\n题目 {i+1}:")
            print(f"  题目: {question['question'][:50]}...")
            print(f"  类型: {question['type']}")
            print(f"  我的答案: '{user_answer}' (类型: {type(user_answer)})")
            print(f"  正确答案: '{correct_answer}' (类型: {type(correct_answer)})")

            # 判断是否答错
            is_wrong = False
            if question['type'] in ['single_choice', 'true_false']:
                is_wrong = user_answer != correct_answer
                print(f"  单选/判断题比较: '{user_answer}' != '{correct_answer}' = {is_wrong}")
            elif question['type'] == 'multiple_choice':
                if isinstance(user_answer, list) and isinstance(correct_answer, list):
                    is_wrong = set(user_answer) != set(correct_answer)
                    print(f"  多选题比较: {set(user_answer)} != {set(correct_answer)} = {is_wrong}")
                else:
                    is_wrong = True
                    print(f"  多选题类型不匹配: {is_wrong}")
            else:
                # 对于主观题，如果有答案就认为是对的（除非特殊处理）
                is_wrong = False
                print(f"  主观题暂不判断对错")

            print(f"  是否错题: {is_wrong}")

            # 如果答错，添加到错题本
            if is_wrong:
                try:
                    print(f"  正在添加错题...")
                    # 题号是从1开始的，所以是i+1
                    question_number = i + 1
                    print(f"  题号: 第{question_number}题")

                    question_id_added = self.add_wrong_question(
                        question_text=question['question'],
                        question_type=question['type'],
                        correct_answer=str(correct_answer),
                        user_answer=str(user_answer),
                        explanation=question.get('explanation', ''),
                        exam_id=exam_id,
                        exam_title=exam_title,
                        exam_record_id=exam_record_id,
                        question_number=question_number
                    )
                    print(f"  ✅ 错题添加成功，ID: {question_id_added}")
                    added_count += 1
                except Exception as e:
                    print(f"  ❌ 错题添加失败: {e}")
                    # 不再忽略错误，而是打印出来
            else:
                print(f"  ✓ 答题正确，跳过")

        print(f"\n🎉 批量添加完成，共添加 {added_count} 道错题")
        return added_count

    def get_wrong_questions_by_exam(self, exam_id: int):
        """根据试卷ID获取错题"""
        query = """
            SELECT id, question_text, question_type, correct_answer, user_answer,
                   explanation, is_favorite, created_at, exam_id, exam_title, exam_record_id
            FROM wrong_questions
            WHERE exam_id = ?
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query, (exam_id,))

    def get_wrong_questions_grouped_by_exam(self):
        """按试卷分组获取错题"""
        query = """
            SELECT exam_id, exam_title, COUNT(*) as question_count,
                   MIN(created_at) as first_created, MAX(created_at) as last_created
            FROM wrong_questions
            WHERE exam_id IS NOT NULL
            GROUP BY exam_id, exam_title
            ORDER BY last_created DESC
        """
        return self.db.execute_query(query)

    def get_exam_wrong_question_summary(self):
        """获取试卷错题统计摘要"""
        query = """
            SELECT
                COALESCE(exam_title, '未分类错题') as exam_name,
                COUNT(*) as total_count,
                SUM(CASE WHEN is_favorite = 1 THEN 1 ELSE 0 END) as favorite_count,
                MAX(created_at) as latest_time
            FROM wrong_questions
            GROUP BY COALESCE(exam_id, 0), COALESCE(exam_title, '未分类错题')
            ORDER BY latest_time DESC
        """
        return self.db.execute_query(query)
