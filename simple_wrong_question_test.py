#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的错题测试
"""

import sys
import os
import sqlite3
from datetime import datetime

def simple_test():
    """简单测试"""
    print("=== 简单错题测试 ===")
    
    # 直接操作数据库
    db_path = "data/exam_system.db"
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建错题表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS wrong_questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question_text TEXT NOT NULL,
            question_type TEXT NOT NULL,
            correct_answer TEXT NOT NULL,
            user_answer TEXT NOT NULL,
            explanation TEXT,
            is_favorite BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 清空现有错题
    cursor.execute("DELETE FROM wrong_questions")
    print("✓ 清空现有错题")
    
    # 添加测试错题
    test_questions = [
        ("测试错题1：Python中哪个关键字用于定义函数？", "single_choice", "B", "A", "Python中使用def关键字来定义函数。"),
        ("测试错题2：Python是开源的编程语言。", "true_false", "对", "错", "Python确实是开源的编程语言。"),
        ("测试错题3：列表和元组的区别是什么？", "short_answer", "列表可变，元组不可变", "不知道", "列表是可变的，元组是不可变的数据结构。")
    ]
    
    for i, (question, q_type, correct, user, explanation) in enumerate(test_questions, 1):
        cursor.execute('''
            INSERT INTO wrong_questions (question_text, question_type, correct_answer, user_answer, explanation, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (question, q_type, correct, user, explanation, datetime.now()))
        print(f"✓ 添加错题 {i}")
    
    conn.commit()
    
    # 验证添加结果
    cursor.execute("SELECT COUNT(*) FROM wrong_questions")
    count = cursor.fetchone()[0]
    print(f"✓ 错题本中共有 {count} 道错题")
    
    # 显示错题
    cursor.execute("SELECT id, question_text, question_type, correct_answer, user_answer FROM wrong_questions")
    questions = cursor.fetchall()
    
    print("\n错题列表:")
    for q in questions:
        print(f"  ID:{q[0]} - {q[1][:50]}...")
        print(f"    类型:{q[2]} | 正确答案:{q[3]} | 我的答案:{q[4]}")
    
    conn.close()
    
    print(f"\n🎉 简单测试完成！")
    print(f"现在启动程序，点击'错题本'应该能看到 {count} 道错题")
    
    return True

if __name__ == "__main__":
    simple_test()
