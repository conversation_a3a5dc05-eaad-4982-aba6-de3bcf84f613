#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试系统主程序 - 简化版本
作者：AI Assistant
版本：2.0 - 简化版
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from src.ui.main_window import MainWindow
from src.utils.config_manager import ConfigManager
from src.utils.database_manager import DatabaseManager
from src.utils.backup_manager import BackupManager

def main():
    """主函数 - 现代化启动流程"""
    startup_time = time.time()

    try:
        # 第一步：显示启动画面
        print("🚀 启动考试系统 v2.0...")

        # 创建隐藏的根窗口
        root = tk.Tk()
        root.withdraw()

        # 显示启动画面
        splash = show_splash_screen()

        # 第二步：性能优化
        print("⚡ 启动性能优化...")
        optimizer = get_performance_optimizer()
        optimizer.optimize_startup()

        # 第三步：初始化配置管理器
        print("⚙️ 初始化配置...")
        config = ConfigManager()

        # 第四步：初始化数据库
        print("💾 初始化数据库...")
        db_manager = DatabaseManager()
        db_manager.init_database()

        # 优化数据库性能
        if hasattr(db_manager, 'connection'):
            optimizer.optimize_database_queries(db_manager.connection)

        # 第五步：初始化备份管理器
        print("📦 初始化备份系统...")
        backup_manager = BackupManager(db_manager.db_path)

        # 第六步：初始化主题系统
        print("🎨 初始化主题系统...")
        theme_manager = get_theme_manager()
        theme_manager.apply_theme()

        # 第七步：创建主窗口
        print("🖥️ 创建主界面...")
        def create_main_window():
            try:
                # 显示主窗口
                root.deiconify()

                # 创建主应用
                app = MainWindow(root, config, db_manager)

                # 集成优化功能
                app.performance_optimizer = optimizer
                app.backup_manager = backup_manager
                app.theme_manager = theme_manager

                # 记录启动时间
                total_startup_time = time.time() - startup_time
                optimizer.performance_stats["startup_time"] = total_startup_time

                print(f"✅ 系统启动完成！耗时: {total_startup_time:.2f}秒")

                # 关闭启动画面
                splash.close()

                # 启动应用主循环
                root.mainloop()

            except Exception as e:
                splash.close()
                messagebox.showerror("启动错误", f"主界面创建失败：{str(e)}")
                sys.exit(1)

        # 延迟创建主窗口，让启动画面完成动画
        root.after(2000, create_main_window)

        # 启动事件循环
        root.mainloop()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        messagebox.showerror("启动错误", f"程序启动失败：{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
