#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口类
负责创建和管理主用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import os
import sys

from src.ui.material_window import MaterialWindow
from src.ui.exam_generation_window import ExamGenerationWindow
from src.ui.exam_selection_window import ExamSelectionWindow
from src.ui.knowledge_graph_window import KnowledgeGraphWindow
from src.ui.settings_window import SettingsWindow
from src.ui.advanced_api_manager_window import AdvancedAPIManagerWindow
from src.ui.theme_manager import get_theme_manager
from src.ui.shortcut_manager import ShortcutManager
from src.core.material_manager import MaterialManager
from src.core.exam_manager import ExamManager
from src.core.wrong_question_manager import WrongQuestionManager
from src.api.ai_manager import AIManager

class MainWindow:
    def __init__(self, root, config_manager, db_manager):
        """初始化主窗口"""
        self.root = root
        self.config = config_manager
        self.db = db_manager

        # 初始化管理器
        self.material_manager = MaterialManager(db_manager)
        self.exam_manager = ExamManager(db_manager)
        self.wrong_question_manager = WrongQuestionManager(db_manager)
        self.ai_manager = AIManager(config_manager)

        # 初始化优化功能
        self.theme_manager = get_theme_manager()
        self.shortcut_manager = ShortcutManager(root)
        self.performance_optimizer = None  # 将在main.py中设置
        self.backup_manager = None  # 将在main.py中设置

        # 初始化第二阶段智能功能
        self.learning_analytics = None
        self.intelligent_assistant = None
        self.question_generator = None

        self.setup_window()
        self.create_menu()
        self.create_main_layout()
        self.create_status_bar()

        # 设置快捷键
        self.setup_shortcuts()

        # 应用主题
        self.apply_theme()
    
    def setup_window(self):
        """设置窗口基本属性"""
        ui_config = self.config.get_ui_config()
        
        self.root.title("考试系统 - Kaoshi")
        self.root.geometry(f"{ui_config['window_width']}x{ui_config['window_height']}")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('assets/icon.ico')
            pass
        except:
            pass
        
        # 居中显示窗口
        self.center_window()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_shortcuts(self):
        """设置快捷键"""
        try:
            # 注册快捷键回调
            self.shortcut_manager.register_action("new_exam", self.generate_exam)
            self.shortcut_manager.register_action("open_exam", self.start_exam)
            self.shortcut_manager.register_action("exam_history", self.view_exam_history)
            self.shortcut_manager.register_action("wrong_questions", self.open_wrong_questions)
            self.shortcut_manager.register_action("settings", self.open_system_settings)
            self.shortcut_manager.register_action("quit", self.on_closing)
            self.shortcut_manager.register_action("theme_toggle", self.toggle_theme)
            self.shortcut_manager.register_action("backup", self.create_backup)
            self.shortcut_manager.register_action("refresh", self.refresh_interface)
            self.shortcut_manager.register_action("learning_dashboard", self.open_learning_dashboard)
            self.shortcut_manager.register_action("intelligent_assistant", self.open_intelligent_assistant)

            print("✅ 快捷键设置完成")

        except Exception as e:
            print(f"❌ 快捷键设置失败: {e}")

    def apply_theme(self):
        """应用主题"""
        try:
            # 应用主题到主窗口
            self.theme_manager.apply_to_widget(self.root, "frame")

            # 注册主题变化回调
            self.theme_manager.add_callback(self.on_theme_changed)

            print("✅ 主题应用完成")

        except Exception as e:
            print(f"❌ 主题应用失败: {e}")

    def toggle_theme(self, event=None):
        """切换主题"""
        try:
            current_theme = self.theme_manager.current_theme
            new_theme = "dark" if current_theme == "light" else "light"
            self.theme_manager.set_theme(new_theme)

        except Exception as e:
            print(f"❌ 主题切换失败: {e}")

    def on_theme_changed(self, theme_name):
        """主题变化回调"""
        try:
            # 重新应用主题到所有控件
            self.apply_theme_to_all_widgets()

        except Exception as e:
            print(f"❌ 主题变化处理失败: {e}")

    def apply_theme_to_all_widgets(self):
        """应用主题到所有控件"""
        try:
            # 递归应用主题到所有子控件
            def apply_to_children(widget):
                try:
                    self.theme_manager.apply_to_widget(widget, "frame")
                    for child in widget.winfo_children():
                        apply_to_children(child)
                except:
                    pass

            apply_to_children(self.root)

        except Exception as e:
            print(f"❌ 批量主题应用失败: {e}")

    def create_backup(self, event=None):
        """创建数据备份"""
        try:
            if self.backup_manager:
                backup_path = self.backup_manager.create_backup()
                messagebox.showinfo("备份成功", f"数据备份已创建：\n{backup_path}")
            else:
                messagebox.showwarning("备份失败", "备份管理器未初始化")

        except Exception as e:
            messagebox.showerror("备份失败", f"创建备份失败：{str(e)}")

    def refresh_interface(self, event=None):
        """刷新界面"""
        try:
            # 重新创建主布局
            for widget in self.main_frame.winfo_children():
                widget.destroy()

            self.create_main_layout()
            self.apply_theme()

            print("✅ 界面刷新完成")

        except Exception as e:
            print(f"❌ 界面刷新失败: {e}")

    def on_closing(self, event=None):
        """程序关闭处理"""
        try:
            # 停止性能监控
            if self.performance_optimizer:
                self.performance_optimizer.stop_monitoring()

            # 保存配置
            # self.config.save()

            self.root.quit()

        except Exception as e:
            print(f"❌ 程序关闭处理失败: {e}")
            self.root.quit()

    def initialize_intelligent_features(self):
        """初始化智能功能"""
        try:
            # 初始化学习分析系统
            from src.core.learning_analytics import LearningAnalytics
            self.learning_analytics = LearningAnalytics(self.db)

            # 初始化智能助手
            from src.core.intelligent_assistant import IntelligentAssistant
            self.intelligent_assistant = IntelligentAssistant(
                self.learning_analytics, self.ai_manager
            )

            # 初始化智能出题系统
            from src.core.intelligent_question_generator import IntelligentQuestionGenerator
            self.question_generator = IntelligentQuestionGenerator(
                self.db, self.learning_analytics
            )

            print("✅ 智能功能初始化完成")

        except Exception as e:
            print(f"❌ 智能功能初始化失败: {e}")

    def open_learning_dashboard(self, event=None):
        """打开学习仪表板"""
        try:
            if not self.learning_analytics:
                self.initialize_intelligent_features()

            if self.learning_analytics:
                from src.ui.learning_dashboard import LearningDashboard
                LearningDashboard(self.root, self.learning_analytics, self.theme_manager)
            else:
                messagebox.showwarning("提示", "学习分析系统未初始化")

        except Exception as e:
            print(f"❌ 打开学习仪表板失败: {e}")
            messagebox.showerror("错误", f"打开学习仪表板失败：{str(e)}")

    def open_intelligent_assistant(self, event=None):
        """打开智能助手"""
        try:
            if not self.intelligent_assistant:
                self.initialize_intelligent_features()

            if self.intelligent_assistant:
                from src.ui.assistant_window import AssistantWindow
                AssistantWindow(self.root, self.intelligent_assistant, self.theme_manager)
            else:
                messagebox.showwarning("提示", "智能助手未初始化")

        except Exception as e:
            print(f"❌ 打开智能助手失败: {e}")
            messagebox.showerror("错误", f"打开智能助手失败：{str(e)}")

    def open_intelligent_exam_generator(self, event=None):
        """打开智能出题系统"""
        try:
            if not self.question_generator:
                self.initialize_intelligent_features()

            if self.question_generator:
                # 生成自适应考试
                exam_config = self.question_generator.generate_adaptive_exam()

                if exam_config:
                    # 显示考试配置
                    self.show_exam_config_dialog(exam_config)
                else:
                    messagebox.showwarning("提示", "无法生成智能考试，请先完成一些学习活动")
            else:
                messagebox.showwarning("提示", "智能出题系统未初始化")

        except Exception as e:
            print(f"❌ 打开智能出题系统失败: {e}")
            messagebox.showerror("错误", f"打开智能出题系统失败：{str(e)}")

    def show_exam_config_dialog(self, exam_config):
        """显示考试配置对话框"""
        try:
            dialog = tk.Toplevel(self.root)
            dialog.title("🎯 智能考试配置")
            dialog.geometry("500x400")
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.geometry("+{}+{}".format(
                self.root.winfo_x() + 100,
                self.root.winfo_y() + 50
            ))

            # 主框架
            main_frame = ttk.Frame(dialog)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 标题
            title_label = ttk.Label(main_frame, text="🎯 智能自适应考试",
                                   font=("Microsoft YaHei", 16, "bold"))
            title_label.pack(pady=(0, 20))

            # 考试信息
            info_text = tk.Text(main_frame, wrap=tk.WORD, height=15, width=50)
            info_text.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

            # 填充考试信息
            info_content = f"""📋 考试详情：

📚 考试标题：{exam_config.get('title', '')}
📝 题目数量：{exam_config.get('question_count', 0)} 题
⏱️ 考试时间：{exam_config.get('time_limit', 0)} 分钟
🎯 目标主题：{', '.join(exam_config.get('target_topics', []))}
📊 能力水平：{exam_config.get('user_ability_level', 0):.2f}

🧠 出题策略：
"""

            strategy = exam_config.get('strategy', {})
            if strategy:
                info_content += f"• 难度分布：{strategy.get('difficulty_distribution', {})}\n"
                info_content += f"• 主题分布：{strategy.get('topic_distribution', {})}\n"

                focus_areas = strategy.get('focus_areas', [])
                if focus_areas:
                    info_content += f"• 重点关注：{', '.join([area['topic'] for area in focus_areas])}\n"

            info_content += f"\n💡 这份考试是根据你的学习情况智能生成的，\n   旨在帮助你检验学习效果并发现需要改进的地方。"

            info_text.insert(1.0, info_content)
            info_text.config(state=tk.DISABLED)

            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            # 开始考试按钮
            start_btn = ttk.Button(button_frame, text="🚀 开始考试",
                                  command=lambda: self.start_intelligent_exam(exam_config, dialog))
            start_btn.pack(side=tk.LEFT)

            # 重新生成按钮
            regenerate_btn = ttk.Button(button_frame, text="🔄 重新生成",
                                       command=lambda: self.regenerate_exam(dialog))
            regenerate_btn.pack(side=tk.LEFT, padx=(10, 0))

            # 取消按钮
            cancel_btn = ttk.Button(button_frame, text="❌ 取消",
                                   command=dialog.destroy)
            cancel_btn.pack(side=tk.RIGHT)

        except Exception as e:
            print(f"❌ 显示考试配置对话框失败: {e}")

    def start_intelligent_exam(self, exam_config, dialog):
        """开始智能考试"""
        try:
            dialog.destroy()

            # 这里可以启动考试界面
            messagebox.showinfo("开始考试", "智能考试功能正在完善中，敬请期待！")

        except Exception as e:
            print(f"❌ 开始智能考试失败: {e}")

    def regenerate_exam(self, dialog):
        """重新生成考试"""
        try:
            dialog.destroy()

            # 重新打开智能出题系统
            self.open_intelligent_exam_generator()

        except Exception as e:
            print(f"❌ 重新生成考试失败: {e}")

    def show_learning_analysis(self, event=None):
        """显示学习分析"""
        try:
            if not self.learning_analytics:
                self.initialize_intelligent_features()

            if self.learning_analytics:
                stats = self.learning_analytics.get_learning_statistics()
                mastery_data = self.learning_analytics.get_knowledge_mastery_overview()

                # 创建分析窗口
                analysis_window = tk.Toplevel(self.root)
                analysis_window.title("📈 学习分析报告")
                analysis_window.geometry("600x500")
                analysis_window.transient(self.root)

                # 分析内容
                analysis_text = tk.Text(analysis_window, wrap=tk.WORD, font=("Microsoft YaHei", 11))
                analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

                # 生成分析报告
                report_content = "📈 学习分析报告\n\n"

                if stats:
                    overall = stats.get('overall', {})
                    recent = stats.get('recent_week', {})
                    knowledge = stats.get('knowledge', {})

                    report_content += f"📊 整体表现：\n"
                    report_content += f"• 总学习次数：{overall.get('total_sessions', 0)} 次\n"
                    report_content += f"• 总学习时长：{overall.get('total_time', 0)//3600:.1f} 小时\n"
                    report_content += f"• 整体正确率：{overall.get('overall_accuracy', 0):.1%}\n\n"

                    report_content += f"📈 最近一周：\n"
                    report_content += f"• 学习次数：{recent.get('sessions', 0)} 次\n"
                    report_content += f"• 学习时长：{recent.get('time', 0)//3600:.1f} 小时\n"
                    report_content += f"• 正确率：{recent.get('accuracy', 0):.1%}\n\n"

                    report_content += f"🧠 知识掌握：\n"
                    report_content += f"• 总知识点：{knowledge.get('total_topics', 0)} 个\n"
                    report_content += f"• 已掌握：{knowledge.get('mastered_topics', 0)} 个\n"
                    report_content += f"• 需提高：{knowledge.get('weak_topics', 0)} 个\n"
                    report_content += f"• 平均掌握度：{knowledge.get('average_mastery', 0):.1%}\n\n"
                else:
                    report_content += "暂无学习数据，请先进行一些学习活动。\n\n"

                # 添加建议
                if mastery_data:
                    weak_topics = [topic for topic, data in mastery_data.items()
                                  if data['average_mastery'] < 0.6]
                    if weak_topics:
                        report_content += f"💡 建议重点关注：{', '.join(weak_topics[:3])}\n"

                analysis_text.insert(1.0, report_content)
                analysis_text.config(state=tk.DISABLED)
            else:
                messagebox.showwarning("提示", "学习分析系统未初始化")

        except Exception as e:
            print(f"❌ 显示学习分析失败: {e}")
            messagebox.showerror("错误", f"显示学习分析失败：{str(e)}")

    def show_personalized_recommendations(self, event=None):
        """显示个性化建议"""
        try:
            if not self.learning_analytics:
                self.initialize_intelligent_features()

            if self.learning_analytics:
                recommendations = self.learning_analytics.generate_personalized_recommendations()

                # 创建建议窗口
                rec_window = tk.Toplevel(self.root)
                rec_window.title("💡 个性化学习建议")
                rec_window.geometry("500x400")
                rec_window.transient(self.root)

                # 建议内容
                rec_text = tk.Text(rec_window, wrap=tk.WORD, font=("Microsoft YaHei", 11))
                rec_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

                # 生成建议内容
                rec_content = "💡 个性化学习建议\n\n"

                if recommendations:
                    for i, rec in enumerate(recommendations, 1):
                        priority_icon = "🔴" if rec['priority'] == 1 else "🟡" if rec['priority'] == 2 else "🟢"
                        rec_content += f"{priority_icon} {i}. {rec['title']}\n"
                        rec_content += f"   {rec['content']}\n"
                        rec_content += f"   💭 {rec['reason']}\n\n"
                else:
                    rec_content += "暂无个性化建议，请先进行一些学习活动。\n\n"

                rec_content += "📚 通用学习建议：\n"
                rec_content += "• 制定明确的学习目标和计划\n"
                rec_content += "• 保持规律的学习习惯\n"
                rec_content += "• 及时复习和总结学习内容\n"
                rec_content += "• 多做练习，巩固知识点\n"

                rec_text.insert(1.0, rec_content)
                rec_text.config(state=tk.DISABLED)
            else:
                messagebox.showwarning("提示", "学习分析系统未初始化")

        except Exception as e:
            print(f"❌ 显示个性化建议失败: {e}")
            messagebox.showerror("错误", f"显示个性化建议失败：{str(e)}")
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入材料", command=self.import_material)
        file_menu.add_separator()
        file_menu.add_command(label="📦 备份管理", command=self.open_backup_manager)
        file_menu.add_command(label="💾 快速备份", command=self.create_backup)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 考试菜单
        exam_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="考试", menu=exam_menu)
        exam_menu.add_command(label="生成试卷", command=self.generate_exam)
        exam_menu.add_command(label="开始考试", command=self.start_exam)
        exam_menu.add_command(label="考试历史", command=self.view_exam_history)
        
        # 学习菜单
        study_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="学习", menu=study_menu)
        study_menu.add_command(label="错题本", command=self.open_wrong_questions)
        study_menu.add_command(label="收藏题目", command=self.open_favorites)
        study_menu.add_command(label="知识图谱", command=self.open_knowledge_graph)
        
        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="API配置", command=self.open_api_settings)
        settings_menu.add_command(label="系统设置", command=self.open_system_settings)
        settings_menu.add_separator()
        settings_menu.add_command(label="🎨 切换主题", command=self.toggle_theme)
        settings_menu.add_command(label="🚀 高级API管理", command=self.open_advanced_api_manager)
        
        # 智能功能菜单
        ai_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="智能功能", menu=ai_menu)
        ai_menu.add_command(label="📊 学习仪表板", command=self.open_learning_dashboard)
        ai_menu.add_command(label="🤖 智能助手", command=self.open_intelligent_assistant)
        ai_menu.add_command(label="🎯 智能出题", command=self.open_intelligent_exam_generator)
        ai_menu.add_separator()
        ai_menu.add_command(label="📈 学习分析", command=self.show_learning_analysis)
        ai_menu.add_command(label="💡 个性化建议", command=self.show_personalized_recommendations)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="📊 性能统计", command=self.show_performance_stats)
        tools_menu.add_command(label="🔄 刷新界面", command=self.refresh_interface)
        tools_menu.add_separator()
        tools_menu.add_command(label="⌨️ 快捷键帮助", command=self.show_shortcut_help)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="⌨️ 快捷键", command=self.show_shortcut_help)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_main_layout(self):
        """创建主布局"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧导航面板
        self.create_navigation_panel(main_frame)
        
        # 右侧内容面板
        self.create_content_panel(main_frame)
    
    def create_navigation_panel(self, parent):
        """创建左侧导航面板"""
        nav_frame = ttk.LabelFrame(parent, text="功能导航", padding=10)
        nav_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 导航按钮 - 重新设计布局
        nav_buttons = [
            # 第一行：核心功能
            [
                ("📚 材料管理", self.show_material_management, "管理学习材料和题库"),
                ("📝 生成试卷", self.generate_exam, "AI智能生成个性化试卷"),
                ("⏱️ 开始考试", self.start_exam, "开始在线考试测验"),
                ("📊 考试记录", self.view_exam_history, "查看历史考试成绩")
            ],
            # 第二行：学习功能
            [
                ("❌ 错题本", self.open_wrong_questions, "管理和复习错题"),
                ("⭐ 收藏题目", self.open_favorites, "查看收藏的重点题目"),
                ("🕸️ 知识图谱", self.open_knowledge_graph, "可视化知识点关系"),
                ("⚙️ 系统设置", self.open_system_settings, "配置系统参数")
            ]
        ]
        
        # 创建美化的按钮布局
        for row_index, button_row in enumerate(nav_buttons):
            row_frame = ttk.Frame(nav_frame)
            row_frame.pack(fill=tk.X, pady=(5, 10))

            for text, command, tooltip in button_row:
                btn_frame = ttk.Frame(row_frame)
                btn_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

                btn = ttk.Button(btn_frame, text=text, command=command)
                btn.pack(fill=tk.BOTH, expand=True, ipady=8)

                # 添加工具提示
                self.create_tooltip(btn, tooltip)
    
    def create_content_panel(self, parent):
        """创建右侧内容面板"""
        self.content_frame = ttk.LabelFrame(parent, text="主要内容", padding=10)
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 默认显示欢迎界面
        self.show_welcome_screen()
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 时间标签
        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=5)
        
        self.update_time()
    
    def update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
    
    def show_welcome_screen(self):
        """显示美化的欢迎界面"""
        # 清空内容面板
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # 创建滚动区域
        canvas = tk.Canvas(self.content_frame)
        scrollbar = ttk.Scrollbar(self.content_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 欢迎标题
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill=tk.X, pady=30)

        welcome_label = ttk.Label(title_frame, text="🎓 智能考试系统",
                                 font=("Microsoft YaHei", 24, "bold"))
        welcome_label.pack()

        subtitle_label = ttk.Label(title_frame, text="AI驱动的个性化学习平台",
                                  font=("Microsoft YaHei", 12), foreground="gray")
        subtitle_label.pack(pady=(5, 0))

        version_label = ttk.Label(title_frame, text="Version 2.0",
                                 font=("Microsoft YaHei", 10), foreground="blue")
        version_label.pack(pady=(5, 0))

        # 功能特色
        features_frame = ttk.LabelFrame(scrollable_frame, text="✨ 核心功能", padding=20)
        features_frame.pack(fill=tk.X, padx=20, pady=20)

        features = [
            ("📚", "智能材料管理", "支持多种格式文件导入，智能内容解析"),
            ("🤖", "AI生成试卷", "基于学习材料自动生成个性化试卷"),
            ("⏱️", "在线考试", "实时考试系统，自动计时和评分"),
            ("📊", "数据分析", "详细的学习分析和可视化图表"),
            ("❌", "错题管理", "智能错题收集，支持批量操作"),
            ("🕸️", "知识图谱", "可视化知识点关系和学习路径")
        ]

        for icon, title, desc in features:
            feature_frame = ttk.Frame(features_frame)
            feature_frame.pack(fill=tk.X, pady=5)

            ttk.Label(feature_frame, text=f"{icon} {title}",
                     font=("Microsoft YaHei", 11, "bold")).pack(anchor=tk.W)
            ttk.Label(feature_frame, text=f"   {desc}",
                     font=("Microsoft YaHei", 9), foreground="gray").pack(anchor=tk.W)

        # 快速开始
        quick_frame = ttk.LabelFrame(scrollable_frame, text="🚀 快速开始", padding=20)
        quick_frame.pack(fill=tk.X, padx=20, pady=20)

        quick_desc = ttk.Label(quick_frame,
                              text="选择一个选项开始您的学习之旅：",
                              font=("Microsoft YaHei", 11))
        quick_desc.pack(pady=(0, 15))

        quick_buttons = ttk.Frame(quick_frame)
        quick_buttons.pack()

        ttk.Button(quick_buttons, text="📚 导入学习材料",
                  command=self.show_material_management).pack(side=tk.LEFT, padx=10, ipady=5)
        ttk.Button(quick_buttons, text="📝 生成练习试卷",
                  command=self.generate_exam).pack(side=tk.LEFT, padx=10, ipady=5)
        ttk.Button(quick_buttons, text="⏱️ 开始考试",
                  command=self.start_exam).pack(side=tk.LEFT, padx=10, ipady=5)

        # 学习统计
        stats_frame = ttk.LabelFrame(scrollable_frame, text="📈 学习统计", padding=20)
        stats_frame.pack(fill=tk.X, padx=20, pady=20)

        try:
            exam_stats = self.exam_manager.get_exam_statistics()
            wrong_stats = self.wrong_question_manager.get_wrong_question_stats()

            stats_text = f"""📊 考试次数: {exam_stats.get('total_records', 0)}次
📈 平均分数: {exam_stats.get('average_percentage', 0):.1f}%
❌ 错题总数: {wrong_stats.get('total_questions', 0)}道
⭐ 收藏题目: {wrong_stats.get('favorite_count', 0)}道"""

        except:
            stats_text = "📊 暂无学习数据\n开始您的第一次考试吧！"

        stats_label = ttk.Label(stats_frame, text=stats_text,
                               font=("Microsoft YaHei", 10))
        stats_label.pack()

        # 使用提示
        tips_frame = ttk.LabelFrame(scrollable_frame, text="💡 使用提示", padding=20)
        tips_frame.pack(fill=tk.X, padx=20, pady=20)

        tips_text = """• 首次使用建议先导入学习材料
• 可以直接生成试卷进行练习
• 错题本支持批量操作，提高学习效率
• 考试记录提供详细的学习分析
• 所有功能都有工具提示，鼠标悬停查看"""

        tips_label = ttk.Label(tips_frame, text=tips_text,
                              font=("Microsoft YaHei", 9), justify=tk.LEFT)
        tips_label.pack(anchor=tk.W)

        # 布局滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    # 菜单和按钮事件处理方法
    def import_material(self):
        """打开材料导入窗口"""
        MaterialWindow(self.root, self.db)

    def backup_data(self):
        """备份数据"""
        try:
            import shutil
            from datetime import datetime

            # 创建备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = filedialog.asksaveasfilename(
                title="保存备份文件",
                defaultextension=".db",
                initialvalue=f"exam_backup_{timestamp}.db",
                filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")]
            )

            if backup_file:
                db_path = self.config.get('DATABASE', 'db_path', 'data/exam_system.db')
                shutil.copy2(db_path, backup_file)
                messagebox.showinfo("成功", "数据备份完成！")

        except Exception as e:
            messagebox.showerror("错误", f"备份失败: {str(e)}")

    def restore_data(self):
        """恢复数据"""
        if messagebox.askyesno("确认", "恢复数据将覆盖当前所有数据，确定继续吗？"):
            backup_file = filedialog.askopenfilename(
                title="选择备份文件",
                filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")]
            )

            if backup_file:
                try:
                    import shutil
                    db_path = self.config.get('DATABASE', 'db_path', 'data/exam_system.db')
                    shutil.copy2(backup_file, db_path)
                    messagebox.showinfo("成功", "数据恢复完成！请重启程序。")
                except Exception as e:
                    messagebox.showerror("错误", f"恢复失败: {str(e)}")

    def generate_exam(self):
        """生成试卷"""
        ExamGenerationWindow(self.root, self.material_manager, self.ai_manager, self.exam_manager)

    def start_exam(self):
        """开始考试"""
        ExamSelectionWindow(self.root, self.exam_manager, self.wrong_question_manager)

    def view_exam_history(self):
        """查看考试历史"""
        try:
            from src.ui.enhanced_exam_history_window import EnhancedExamHistoryWindow
            EnhancedExamHistoryWindow(self.root, self.exam_manager, self.wrong_question_manager)
        except ImportError:
            # 回退到传统面板
            self.show_exam_history_panel()
        except Exception as e:
            messagebox.showerror("错误", f"打开考试记录失败: {str(e)}")
            self.show_exam_history_panel()

    def open_wrong_questions(self):
        """打开错题本 - 直接使用高级版"""
        try:
            from src.ui.advanced_wrong_questions_window import AdvancedWrongQuestionsWindow
            advanced_window = AdvancedWrongQuestionsWindow(self.root, self.wrong_question_manager, self.exam_manager)
            advanced_window.show()
        except ImportError as e:
            messagebox.showerror("错误", f"高级错题本模块未找到: {e}")
            # 回退到传统版本
            self.show_wrong_questions_panel()
        except Exception as e:
            messagebox.showerror("错误", f"打开错题本失败: {str(e)}")
            print(f"错题本错误: {e}")
            import traceback
            traceback.print_exc()



    def open_favorites(self):
        """打开收藏题目 - 使用高级版错题本"""
        try:
            from src.ui.advanced_wrong_questions_window import AdvancedWrongQuestionsWindow
            advanced_window = AdvancedWrongQuestionsWindow(self.root, self.wrong_question_manager, self.exam_manager)
            advanced_window.show()
            # 自动切换到收藏视图
            advanced_window.switch_to_favorites_view()
        except ImportError as e:
            messagebox.showerror("错误", f"高级错题本模块未找到: {e}")
            # 回退到传统收藏面板
            self.show_favorites_panel()
        except Exception as e:
            messagebox.showerror("错误", f"打开收藏题目失败: {str(e)}")
            print(f"收藏题目错误: {e}")
            import traceback
            traceback.print_exc()

    def open_knowledge_graph(self):
        """打开知识图谱"""
        try:
            # 尝试导入完整的知识图谱窗口
            from src.ui.knowledge_graph_window import KnowledgeGraphWindow
            KnowledgeGraphWindow(self.root, self.db)
        except ImportError as e:
            print(f"知识图谱模块导入失败: {e}")
            # 显示简化版知识图谱
            self.show_simple_knowledge_graph()
        except Exception as e:
            print(f"知识图谱错误: {e}")
            # 显示简化版知识图谱
            self.show_simple_knowledge_graph()

    def show_simple_knowledge_graph(self):
        """显示简化版知识图谱"""
        # 创建简化版知识图谱窗口
        kg_window = tk.Toplevel(self.root)
        kg_window.title("🕸️ 知识图谱")
        kg_window.geometry("800x600")
        kg_window.transient(self.root)

        # 主框架
        main_frame = ttk.Frame(kg_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题
        ttk.Label(main_frame, text="🕸️ 知识图谱",
                 font=("Microsoft YaHei", 16, "bold")).pack(pady=(0, 20))

        # 功能说明
        info_text = """📊 知识图谱功能

🎯 主要功能:
• 知识点关系可视化
• 学习进度追踪
• 薄弱环节分析
• 学习路径推荐

📈 当前状态:
• 基础框架已完成
• 数据分析功能开发中
• 可视化组件优化中

💡 使用建议:
• 通过错题本分析薄弱知识点
• 结合考试记录制定学习计划
• 定期查看学习进度统计

🔧 开发进度:
• ✅ 界面框架
• 🔄 数据分析算法
• 🔄 图谱可视化
• 📅 预计完成时间: 下个版本

感谢您的耐心等待！"""

        text_widget = tk.Text(main_frame, wrap=tk.WORD, font=("Microsoft YaHei", 11))
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(1.0, info_text)
        text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        ttk.Button(main_frame, text="关闭", command=kg_window.destroy).pack(pady=(10, 0))

    def show_material_management(self):
        """显示材料管理"""
        MaterialWindow(self.root, self.db)

    def open_api_settings(self):
        """打开API设置"""
        SettingsWindow(self.root, self.config, self.db, self.ai_manager)

    def open_system_settings(self):
        """打开系统设置"""
        SettingsWindow(self.root, self.config, self.db, self.ai_manager)

    def open_backup_manager(self):
        """打开备份管理器"""
        try:
            if self.backup_manager:
                from src.ui.backup_window import BackupWindow
                BackupWindow(self.root, self.backup_manager)
            else:
                messagebox.showwarning("提示", "备份管理器未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"打开备份管理器失败：{str(e)}")

    def show_performance_stats(self):
        """显示性能统计"""
        try:
            if self.performance_optimizer:
                stats = self.performance_optimizer.get_performance_stats()

                stats_text = f"""📊 系统性能统计

🚀 启动时间: {stats.get('startup_time', 0):.2f} 秒
💾 内存使用: {stats.get('memory_usage', 0):.1f} MB
📈 缓存命中率: {stats.get('cache_hit_rate', 0):.1f}%
🗃️ 缓存大小: {stats.get('cache_size', 0)} 项
🔍 数据库查询: {stats.get('db_queries', 0)} 次
🖥️ UI更新: {stats.get('ui_updates', 0)} 次
"""

                messagebox.showinfo("性能统计", stats_text)
            else:
                messagebox.showwarning("提示", "性能优化器未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"获取性能统计失败：{str(e)}")

    def show_shortcut_help(self):
        """显示快捷键帮助"""
        try:
            if self.shortcut_manager:
                self.shortcut_manager.show_help()
            else:
                messagebox.showwarning("提示", "快捷键管理器未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"显示快捷键帮助失败：{str(e)}")

    def open_advanced_api_manager(self):
        """打开高级API管理器"""
        AdvancedAPIManagerWindow(self.root)
    
    def show_help(self):
        messagebox.showinfo("使用说明", "这是一个智能考试系统，支持：\n\n1. 导入学习材料\n2. AI生成试卷\n3. 在线考试\n4. 错题管理\n5. 知识图谱")
    
    def show_about(self):
        messagebox.showinfo("关于", "考试系统 v2.0\n\n基于Tkinter开发的智能考试系统\n支持多种AI API生成试卷\n\n✨ 新版本特性:\n• 美化的用户界面\n• 高级错题本管理\n• 批量操作功能\n• 增强的考试记录\n• 智能学习分析")

    def create_tooltip(self, widget, text):
        """创建工具提示"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(tooltip, text=text,
                           background="lightyellow",
                           relief="solid",
                           borderwidth=1,
                           font=("Microsoft YaHei", 9))
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def show_exam_history_panel(self):
        """显示考试历史面板"""
        # 清空内容面板
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # 创建考试历史界面
        history_frame = ttk.Frame(self.content_frame)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(history_frame, text="考试历史", font=("Arial", 16, "bold")).pack(pady=(0, 10))

        # 考试记录列表
        columns = ('ID', '考试标题', '分数', '总分', '百分比', '考试时间')
        tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            if col == 'ID':
                tree.column(col, width=50)
            elif col in ['分数', '总分', '百分比']:
                tree.column(col, width=80)

        # 获取考试记录
        records = self.exam_manager.get_exam_records()
        for record in records:
            percentage = f"{record[3]/record[4]*100:.1f}%" if record[4] > 0 else "0%"
            tree.insert('', tk.END, values=(
                record[0], record[8], record[3], record[4], percentage, record[7]
            ))

        tree.pack(fill=tk.BOTH, expand=True)

        # 统计信息
        stats = self.exam_manager.get_exam_statistics()
        stats_frame = ttk.Frame(history_frame)
        stats_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(stats_frame, text=f"总考试次数: {stats['total_records']}").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(stats_frame, text=f"平均分: {stats['average_percentage']:.1f}%").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(stats_frame, text=f"最高分: {stats['highest_percentage']:.1f}%").pack(side=tk.LEFT)

    def show_wrong_questions_panel(self):
        """显示错题本面板"""
        # 清空内容面板
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # 创建错题本界面
        wrong_frame = ttk.Frame(self.content_frame)
        wrong_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(wrong_frame, text="错题本", font=("Arial", 16, "bold")).pack(pady=(0, 10))

        # 工具栏
        toolbar = ttk.Frame(wrong_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar, text="🔄 刷新", command=lambda: self.show_wrong_questions_panel()).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="📝 查看解析", command=lambda: self.view_wrong_question_explanation(tree)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="⭐ 切换收藏", command=lambda: self.toggle_wrong_question_favorite(tree)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="🗑️ 删除错题", command=lambda: self.delete_wrong_question(tree)).pack(side=tk.LEFT, padx=(0, 5))

        # 错题列表
        columns = ('ID', '题目', '类型', '正确答案', '我的答案', '试卷', '收藏', '时间')
        tree = ttk.Treeview(wrong_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            if col == 'ID':
                tree.column(col, width=50)
            elif col in ['类型', '收藏']:
                tree.column(col, width=80)
            elif col == '题目':
                tree.column(col, width=250)
            elif col == '试卷':
                tree.column(col, width=150)

        # 获取错题
        wrong_questions = self.wrong_question_manager.get_all_wrong_questions()
        for question in wrong_questions:
            favorite_text = "⭐" if question[6] else ""
            # 获取试卷名称（新字段）
            exam_title = question[9] if len(question) > 9 and question[9] else "未分类"

            tree.insert('', tk.END, values=(
                question[0],
                question[1][:40] + "..." if len(question[1]) > 40 else question[1],
                question[2], question[3], question[4],
                exam_title[:20] + "..." if len(exam_title) > 20 else exam_title,
                favorite_text,
                question[7][:16] if question[7] else ""  # 只显示日期时间
            ))

        tree.pack(fill=tk.BOTH, expand=True)

        # 绑定双击事件
        tree.bind('<Double-Button-1>', lambda e: self.view_wrong_question_explanation(tree))

        # 统计信息
        stats = self.wrong_question_manager.get_wrong_question_stats()
        stats_frame = ttk.Frame(wrong_frame)
        stats_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(stats_frame, text=f"总错题数: {stats['total_count']}").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(stats_frame, text=f"收藏题目: {stats['favorite_count']}").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(stats_frame, text=f"最近7天: {stats['recent_count']}").pack(side=tk.LEFT)

    def view_wrong_question_explanation(self, tree):
        """查看错题解析"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题！")
            return

        # 获取选中错题的ID
        item = tree.item(selection[0])
        question_id = item['values'][0]

        # 获取错题详情
        question_data = self.wrong_question_manager.get_wrong_question_by_id(question_id)
        if question_data:
            # 创建错题解析窗口
            WrongQuestionExplanationWindow(self.root, question_data)
        else:
            messagebox.showerror("错误", "无法获取错题信息！")

    def toggle_wrong_question_favorite(self, tree):
        """切换错题收藏状态"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题！")
            return

        # 获取选中错题的ID
        item = tree.item(selection[0])
        question_id = item['values'][0]

        try:
            new_status = self.wrong_question_manager.toggle_favorite(question_id)
            status_text = "已收藏" if new_status else "已取消收藏"
            messagebox.showinfo("成功", f"错题{status_text}！")
            # 刷新界面
            self.show_wrong_questions_panel()
        except Exception as e:
            messagebox.showerror("错误", f"操作失败：{str(e)}")

    def delete_wrong_question(self, tree):
        """删除错题"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题！")
            return

        # 获取选中错题的ID
        item = tree.item(selection[0])
        question_id = item['values'][0]
        question_text = item['values'][1]

        if messagebox.askyesno("确认删除", f"确定要删除这道错题吗？\n\n{question_text}"):
            try:
                self.wrong_question_manager.delete_wrong_question(question_id)
                messagebox.showinfo("成功", "错题已删除！")
                # 刷新界面
                self.show_wrong_questions_panel()
            except Exception as e:
                messagebox.showerror("错误", f"删除失败：{str(e)}")


class WrongQuestionExplanationWindow:
    """错题解析窗口"""

    def __init__(self, parent, question_data):
        self.question_data = question_data

        self.window = tk.Toplevel(parent)
        self.window.title("错题解析")
        self.window.geometry("700x500")
        self.window.transient(parent)
        self.window.grab_set()

        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(title_frame, text="错题解析",
                 font=("Arial", 16, "bold")).pack()

        # 题目类型和时间
        type_name = self.get_question_type_name(self.question_data[2])
        create_time = self.question_data[7] if len(self.question_data) > 7 else "未知"
        ttk.Label(title_frame, text=f"{type_name} | {create_time}",
                 font=("Arial", 10), foreground="gray").pack()

        # 题目内容
        question_frame = ttk.LabelFrame(main_frame, text="题目", padding=10)
        question_frame.pack(fill=tk.X, pady=(0, 10))

        question_text = tk.Text(question_frame, wrap=tk.WORD, height=4,
                               font=("Arial", 11), state=tk.DISABLED)
        question_text.pack(fill=tk.X)

        question_text.config(state=tk.NORMAL)
        question_text.insert(1.0, self.question_data[1])  # question_text
        question_text.config(state=tk.DISABLED)

        # 答案对比
        answer_frame = ttk.LabelFrame(main_frame, text="答案对比", padding=10)
        answer_frame.pack(fill=tk.X, pady=(0, 10))

        # 我的答案
        my_answer_frame = ttk.Frame(answer_frame)
        my_answer_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(my_answer_frame, text="我的答案：", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        user_answer = self.question_data[4]  # user_answer
        ttk.Label(my_answer_frame, text=f"✗ {user_answer}",
                 foreground="red", font=("Arial", 10)).pack(side=tk.LEFT, padx=(5, 0))

        # 正确答案
        correct_answer_frame = ttk.Frame(answer_frame)
        correct_answer_frame.pack(fill=tk.X)

        ttk.Label(correct_answer_frame, text="正确答案：", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        correct_answer = self.question_data[3]  # correct_answer
        ttk.Label(correct_answer_frame, text=f"✓ {correct_answer}",
                 foreground="green", font=("Arial", 10)).pack(side=tk.LEFT, padx=(5, 0))

        # 解析内容
        explanation_frame = ttk.LabelFrame(main_frame, text="详细解析", padding=10)
        explanation_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        explanation_text = scrolledtext.ScrolledText(explanation_frame, wrap=tk.WORD,
                                                   font=("Arial", 10), height=8)
        explanation_text.pack(fill=tk.BOTH, expand=True)

        explanation = self.question_data[5] if len(self.question_data) > 5 and self.question_data[5] else '暂无解析'
        explanation_text.insert(1.0, explanation)
        explanation_text.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        is_favorite = bool(self.question_data[6]) if len(self.question_data) > 6 else False
        favorite_text = "⭐ 取消收藏" if is_favorite else "⭐ 添加收藏"
        ttk.Button(button_frame, text=favorite_text,
                  command=self.toggle_favorite).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(button_frame, text="🗑️ 删除错题",
                  command=self.delete_question).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)

    def get_question_type_name(self, question_type):
        """获取题目类型名称"""
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        return type_names.get(question_type, '未知题型')

    def toggle_favorite(self):
        """切换收藏状态"""
        try:
            # 这里需要访问错题管理器，暂时显示提示
            messagebox.showinfo("提示", "收藏功能需要在错题本界面中操作")
        except Exception as e:
            messagebox.showerror("错误", f"操作失败：{str(e)}")

    def delete_question(self):
        """删除错题"""
        if messagebox.askyesno("确认删除", "确定要删除这道错题吗？"):
            try:
                # 这里需要访问错题管理器，暂时显示提示
                messagebox.showinfo("提示", "删除功能需要在错题本界面中操作")
            except Exception as e:
                messagebox.showerror("错误", f"删除失败：{str(e)}")

    def show_favorites_panel(self):
        """显示收藏题目面板"""
        # 清空内容面板
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # 创建收藏题目界面
        fav_frame = ttk.Frame(self.content_frame)
        fav_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(fav_frame, text="收藏题目", font=("Arial", 16, "bold")).pack(pady=(0, 10))

        # 收藏题目列表
        columns = ('ID', '题目', '类型', '正确答案', '我的答案', '时间')
        tree = ttk.Treeview(fav_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            if col == 'ID':
                tree.column(col, width=50)
            elif col == '类型':
                tree.column(col, width=80)
            elif col == '题目':
                tree.column(col, width=300)

        # 获取收藏题目
        favorites = self.wrong_question_manager.get_favorite_questions()
        for question in favorites:
            tree.insert('', tk.END, values=(
                question[0], question[1][:50] + "..." if len(question[1]) > 50 else question[1],
                question[2], question[3], question[4], question[7]
            ))

        tree.pack(fill=tk.BOTH, expand=True)

        if not favorites:
            ttk.Label(fav_frame, text="暂无收藏题目", font=("Arial", 12)).pack(pady=20)
