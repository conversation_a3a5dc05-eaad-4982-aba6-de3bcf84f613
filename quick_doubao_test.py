#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速豆包API测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_doubao_config():
    """测试豆包配置"""
    try:
        from src.utils.config_manager import ConfigManager
        from src.api.ai_manager import AIManager
        
        print("=== 豆包配置测试 ===")
        
        # 测试配置管理器
        config = ConfigManager()
        api_config = config.get_api_config()
        
        print(f"豆包API密钥: {api_config['doubao_api_key'][:10]}...")
        print(f"豆包端点: {api_config['doubao_endpoint']}")
        print(f"豆包模型: {api_config['doubao_model']}")
        print()
        
        # 测试AI管理器
        ai_manager = AIManager(config)
        clients = ai_manager.get_available_clients()
        print(f"可用客户端: {clients}")
        
        if 'doubao' in clients:
            print("✓ 豆包客户端已初始化")
            
            # 获取当前模型
            current_model = ai_manager.get_client_model('doubao')
            print(f"当前豆包模型: {current_model}")
            
            # 测试连接
            print("\n正在测试豆包连接...")
            success = ai_manager.test_client_connection('doubao')
            
            if success:
                print("✓ 豆包连接测试成功！")
                return True
            else:
                print("✗ 豆包连接测试失败")
                return False
        else:
            print("✗ 豆包客户端未初始化")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_doubao_config()
    
    if success:
        print("\n🎉 豆包配置正确！可以在考试系统中使用了。")
    else:
        print("\n❌ 豆包配置有问题，请检查:")
        print("1. 确保使用正确的推理接入点ID")
        print("2. 检查API密钥是否有效")
        print("3. 确认推理接入点已启用")
        print("4. 检查账户余额")
