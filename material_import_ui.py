#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
材料导入用户界面组件
基于tkinter的GUI组件，可集成到其他系统中
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
from typing import Callable, Optional
from .material_import_core import MaterialManager, MaterialImporter


class MaterialImportDialog:
    """材料导入对话框"""
    
    def __init__(self, parent, db_connection=None, callback: Optional[Callable] = None):
        """
        初始化材料导入对话框
        
        Args:
            parent: 父窗口
            db_connection: 数据库连接
            callback: 导入成功后的回调函数
        """
        self.parent = parent
        self.db = db_connection
        self.callback = callback
        self.importer = MaterialImporter(db_connection)
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("导入学习材料")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 导入方式选择
        ttk.Label(main_frame, text="选择导入方式:", font=("Arial", 12, "bold")).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # 文件导入按钮
        file_frame = ttk.LabelFrame(main_frame, text="文件导入", padding="10")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(file_frame, text="导入文本文件 (.txt)", command=self.import_text_file, width=20).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(file_frame, text="导入PDF文件 (.pdf)", command=self.import_pdf_file, width=20).grid(row=0, column=1)
        
        # 手动输入
        manual_frame = ttk.LabelFrame(main_frame, text="手动输入", padding="10")
        manual_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 标题输入
        ttk.Label(manual_frame, text="标题:").grid(row=0, column=0, sticky=tk.W)
        self.title_var = tk.StringVar()
        title_entry = ttk.Entry(manual_frame, textvariable=self.title_var, width=50)
        title_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 内容输入
        ttk.Label(manual_frame, text="内容:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=(5, 0))
        self.content_text = scrolledtext.ScrolledText(manual_frame, width=50, height=15, wrap=tk.WORD)
        self.content_text.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="保存手动输入", command=self.save_manual_input).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空", command=self.clear_manual_input).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=self.dialog.destroy).pack(side=tk.RIGHT)
        
        # 配置网格权重
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        manual_frame.columnconfigure(1, weight=1)
        manual_frame.rowconfigure(1, weight=1)
    
    def import_text_file(self):
        """导入文本文件"""
        file_path = filedialog.askopenfilename(
            title="选择文本文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                result = self.importer.import_text_file(file_path)
                if result['success']:
                    messagebox.showinfo("成功", f"文本文件导入成功！\n标题: {result['title']}\n内容长度: {result['file_size']} 字符")
                    if self.callback:
                        self.callback()
                    self.dialog.destroy()
                else:
                    messagebox.showerror("错误", result['error'])
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {str(e)}")
    
    def import_pdf_file(self):
        """导入PDF文件"""
        file_path = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                result = self.importer.import_pdf_file(file_path)
                if result['success']:
                    messagebox.showinfo("成功", f"PDF文件导入成功！\n标题: {result['title']}\n页数: {result['page_count']}\n内容长度: {result['file_size']} 字符")
                    if self.callback:
                        self.callback()
                    self.dialog.destroy()
                else:
                    messagebox.showerror("错误", result['error'])
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {str(e)}")
    
    def save_manual_input(self):
        """保存手动输入的内容"""
        title = self.title_var.get().strip()
        content = self.content_text.get(1.0, tk.END).strip()
        
        if not title:
            messagebox.showwarning("警告", "请输入标题！")
            return
        
        if not content:
            messagebox.showwarning("警告", "请输入内容！")
            return
        
        try:
            result = self.importer.import_from_text(title, content)
            if result['success']:
                messagebox.showinfo("成功", f"材料保存成功！\n标题: {result['title']}\n内容长度: {result['file_size']} 字符")
                if self.callback:
                    self.callback()
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", result['error'])
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def clear_manual_input(self):
        """清空手动输入的内容"""
        self.title_var.set("")
        self.content_text.delete(1.0, tk.END)


class MaterialListWidget:
    """材料列表组件"""
    
    def __init__(self, parent, db_connection=None):
        """
        初始化材料列表组件
        
        Args:
            parent: 父容器
            db_connection: 数据库连接
        """
        self.parent = parent
        self.db = db_connection
        self.manager = MaterialManager(db_connection)
        
        self.setup_ui()
        self.refresh_list()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 工具栏
        toolbar = ttk.Frame(self.main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar, text="导入材料", command=self.show_import_dialog).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="刷新", command=self.refresh_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="删除", command=self.delete_selected).pack(side=tk.LEFT, padx=(0, 10))
        
        # 搜索框
        search_frame = ttk.Frame(toolbar)
        search_frame.pack(side=tk.RIGHT)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT)
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # 材料列表
        list_frame = ttk.Frame(self.main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('ID', '标题', '类型', '创建时间')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)
        
        # 调整列宽
        self.tree.column('ID', width=50)
        self.tree.column('标题', width=300)
        self.tree.column('类型', width=80)
        self.tree.column('创建时间', width=150)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.tree.bind('<Double-1>', self.on_item_double_click)
        
        # 内容预览
        preview_frame = ttk.LabelFrame(self.main_frame, text="内容预览", padding="5")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.preview_text = scrolledtext.ScrolledText(preview_frame, height=8, wrap=tk.WORD, state=tk.DISABLED)
        self.preview_text.pack(fill=tk.BOTH, expand=True)
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_item_select)
    
    def show_import_dialog(self):
        """显示导入对话框"""
        MaterialImportDialog(self.parent, self.db, self.refresh_list)
    
    def refresh_list(self):
        """刷新材料列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            materials = self.manager.get_all_materials()
            for material in materials:
                self.tree.insert('', tk.END, values=(
                    material['id'],
                    material['title'],
                    material['file_type'],
                    material['created_at']
                ))
        except Exception as e:
            messagebox.showerror("错误", f"刷新列表失败: {str(e)}")
    
    def on_search(self, event=None):
        """搜索事件处理"""
        keyword = self.search_var.get().strip()
        
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            if keyword:
                materials = self.manager.search_materials(keyword)
            else:
                materials = self.manager.get_all_materials()
            
            for material in materials:
                self.tree.insert('', tk.END, values=(
                    material['id'],
                    material['title'],
                    material['file_type'],
                    material['created_at']
                ))
        except Exception as e:
            messagebox.showerror("错误", f"搜索失败: {str(e)}")
    
    def on_item_select(self, event):
        """项目选择事件处理"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            material_id = item['values'][0]
            
            try:
                material = self.manager.get_material_by_id(material_id)
                if material:
                    self.preview_text.config(state=tk.NORMAL)
                    self.preview_text.delete(1.0, tk.END)
                    self.preview_text.insert(1.0, material['content'])
                    self.preview_text.config(state=tk.DISABLED)
            except Exception as e:
                messagebox.showerror("错误", f"加载内容失败: {str(e)}")
    
    def on_item_double_click(self, event):
        """双击事件处理"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            material_id = item['values'][0]
            
            try:
                material = self.manager.get_material_by_id(material_id)
                if material:
                    # 显示材料详情对话框
                    MaterialDetailDialog(self.parent, material, self.manager, self.refresh_list)
            except Exception as e:
                messagebox.showerror("错误", f"打开详情失败: {str(e)}")
    
    def delete_selected(self):
        """删除选中的材料"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的材料！")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的材料吗？"):
            item = self.tree.item(selection[0])
            material_id = item['values'][0]
            
            try:
                if self.manager.delete_material(material_id):
                    messagebox.showinfo("成功", "材料删除成功！")
                    self.refresh_list()
                    
                    # 清空内容预览
                    self.preview_text.config(state=tk.NORMAL)
                    self.preview_text.delete(1.0, tk.END)
                    self.preview_text.config(state=tk.DISABLED)
                else:
                    messagebox.showerror("错误", "删除失败！")
            except Exception as e:
                messagebox.showerror("错误", f"删除材料失败: {str(e)}")


class MaterialDetailDialog:
    """材料详情对话框"""
    
    def __init__(self, parent, material, manager, refresh_callback):
        """
        初始化材料详情对话框
        
        Args:
            parent: 父窗口
            material: 材料数据
            manager: 材料管理器
            refresh_callback: 刷新回调函数
        """
        self.parent = parent
        self.material = material
        self.manager = manager
        self.refresh_callback = refresh_callback
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"材料详情 - {material['title']}")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题编辑
        ttk.Label(main_frame, text="标题:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.title_var = tk.StringVar(value=self.material['title'])
        title_entry = ttk.Entry(main_frame, textvariable=self.title_var, width=60)
        title_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 内容编辑
        ttk.Label(main_frame, text="内容:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=(5, 0))
        self.content_text = scrolledtext.ScrolledText(main_frame, width=60, height=20, wrap=tk.WORD)
        self.content_text.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        self.content_text.insert(1.0, self.material['content'])
        
        # 信息显示
        info_frame = ttk.LabelFrame(main_frame, text="材料信息", padding="5")
        info_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Label(info_frame, text=f"ID: {self.material['id']}").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"类型: {self.material['file_type']}").grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        ttk.Label(info_frame, text=f"创建时间: {self.material['created_at']}").grid(row=1, column=0, columnspan=2, sticky=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="保存", command=self.save_changes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
    
    def save_changes(self):
        """保存更改"""
        title = self.title_var.get().strip()
        content = self.content_text.get(1.0, tk.END).strip()
        
        if not title:
            messagebox.showwarning("警告", "请输入标题！")
            return
        
        if not content:
            messagebox.showwarning("警告", "请输入内容！")
            return
        
        try:
            if self.manager.update_material(self.material['id'], title, content):
                messagebox.showinfo("成功", "材料更新成功！")
                self.refresh_callback()
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "更新失败！")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")


def main():
    """主函数 - 演示UI组件"""
    import sqlite3
    from database_setup import DatabaseSetup
    
    # 创建数据库
    db_setup = DatabaseSetup("data/materials.db")
    conn = db_setup.create_database()
    
    # 创建主窗口
    root = tk.Tk()
    root.title("材料导入系统演示")
    root.geometry("800x600")
    
    # 创建材料列表组件
    material_list = MaterialListWidget(root, conn)
    
    # 运行主循环
    root.mainloop()
    
    # 关闭数据库连接
    conn.close()


if __name__ == "__main__":
    main()
