#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段智能化功能测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_intelligence_features():
    """测试第二阶段智能化功能"""
    print("🧠 第二阶段智能化功能测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 学习分析系统
    print("1️⃣ 测试学习分析系统...")
    try:
        # 创建模拟数据库管理器
        class MockDBManager:
            def get_connection(self):
                import sqlite3
                conn = sqlite3.connect(':memory:')
                return conn
                
        db_manager = MockDBManager()
        
        from src.core.learning_analytics import LearningAnalytics
        analytics = LearningAnalytics(db_manager)
        
        print("   ✅ 学习分析系统初始化成功")
        
        # 测试基本功能
        stats = analytics.get_learning_statistics()
        print(f"   📊 学习统计获取成功: {type(stats)}")
        
        success_count += 1
    except Exception as e:
        print(f"   ❌ 学习分析系统测试失败: {e}")
    
    # 测试2: 智能助手
    print("\n2️⃣ 测试智能助手...")
    try:
        from src.core.intelligent_assistant import IntelligentAssistant
        
        # 使用之前创建的分析系统
        if 'analytics' in locals():
            assistant = IntelligentAssistant(analytics)
            
            # 测试基本对话
            response = assistant.process_user_query("你好")
            print(f"   ✅ 智能助手创建成功")
            print(f"   💬 测试对话: {response.get('type', 'unknown')}")
            
            # 测试学习状态查询
            response2 = assistant.process_user_query("我的学习进度如何")
            print(f"   📊 学习状态查询: {response2.get('type', 'unknown')}")
            
            success_count += 1
        else:
            print("   ❌ 智能助手测试失败: 依赖学习分析系统")
            
    except Exception as e:
        print(f"   ❌ 智能助手测试失败: {e}")
    
    # 测试3: 智能出题系统
    print("\n3️⃣ 测试智能出题系统...")
    try:
        from src.core.intelligent_question_generator import IntelligentQuestionGenerator
        
        if 'analytics' in locals():
            generator = IntelligentQuestionGenerator(db_manager, analytics)
            
            print("   ✅ 智能出题系统初始化成功")
            
            # 测试能力评估
            ability = generator._estimate_user_ability({}, ['数学'])
            print(f"   🎯 能力评估测试: {ability:.2f}")
            
            # 测试策略生成
            strategy = generator._generate_question_strategy(0.6, 10, ['数学'], {})
            print(f"   📋 策略生成测试: {len(strategy)} 个配置项")
            
            success_count += 1
        else:
            print("   ❌ 智能出题系统测试失败: 依赖学习分析系统")
            
    except Exception as e:
        print(f"   ❌ 智能出题系统测试失败: {e}")
    
    # 测试4: UI组件
    print("\n4️⃣ 测试UI组件...")
    try:
        import tkinter as tk
        
        # 测试助手窗口组件
        root = tk.Tk()
        root.withdraw()
        
        from src.ui.assistant_window import AssistantWindow
        
        if 'assistant' in locals():
            # 不实际显示窗口，只测试创建
            print("   ✅ 助手窗口组件导入成功")
            success_count += 1
        else:
            print("   ❌ UI组件测试失败: 依赖智能助手")
            
        root.destroy()
        
    except Exception as e:
        print(f"   ❌ UI组件测试失败: {e}")
    
    # 测试结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有智能化功能测试通过！")
        print("✅ 第二阶段智能化优化成功完成")
    elif success_count >= 2:
        print("⚠️ 大部分功能正常，可以继续使用")
        print("💡 建议安装可选依赖以获得完整功能")
    else:
        print("❌ 多个功能存在问题，需要检查")
    
    print("\n🧠 智能化功能说明：")
    print("📊 学习分析: 深度分析学习数据，提供个性化洞察")
    print("🤖 智能助手: 24/7学习伙伴，回答问题提供建议")
    print("🎯 智能出题: 基于能力水平生成个性化试题")
    print("📈 数据可视化: 直观展示学习进度和趋势")
    
    print("\n🚀 现在可以启动完整系统体验智能化功能：")
    print("   python main_optimized.py")
    print("=" * 50)

def test_dependencies():
    """测试依赖项"""
    print("🔍 检查依赖项...")
    
    dependencies = {
        'numpy': '数值计算（可选）',
        'matplotlib': '图表绘制（可选）',
        'pandas': '数据处理（可选）',
        'seaborn': '高级图表（可选）'
    }
    
    available = []
    missing = []
    
    for dep, desc in dependencies.items():
        try:
            __import__(dep)
            available.append(f"✅ {dep}: {desc}")
        except ImportError:
            missing.append(f"❌ {dep}: {desc}")
    
    print("\n📦 可用依赖:")
    for item in available:
        print(f"   {item}")
    
    if missing:
        print("\n⚠️ 缺失依赖:")
        for item in missing:
            print(f"   {item}")
        print("\n💡 提示: 这些是可选依赖，系统会使用替代方案")
        print("   如需完整功能，可以安装: pip install numpy matplotlib pandas seaborn")
    else:
        print("\n🎉 所有依赖都已安装！")

if __name__ == "__main__":
    print("🧠 第二阶段智能化功能测试")
    print("测试内容：学习分析、智能助手、智能出题、UI组件")
    print("=" * 50)
    
    # 先检查依赖
    test_dependencies()
    print()
    
    # 然后测试功能
    test_intelligence_features()
