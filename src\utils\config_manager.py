#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责读取和管理系统配置
"""

import configparser
import os

class ConfigManager:
    def __init__(self, config_file='config.ini'):
        """初始化配置管理器"""
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config['API'] = {
            'openai_api_key': 'your_openai_api_key_here',
            'openai_base_url': 'https://api.openai.com',
            'openai_model': 'gpt-3.5-turbo',
            'deepseek_api_key': 'your_deepseek_api_key_here',
            'deepseek_base_url': 'https://api.deepseek.com',
            'deepseek_model': 'deepseek-chat',
            'gemini_api_key': 'your_gemini_api_key_here',
            'gemini_model': 'gemini-pro',
            'doubao_api_key': 'your_doubao_api_key_here',
            'doubao_endpoint': 'your_doubao_endpoint_here',
            'doubao_model': 'doubao-seed-1-6-flash-250615',
            'relay_api_key': 'your_relay_api_key_here',
            'relay_base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1',
            'relay_model': 'qwen-turbo'
        }
        
        self.config['DATABASE'] = {
            'db_path': 'data/exam_system.db'
        }
        
        self.config['EXAM'] = {
            'default_exam_time': '60',
            'questions_per_exam': '20',
            'auto_save_interval': '30'
        }
        
        self.config['UI'] = {
            'window_width': '1200',
            'window_height': '800',
            'theme': 'light',
            'font_size': '12'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get(self, section, key, fallback=None):
        """获取配置值"""
        return self.config.get(section, key, fallback=fallback)
    
    def getint(self, section, key, fallback=0):
        """获取整数配置值"""
        return self.config.getint(section, key, fallback=fallback)
    
    def getboolean(self, section, key, fallback=False):
        """获取布尔配置值"""
        return self.config.getboolean(section, key, fallback=fallback)
    
    def set(self, section, key, value):
        """设置配置值"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))
        self.save_config()
    
    def get_api_config(self):
        """获取API配置"""
        return {
            'openai_api_key': self.get('API', 'openai_api_key'),
            'openai_base_url': self.get('API', 'openai_base_url'),
            'openai_model': self.get('API', 'openai_model'),
            'deepseek_api_key': self.get('API', 'deepseek_api_key'),
            'deepseek_base_url': self.get('API', 'deepseek_base_url'),
            'deepseek_model': self.get('API', 'deepseek_model'),
            'gemini_api_key': self.get('API', 'gemini_api_key'),
            'gemini_model': self.get('API', 'gemini_model'),
            'doubao_api_key': self.get('API', 'doubao_api_key'),
            'doubao_endpoint': self.get('API', 'doubao_endpoint'),
            'doubao_model': self.get('API', 'doubao_model'),
            'relay_api_key': self.get('API', 'relay_api_key'),
            'relay_base_url': self.get('API', 'relay_base_url'),
            'relay_model': self.get('API', 'relay_model')
        }
    
    def get_ui_config(self):
        """获取UI配置"""
        return {
            'window_width': self.getint('UI', 'window_width', 1200),
            'window_height': self.getint('UI', 'window_height', 800),
            'theme': self.get('UI', 'theme', 'light'),
            'font_size': self.getint('UI', 'font_size', 12)
        }
