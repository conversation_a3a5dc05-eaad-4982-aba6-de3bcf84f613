#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化AI考试生成系统演示
展示灵动风格UI的所有功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def show_welcome_dialog():
    """显示欢迎对话框"""
    msg = QMessageBox()
    msg.setWindowTitle("🎨 现代化AI考试生成系统")
    msg.setIcon(QMessageBox.Icon.Information)
    
    welcome_text = """
🎉 欢迎使用现代化AI考试生成系统！

✨ 主要特性：
• 🎨 灵动风格UI设计
• 🌈 半透明毛玻璃背景
• 🎯 蓝紫渐变色彩方案
• 💫 涟漪动效按钮
• 📊 实时预览更新
• 🎛️ 直观的参数控制

🚀 操作指南：
1. 调整左侧参数设置
2. 观察右侧实时预览
3. 点击按钮体验涟漪效果
4. 尝试不同的题型和难度组合

💡 设计亮点：
• 严格按照用户指定的色值 (#219be4 到 #7338ab)
• 16px圆角和毛玻璃效果
• 灵动岛风格导航栏
• 现代化卡片式布局
"""
    
    msg.setText(welcome_text)
    msg.setStandardButtons(QMessageBox.StandardButton.Ok)
    msg.exec()

def main():
    """主函数"""
    print("🎨 启动现代化AI考试生成系统演示...")
    print("=" * 60)
    
    # 检查PyQt6
    try:
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6 已安装")
    except ImportError:
        print("❌ PyQt6 未安装")
        print("请运行: pip install PyQt6")
        input("按回车键退出...")
        return
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 设置应用属性
    app.setApplicationName("现代化AI考试生成系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("AI Education")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    print("🚀 启动灵动风格界面...")
    
    try:
        # 导入并创建主窗口
        from src.ui.modern_exam_generator import ModernExamGenerator
        
        # 创建主窗口
        window = ModernExamGenerator()
        
        # 显示欢迎对话框
        show_welcome_dialog()
        
        # 显示主窗口
        window.show()
        
        print("✅ 界面启动成功！")
        print("\n🎯 功能演示：")
        print("• 拖拽滑块调整参数")
        print("• 观察右侧预览实时更新")
        print("• 点击按钮查看涟漪效果")
        print("• 尝试不同的主题色彩")
        print("\n💡 按 Ctrl+C 退出程序")
        
        # 运行应用
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("\n可能的解决方案:")
        print("1. 确保文件路径正确")
        print("2. 检查 src/ui/modern_exam_generator.py 是否存在")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n详细错误信息:")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
