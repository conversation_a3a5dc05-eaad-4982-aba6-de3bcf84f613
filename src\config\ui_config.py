#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI配置文件
管理界面相关的配置选项
"""

class UIConfig:
    """UI配置类"""
    
    # 考试界面配置
    EXAM_INTERFACE = {
        'default_mode': 'comfortable',  # 'comfortable' 或 'traditional'
        'show_interface_choice': True,  # 是否显示界面选择对话框
        'remember_choice': True,        # 是否记住用户选择
    }
    
    # 舒适版界面配置
    COMFORTABLE_INTERFACE = {
        'window_size': '1200x800',
        'font_family': 'Microsoft YaHei',
        'font_sizes': {
            'title': 16,
            'subtitle': 14,
            'content': 12,
            'button': 11,
            'small': 10
        },
        'colors': {
            'current_question': '#ff6b6b',
            'answered_question': '#51cf66',
            'unanswered_question': '#f8f9fa',
            'marked_question': '#ffd43b',
            'primary': '#228be6',
            'success': '#40c057',
            'warning': '#fd7e14',
            'danger': '#fa5252'
        },
        'spacing': {
            'main_padding': 20,
            'section_padding': 15,
            'item_padding': 10,
            'button_padding': 8
        }
    }
    
    # 传统界面配置
    TRADITIONAL_INTERFACE = {
        'window_size': '1000x700',
        'font_family': 'Arial',
        'font_sizes': {
            'title': 14,
            'subtitle': 12,
            'content': 10,
            'button': 9,
            'small': 8
        },
        'spacing': {
            'main_padding': 10,
            'section_padding': 10,
            'item_padding': 5,
            'button_padding': 5
        }
    }
    
    # 试卷生成界面配置
    EXAM_GENERATION = {
        'auto_title_format': '{material_title} - 测试',  # 自动标题格式
        'default_exam_time': 60,                        # 默认考试时间（分钟）
        'default_question_count': 10,                   # 默认题目数量
        'show_material_preview': True,                  # 是否显示材料预览
    }
    
    # 键盘快捷键配置
    KEYBOARD_SHORTCUTS = {
        'prev_question': '<Left>',
        'next_question': '<Right>',
        'save_answer': '<Control-s>',
        'submit_exam': '<Control-Return>',
        'show_help': '<F1>',
        'quick_select_a': '<Key-1>',
        'quick_select_b': '<Key-2>',
        'quick_select_c': '<Key-3>',
        'quick_select_d': '<Key-4>',
        'mark_question': '<Control-m>',
        'clear_answer': '<Control-d>',
        'question_list': '<Control-l>'
    }
    
    # 可访问性配置
    ACCESSIBILITY = {
        'high_contrast': False,         # 高对比度模式
        'large_fonts': False,          # 大字体模式
        'screen_reader_support': True, # 屏幕阅读器支持
        'keyboard_navigation': True,   # 键盘导航
    }
    
    @classmethod
    def get_exam_interface_config(cls, mode='comfortable'):
        """获取考试界面配置"""
        if mode == 'comfortable':
            return cls.COMFORTABLE_INTERFACE
        else:
            return cls.TRADITIONAL_INTERFACE
    
    @classmethod
    def get_font_config(cls, mode='comfortable', size_type='content'):
        """获取字体配置"""
        config = cls.get_exam_interface_config(mode)
        return {
            'family': config['font_family'],
            'size': config['font_sizes'].get(size_type, 12)
        }
    
    @classmethod
    def get_color_config(cls, color_type='primary'):
        """获取颜色配置"""
        return cls.COMFORTABLE_INTERFACE['colors'].get(color_type, '#228be6')
    
    @classmethod
    def get_spacing_config(cls, mode='comfortable', spacing_type='main_padding'):
        """获取间距配置"""
        config = cls.get_exam_interface_config(mode)
        return config['spacing'].get(spacing_type, 10)
    
    @classmethod
    def format_exam_title(cls, material_title):
        """格式化考试标题"""
        format_template = cls.EXAM_GENERATION['auto_title_format']
        return format_template.format(material_title=material_title)
    
    @classmethod
    def get_keyboard_shortcut(cls, action):
        """获取键盘快捷键"""
        return cls.KEYBOARD_SHORTCUTS.get(action, '')
    
    @classmethod
    def is_accessibility_enabled(cls, feature):
        """检查可访问性功能是否启用"""
        return cls.ACCESSIBILITY.get(feature, False)

# 全局UI配置实例
ui_config = UIConfig()
