print("Hello, this is a minimal test!")
print("Python is working correctly.")

import sqlite3
import os
from datetime import datetime

print("All imports successful!")

# 创建数据库
db_path = "data/exam_system.db"
os.makedirs("data", exist_ok=True)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 创建错题表
cursor.execute('''
    CREATE TABLE IF NOT EXISTS wrong_questions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        question_text TEXT NOT NULL,
        question_type TEXT NOT NULL,
        correct_answer TEXT NOT NULL,
        user_answer TEXT NOT NULL,
        explanation TEXT,
        is_favorite BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
''')

# 添加一道错题
cursor.execute('''
    INSERT INTO wrong_questions (question_text, question_type, correct_answer, user_answer, explanation, created_at)
    VALUES (?, ?, ?, ?, ?, ?)
''', ("测试错题：Python是什么？", "single_choice", "编程语言", "动物", "Python是一种编程语言", datetime.now()))

conn.commit()

# 查询错题
cursor.execute("SELECT COUNT(*) FROM wrong_questions")
count = cursor.fetchone()[0]
print(f"错题数量: {count}")

cursor.execute("SELECT * FROM wrong_questions LIMIT 1")
question = cursor.fetchone()
if question:
    print(f"错题内容: {question[1]}")

conn.close()
print("数据库操作完成！")
