#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的题目生成器
减少token消耗，提高生成效率
"""

import json
import re
from typing import List, Dict, Any
from src.api.base_ai_client import BaseAIClient

class OptimizedQuestionGenerator:
    """优化的题目生成器"""
    
    def __init__(self, ai_client: BaseAIClient):
        self.ai_client = ai_client
        self.material_cache = {}  # 材料缓存
        self.knowledge_points = []  # 提取的知识点
    
    def extract_knowledge_points(self, material: str, max_points: int = 10) -> List[str]:
        """提取材料中的关键知识点"""
        
        # 简化的prompt，只提取关键点
        prompt = f"""
请从以下材料中提取{max_points}个最重要的知识点，每个知识点用一句话概括：

材料：{material[:2000]}...

要求：
1. 每个知识点一行
2. 简洁明了
3. 涵盖核心概念
4. 格式：- 知识点内容
"""
        
        try:
            messages = [
                {"role": "user", "content": prompt}
            ]
            
            # 使用简化的生成方式
            try:
                # 尝试使用generate_questions方法
                response = self.ai_client.generate_questions(
                    material=prompt,
                    question_types=['single_choice'],
                    num_questions=1
                )
                return self._extract_points_from_response(str(response))
            except Exception as e:
                print(f"AI提取知识点失败: {e}")
                # 如果AI提取失败，使用简单的文本分析
                return self._simple_extract_points(material, max_points)
            
            # 这部分代码已经在上面的try块中处理了
            
        except Exception as e:
            print(f"提取知识点失败: {e}")
        
        # 如果AI提取失败，使用简单的文本分析
        return self._simple_extract_points(material, max_points)
    
    def _extract_points_from_response(self, content: str) -> List[str]:
        """从AI响应中提取知识点"""
        points = []
        lines = content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('-') or line.startswith('•') or line.startswith('*'):
                point = line[1:].strip()
                if point and len(point) > 10:  # 过滤太短的内容
                    points.append(point)
            elif line and not line.startswith('请') and not line.startswith('要求'):
                if len(line) > 10:
                    points.append(line)
        
        return points[:10]  # 最多返回10个
    
    def _simple_extract_points(self, material: str, max_points: int) -> List[str]:
        """简单的文本分析提取知识点"""
        # 按句子分割
        sentences = re.split(r'[。！？\n]', material)
        
        # 过滤和排序
        points = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20 and len(sentence) < 200:  # 合适长度的句子
                # 优先选择包含关键词的句子
                keywords = ['是', '包括', '具有', '特点', '方法', '原理', '定义', '概念']
                if any(keyword in sentence for keyword in keywords):
                    points.append(sentence)
        
        return points[:max_points]
    
    def generate_questions_optimized(self, material: str, question_types: List[str], 
                                   num_questions: int = 20) -> List[Dict[str, Any]]:
        """优化的题目生成"""
        
        # 策略1: 分批生成，减少单次token消耗
        if num_questions > 10:
            return self._batch_generate(material, question_types, num_questions)
        
        # 策略2: 使用知识点而不是完整材料
        knowledge_points = self.extract_knowledge_points(material, min(num_questions, 8))
        
        if not knowledge_points:
            # 如果知识点提取失败，使用材料摘要
            return self._generate_with_summary(material, question_types, num_questions)
        
        # 使用知识点生成题目
        return self._generate_from_knowledge_points(knowledge_points, question_types, num_questions)
    
    def _batch_generate(self, material: str, question_types: List[str], 
                       num_questions: int) -> List[Dict[str, Any]]:
        """分批生成题目"""
        
        batch_size = 5  # 每批生成5道题
        all_questions = []
        
        # 提取知识点
        knowledge_points = self.extract_knowledge_points(material, num_questions)
        
        for i in range(0, num_questions, batch_size):
            batch_num = min(batch_size, num_questions - i)
            
            # 为这一批选择知识点
            start_idx = i * len(knowledge_points) // num_questions
            end_idx = (i + batch_num) * len(knowledge_points) // num_questions
            batch_points = knowledge_points[start_idx:end_idx]
            
            if not batch_points and knowledge_points:
                batch_points = knowledge_points[:2]  # 至少使用2个知识点
            
            print(f"生成第{i+1}-{i+batch_num}题...")
            
            try:
                batch_questions = self._generate_from_knowledge_points(
                    batch_points, question_types, batch_num
                )
                all_questions.extend(batch_questions)
                
            except Exception as e:
                print(f"批次生成失败: {e}")
                # 如果批次生成失败，尝试单个生成
                for j in range(batch_num):
                    try:
                        single_q = self._generate_single_question(
                            batch_points[j % len(batch_points)] if batch_points else material[:500],
                            question_types[j % len(question_types)]
                        )
                        if single_q:
                            all_questions.append(single_q)
                    except:
                        continue
        
        return all_questions
    
    def _generate_from_knowledge_points(self, knowledge_points: List[str], 
                                      question_types: List[str], num_questions: int) -> List[Dict[str, Any]]:
        """基于知识点生成题目"""
        
        # 构建简化的prompt
        points_text = '\n'.join(f"{i+1}. {point}" for i, point in enumerate(knowledge_points))
        
        type_descriptions = {
            'single_choice': '单选题(4个选项,答案用A/B/C/D)',
            'multiple_choice': '多选题(4个选项,答案用["A","B"]格式)',
            'true_false': '判断题(答案用"对"或"错")',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        
        selected_types = [type_descriptions.get(t, t) for t in question_types]
        
        prompt = f"""基于以下知识点生成{num_questions}道题目：

知识点：
{points_text}

题型：{', '.join(selected_types)}

返回JSON格式：
[{{"type":"题型","question":"题目","options":["A","B","C","D"],"correct_answer":"答案","explanation":"解析","score":分值}}]

要求：
1. 题目基于知识点
2. 难度适中
3. 解析简洁
4. 必须返回有效JSON"""
        
        try:
            # 使用现有的generate_questions接口
            return self.ai_client.generate_questions(
                material=prompt,
                question_types=question_types,
                num_questions=num_questions
            )
                
        except Exception as e:
            print(f"基于知识点生成失败: {e}")
            return []
        
        return []
    
    def _generate_with_summary(self, material: str, question_types: List[str], 
                             num_questions: int) -> List[Dict[str, Any]]:
        """使用材料摘要生成题目"""
        
        # 创建材料摘要（限制长度）
        summary = material[:1000] + "..." if len(material) > 1000 else material
        
        return self.ai_client.generate_questions(
            material=summary,
            question_types=question_types,
            num_questions=num_questions
        )
    
    def _generate_single_question(self, knowledge_point: str, question_type: str) -> Dict[str, Any]:
        """生成单个题目"""
        
        type_map = {
            'single_choice': '单选题，提供4个选项',
            'multiple_choice': '多选题，提供4个选项',
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        
        prompt = f"""基于知识点生成1道{type_map.get(question_type, question_type)}：

知识点：{knowledge_point}

返回JSON：
{{"type":"{question_type}","question":"题目内容","correct_answer":"答案","explanation":"简短解析","score":2}}"""
        
        try:
            # 使用现有接口生成单个题目
            questions = self.ai_client.generate_questions(
                material=prompt,
                question_types=[question_type],
                num_questions=1
            )
            return questions[0] if questions else None
            
        except Exception as e:
            print(f"单题生成失败: {e}")
        
        return None
    
    def _parse_questions_response(self, response: str) -> List[Dict[str, Any]]:
        """解析题目响应"""
        try:
            # 尝试直接解析JSON
            questions = json.loads(response)
            if isinstance(questions, dict):
                questions = [questions]
            return questions
        except json.JSONDecodeError:
            # 尝试提取JSON部分
            import re
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                try:
                    questions = json.loads(json_match.group())
                    return questions
                except json.JSONDecodeError:
                    pass
            
            # 尝试提取单个JSON对象
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                try:
                    question = json.loads(json_match.group())
                    return [question]
                except json.JSONDecodeError:
                    pass
        
        return []
    
    def estimate_tokens(self, material: str, num_questions: int) -> int:
        """估算token消耗"""
        # 粗略估算：1个中文字符约等于1.5个token
        material_tokens = len(material) * 1.5
        prompt_tokens = 500  # 基础prompt
        output_tokens = num_questions * 150  # 每题约150token
        
        return int(material_tokens + prompt_tokens + output_tokens)
    
    def get_optimization_suggestion(self, material: str, num_questions: int) -> str:
        """获取优化建议"""
        estimated_tokens = self.estimate_tokens(material, num_questions)
        
        if estimated_tokens > 10000:
            return f"预计消耗{estimated_tokens}tokens，建议：\n1. 减少题目数量到10题以下\n2. 使用分批生成\n3. 启用知识点提取模式"
        elif estimated_tokens > 5000:
            return f"预计消耗{estimated_tokens}tokens，建议使用知识点提取模式"
        else:
            return f"预计消耗{estimated_tokens}tokens，可以直接生成"
