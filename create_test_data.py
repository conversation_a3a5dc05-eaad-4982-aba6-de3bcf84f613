#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试数据
"""

import sqlite3
import json
import os
from datetime import datetime

def create_test_data():
    """创建测试数据"""
    
    # 确保数据目录存在
    if not os.path.exists("data"):
        os.makedirs("data")
    
    db_path = "data/exam_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 创建测试数据 ===")
        
        # 创建表（如果不存在）
        print("1. 创建数据库表...")
        
        # 材料表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS materials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                type TEXT DEFAULT 'text',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 考试表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                time_limit INTEGER DEFAULT 60,
                questions TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 考试记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exam_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exam_id INTEGER,
                answers TEXT,
                score REAL,
                total_score REAL,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (exam_id) REFERENCES exams (id)
            )
        ''')
        
        print("✓ 数据库表创建完成")
        
        # 创建测试材料
        print("2. 创建测试材料...")
        
        test_material = """
Python编程基础知识

Python是一种高级编程语言，具有以下特点：
1. 简洁易读的语法
2. 跨平台兼容性
3. 丰富的标准库和第三方库
4. 支持面向对象编程
5. 动态类型系统

Python的基本数据类型包括：
- 数字类型：int, float, complex
- 字符串类型：str
- 布尔类型：bool
- 列表类型：list
- 元组类型：tuple
- 字典类型：dict
- 集合类型：set

Python常用的控制结构：
- 条件语句：if, elif, else
- 循环语句：for, while
- 异常处理：try, except, finally

Python函数定义使用def关键字，支持默认参数、可变参数等特性。
        """
        
        cursor.execute(
            "INSERT INTO materials (title, content, type) VALUES (?, ?, ?)",
            ("Python编程基础", test_material, "text")
        )
        
        print("✓ 测试材料创建完成")
        
        # 创建测试考试
        print("3. 创建测试考试...")
        
        test_questions = [
            {
                "type": "single_choice",
                "question": "Python是什么类型的编程语言？",
                "options": ["编译型语言", "解释型语言", "汇编语言", "机器语言"],
                "correct_answer": "B",
                "explanation": "Python是解释型语言，代码在运行时被解释器逐行执行。",
                "score": 2
            },
            {
                "type": "multiple_choice", 
                "question": "Python的基本数据类型包括哪些？",
                "options": ["int", "str", "list", "file"],
                "correct_answer": ["A", "B", "C"],
                "explanation": "Python的基本数据类型包括int、str、list等，但file不是基本数据类型。",
                "score": 3
            },
            {
                "type": "true_false",
                "question": "Python支持面向对象编程。",
                "correct_answer": "对",
                "explanation": "Python完全支持面向对象编程，包括类、继承、多态等特性。",
                "score": 1
            },
            {
                "type": "short_answer",
                "question": "请列举Python的三个主要特点。",
                "correct_answer": "简洁易读、跨平台、丰富的库",
                "explanation": "Python的主要特点包括语法简洁易读、跨平台兼容、拥有丰富的库生态等。",
                "score": 3
            }
        ]
        
        questions_json = json.dumps(test_questions, ensure_ascii=False)
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.execute(
            "INSERT INTO exams (title, description, time_limit, questions, created_at) VALUES (?, ?, ?, ?, ?)",
            ("Python基础测试", "Python编程基础知识测试", 45, questions_json, current_time)
        )
        
        exam_id = cursor.lastrowid
        print(f"✓ 测试考试创建完成，ID: {exam_id}")
        
        # 再创建一个考试
        test_questions_2 = [
            {
                "type": "single_choice",
                "question": "Python中定义函数使用哪个关键字？",
                "options": ["function", "def", "func", "define"],
                "correct_answer": "B",
                "explanation": "Python中使用def关键字定义函数。",
                "score": 2
            },
            {
                "type": "true_false",
                "question": "Python是动态类型语言。",
                "correct_answer": "对",
                "explanation": "Python是动态类型语言，变量的类型在运行时确定。",
                "score": 1
            }
        ]
        
        questions_json_2 = json.dumps(test_questions_2, ensure_ascii=False)
        
        cursor.execute(
            "INSERT INTO exams (title, description, time_limit, questions, created_at) VALUES (?, ?, ?, ?, ?)",
            ("Python函数测试", "Python函数相关知识测试", 30, questions_json_2, current_time)
        )
        
        exam_id_2 = cursor.lastrowid
        print(f"✓ 第二个测试考试创建完成，ID: {exam_id_2}")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print("\n=== 测试数据创建完成 ===")
        print(f"✅ 创建了2个测试考试")
        print("现在可以在考试系统中看到这些考试了！")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_test_data()
    
    if success:
        print("\n🎉 测试数据创建成功！")
        print("请重新启动考试系统，然后点击'开始考试'查看。")
    else:
        print("\n❌ 测试数据创建失败。")
