#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试题目生成问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_question_generation():
    """测试题目生成"""
    try:
        from src.api.enhanced_doubao_client import EnhancedDoubaoClient
        from src.utils.config_manager import ConfigManager
        
        print("=== 调试题目生成 ===")
        
        # 获取配置
        config = ConfigManager()
        api_config = config.get_api_config()
        
        # 创建豆包客户端
        client = EnhancedDoubaoClient(
            api_key=api_config.get('doubao_api_key', ''),
            model="doubao-seed-1-6-flash-250615"
        )
        
        # 测试材料
        test_material = """
        Python是一种解释型、面向对象、动态数据类型的高级程序设计语言。
        Python由Guido van Ross<PERSON>于1989年底发明，第一个公开发行版发行于1991年。
        Python具有丰富和强大的库，它常被昵称为胶水语言。
        """
        
        print("测试材料长度:", len(test_material))
        
        # 生成题目
        print("\n开始生成题目...")
        questions = client.generate_questions(
            material=test_material,
            question_types=['single_choice'],
            num_questions=2
        )
        
        print(f"\n生成结果: {len(questions)} 道题目")
        
        # 详细检查每道题目
        for i, q in enumerate(questions, 1):
            print(f"\n=== 题目 {i} ===")
            print(f"类型: {q.get('type', '未知')}")
            print(f"题目: {q.get('question', '无题目')}")
            print(f"选项: {q.get('options', '无选项')}")
            print(f"答案: {q.get('correct_answer', '无答案')}")
            print(f"解析: {q.get('explanation', '无解析')}")
            print(f"分值: {q.get('score', '无分值')}")
            
            # 检查选择题的选项
            if q.get('type') in ['single_choice', 'multiple_choice']:
                options = q.get('options', [])
                if not options:
                    print("❌ 选择题缺少选项！")
                elif len(options) < 2:
                    print(f"❌ 选择题选项不足: {len(options)} < 2")
                else:
                    print(f"✅ 选择题选项正常: {len(options)} 个")
        
        return questions
        
    except Exception as e:
        print(f"❌ 题目生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_raw_api_response():
    """测试原始API响应"""
    try:
        from src.api.enhanced_doubao_client import EnhancedDoubaoClient
        from src.utils.config_manager import ConfigManager
        
        print("\n=== 测试原始API响应 ===")
        
        config = ConfigManager()
        api_config = config.get_api_config()
        
        client = EnhancedDoubaoClient(
            api_key=api_config.get('doubao_api_key', ''),
            model="doubao-seed-1-6-flash-250615"
        )
        
        # 创建简单的prompt
        prompt = """请生成1道Python单选题，格式如下：
[
    {
        "type": "single_choice",
        "question": "题目内容",
        "options": ["选项A", "选项B", "选项C", "选项D"],
        "correct_answer": "A",
        "explanation": "解析内容",
        "score": 1
    }
]"""
        
        print("发送的prompt:")
        print(prompt)
        
        # 直接调用API
        import requests
        import json
        
        url = f"{client.endpoint}/chat/completions"
        data = {
            "model": client.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        print(f"\n调用API: {url}")
        print(f"模型: {client.model}")
        
        response = requests.post(url, headers=client.headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"\n原始响应内容:")
                print(content)
                
                # 尝试解析
                questions = client.parse_questions_response(content)
                print(f"\n解析结果: {len(questions)} 道题目")
                
                for q in questions:
                    print(f"解析的题目: {q}")
                
                # 验证
                validated = client.validate_questions(questions)
                print(f"\n验证后: {len(validated)} 道题目")
                
                return validated
            else:
                print("❌ API响应格式异常")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(response.text)
        
        return []
        
    except Exception as e:
        print(f"❌ 原始API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_validation_logic():
    """测试验证逻辑"""
    try:
        from src.api.base_ai_client import BaseAIClient
        
        print("\n=== 测试验证逻辑 ===")
        
        # 创建测试题目
        test_questions = [
            {
                "type": "single_choice",
                "question": "Python是什么？",
                "options": ["编程语言", "动物", "食物", "工具"],
                "correct_answer": "A",
                "explanation": "Python是编程语言",
                "score": 1
            },
            {
                "type": "single_choice",
                "question": "没有选项的题目",
                "correct_answer": "A",
                "explanation": "测试",
                "score": 1
            },
            {
                "type": "single_choice",
                "question": "选项不足的题目",
                "options": ["只有一个选项"],
                "correct_answer": "A",
                "explanation": "测试",
                "score": 1
            }
        ]
        
        # 创建基类实例来测试验证
        class TestClient(BaseAIClient):
            def generate_questions(self, material, question_types, num_questions):
                return []
            def evaluate_answer(self, question, answer, correct_answer):
                return {}
        
        client = TestClient("test_key")
        
        print(f"原始题目数量: {len(test_questions)}")
        
        validated = client.validate_questions(test_questions)
        
        print(f"验证后题目数量: {len(validated)}")
        
        for i, q in enumerate(validated, 1):
            print(f"题目{i}: {q['question'][:20]}... 选项数: {len(q.get('options', []))}")
        
        return validated
        
    except Exception as e:
        print(f"❌ 验证逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函数"""
    print("开始调试题目生成问题...\n")
    
    # 测试题目生成
    questions1 = test_question_generation()
    
    # 测试原始API响应
    questions2 = test_raw_api_response()
    
    # 测试验证逻辑
    questions3 = test_validation_logic()
    
    print("\n=== 调试总结 ===")
    print(f"标准生成: {len(questions1)} 道题目")
    print(f"原始API: {len(questions2)} 道题目")
    print(f"验证测试: {len(questions3)} 道题目")
    
    if len(questions1) == 0:
        print("\n❌ 问题确认: 题目生成失败")
        print("可能原因:")
        print("1. API响应格式不正确")
        print("2. 选择题缺少options字段")
        print("3. 验证逻辑过于严格")
        print("4. JSON解析失败")
        
        print("\n🔧 建议解决方案:")
        print("1. 检查API响应的原始内容")
        print("2. 修改prompt确保包含options")
        print("3. 放宽验证条件")
        print("4. 添加更多调试信息")
    else:
        print("\n✅ 题目生成正常")
        
        # 检查是否有选项
        has_options = any(q.get('options') for q in questions1)
        if not has_options:
            print("⚠️ 但是选择题缺少选项")
        else:
            print("✅ 选择题选项正常")

if __name__ == "__main__":
    main()
