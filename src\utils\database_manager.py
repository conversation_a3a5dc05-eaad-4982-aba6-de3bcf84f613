#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器
负责数据库的创建、连接和基本操作
"""

import sqlite3
import os
import json
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path='data/exam_system.db'):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.ensure_data_dir()
    
    def ensure_data_dir(self):
        """确保数据目录存在"""
        data_dir = os.path.dirname(self.db_path)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """初始化数据库表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 学习材料表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS materials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                file_type TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 试卷表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                questions TEXT NOT NULL,  -- JSON格式存储题目
                time_limit INTEGER DEFAULT 60,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 考试记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exam_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exam_id INTEGER NOT NULL,
                answers TEXT NOT NULL,  -- JSON格式存储答案
                score REAL NOT NULL,
                total_score REAL NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (exam_id) REFERENCES exams (id)
            )
        ''')
        
        # 错题本表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS wrong_questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                question_text TEXT NOT NULL,
                question_type TEXT NOT NULL,
                correct_answer TEXT NOT NULL,
                user_answer TEXT NOT NULL,
                explanation TEXT,
                is_favorite BOOLEAN DEFAULT FALSE,
                exam_id INTEGER,
                exam_title TEXT,
                exam_record_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (exam_id) REFERENCES exams (id),
                FOREIGN KEY (exam_record_id) REFERENCES exam_records (id)
            )
        ''')

        # 检查并添加新字段（为了兼容旧数据）
        self._add_missing_columns(cursor)
        
        # 知识点表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_points (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                parent_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES knowledge_points (id)
            )
        ''')
        
        conn.commit()
        conn.close()

    def _add_missing_columns(self, cursor):
        """添加缺失的列"""
        try:
            # 检查错题表是否有新字段
            cursor.execute("PRAGMA table_info(wrong_questions)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]

            # 添加缺失的字段
            if 'exam_id' not in column_names:
                cursor.execute("ALTER TABLE wrong_questions ADD COLUMN exam_id INTEGER")
                print("✅ 添加 exam_id 字段")

            if 'exam_title' not in column_names:
                cursor.execute("ALTER TABLE wrong_questions ADD COLUMN exam_title TEXT")
                print("✅ 添加 exam_title 字段")

            if 'exam_record_id' not in column_names:
                cursor.execute("ALTER TABLE wrong_questions ADD COLUMN exam_record_id INTEGER")
                print("✅ 添加 exam_record_id 字段")

            if 'question_number' not in column_names:
                cursor.execute("ALTER TABLE wrong_questions ADD COLUMN question_number INTEGER")
                print("✅ 添加 question_number 字段")

        except Exception as e:
            print(f"⚠️ 添加字段时出错: {e}")
            # 不抛出异常，继续运行
    
    def execute_query(self, query, params=None):
        """执行查询"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        result = cursor.fetchall()
        conn.close()
        return result
    
    def execute_update(self, query, params=None):
        """执行更新操作"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        conn.commit()
        last_id = cursor.lastrowid
        conn.close()
        return last_id
