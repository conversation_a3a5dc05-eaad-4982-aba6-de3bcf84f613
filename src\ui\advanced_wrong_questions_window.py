#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级错题本窗口 - 全面优化版
包含详细的错题统计、试卷分析、错误次数追踪等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime, timedelta
import json

class AdvancedWrongQuestionsWindow:
    def __init__(self, parent, wrong_question_manager, exam_manager):
        self.parent = parent
        self.wrong_question_manager = wrong_question_manager
        self.exam_manager = exam_manager
        self.window = None
        self.current_view = "by_exam"  # by_exam, all, favorites, statistics
        
    def show(self):
        """显示高级错题本窗口"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("🎯 高级错题本 - 全面分析")
        self.window.geometry("1400x800")
        self.window.transient(self.parent)
        
        self.create_interface()
        self.load_exam_summary()
        
    def create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建顶部工具栏
        self.create_toolbar(main_frame)
        
        # 创建主要内容区域
        self.create_main_content(main_frame)
        
        # 创建底部状态栏
        self.create_status_bar(main_frame)
        
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧标题和统计
        left_frame = ttk.Frame(toolbar_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        title_label = ttk.Label(left_frame, text="🎯 高级错题本", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        self.stats_label = ttk.Label(left_frame, text="加载中...", 
                                    font=("Microsoft YaHei", 10))
        self.stats_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 右侧工具按钮
        right_frame = ttk.Frame(toolbar_frame)
        right_frame.pack(side=tk.RIGHT)
        
        # 视图切换按钮
        ttk.Button(right_frame, text="📋 按试卷分类", 
                  command=lambda: self.switch_view("by_exam")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_frame, text="📝 全部错题", 
                  command=lambda: self.switch_view("all")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_frame, text="⭐ 收藏题目", 
                  command=lambda: self.switch_view("favorites")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_frame, text="📊 统计分析", 
                  command=lambda: self.switch_view("statistics")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_frame, text="🔄 刷新", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(5, 0))
        
    def create_main_content(self, parent):
        """创建主要内容区域"""
        # 使用PanedWindow创建可调整大小的面板
        self.paned_window = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧试卷/分类面板
        self.create_exam_list_panel(self.paned_window)
        
        # 创建右侧错题详情面板
        self.create_question_detail_panel(self.paned_window)
        
    def create_exam_list_panel(self, parent):
        """创建试卷列表面板"""
        left_frame = ttk.Frame(parent)
        parent.add(left_frame, weight=1)
        
        # 试卷列表标题
        list_title = ttk.Label(left_frame, text="试卷列表", 
                              font=("Microsoft YaHei", 12, "bold"))
        list_title.pack(pady=(0, 10))
        
        # 试卷列表
        self.exam_listbox = tk.Listbox(left_frame, font=("Microsoft YaHei", 10))
        self.exam_listbox.pack(fill=tk.BOTH, expand=True)
        self.exam_listbox.bind('<<ListboxSelect>>', self.on_exam_select)
        
        # 试卷统计信息
        self.exam_info_frame = ttk.LabelFrame(left_frame, text="试卷详情", padding=10)
        self.exam_info_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.exam_info_text = tk.Text(self.exam_info_frame, height=6, wrap=tk.WORD,
                                     font=("Microsoft YaHei", 9), state=tk.DISABLED)
        self.exam_info_text.pack(fill=tk.BOTH, expand=True)
        
    def create_question_detail_panel(self, parent):
        """创建错题详情面板"""
        right_frame = ttk.Frame(parent)
        parent.add(right_frame, weight=3)
        
        # 详情标题和操作按钮
        detail_header = ttk.Frame(right_frame)
        detail_header.pack(fill=tk.X, pady=(0, 10))
        
        self.detail_title_label = ttk.Label(detail_header, text="选择试卷查看错题", 
                                           font=("Microsoft YaHei", 12, "bold"))
        self.detail_title_label.pack(side=tk.LEFT)
        
        # 操作按钮
        button_frame = ttk.Frame(detail_header)
        button_frame.pack(side=tk.RIGHT)
        
        ttk.Button(button_frame, text="📝 查看解析", 
                  command=self.view_question_explanation).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="⭐ 切换收藏", 
                  command=self.toggle_favorite).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🗑️ 删除错题", 
                  command=self.delete_question).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="📊 题目统计",
                  command=self.show_question_stats).pack(side=tk.LEFT, padx=(0, 5))

        # 批量操作按钮
        batch_frame = ttk.Frame(right_frame)
        batch_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(batch_frame, text="批量操作:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(batch_frame, text="☑️ 全选", command=self.select_all_questions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="❌ 取消选择", command=self.deselect_all_questions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="⭐ 批量收藏", command=self.batch_toggle_favorite).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="🗑️ 批量删除", command=self.batch_delete_questions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="📋 批量导出", command=self.batch_export_questions).pack(side=tk.LEFT, padx=(0, 5))

        # 错题列表 - 增强版列
        columns = ('ID', '题目', '类型', '正确答案', '我的答案', '试卷', '考试次数', '题号', '错误次数', '收藏', '时间')
        self.question_tree = ttk.Treeview(right_frame, columns=columns, show='headings', height=20)
        
        # 设置列宽和标题
        column_configs = {
            'ID': {'width': 50, 'text': 'ID'},
            '题目': {'width': 200, 'text': '题目内容'},
            '类型': {'width': 70, 'text': '题型'},
            '正确答案': {'width': 80, 'text': '正确答案'},
            '我的答案': {'width': 80, 'text': '我的答案'},
            '试卷': {'width': 120, 'text': '来源试卷'},
            '考试次数': {'width': 70, 'text': '第几次考试'},
            '题号': {'width': 50, 'text': '试卷题号'},
            '错误次数': {'width': 70, 'text': '累计错误'},
            '收藏': {'width': 50, 'text': '收藏'},
            '时间': {'width': 120, 'text': '添加时间'}
        }
        
        for col, config in column_configs.items():
            self.question_tree.heading(col, text=config['text'])
            self.question_tree.column(col, width=config['width'])
        
        # 滚动条
        tree_frame = ttk.Frame(right_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        tree_scrollbar_v = ttk.Scrollbar(tree_frame, orient="vertical", command=self.question_tree.yview)
        tree_scrollbar_h = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.question_tree.xview)
        
        self.question_tree.configure(yscrollcommand=tree_scrollbar_v.set, 
                                    xscrollcommand=tree_scrollbar_h.set)
        
        self.question_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定事件
        self.question_tree.bind('<Double-Button-1>', lambda e: self.view_question_explanation())
        self.question_tree.bind('<Button-3>', self.show_context_menu)  # 右键菜单
        
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="就绪", 
                                     font=("Microsoft YaHei", 9))
        self.status_label.pack(side=tk.LEFT)
        
        # 快速统计
        self.quick_stats_label = ttk.Label(status_frame, text="", 
                                          font=("Microsoft YaHei", 9))
        self.quick_stats_label.pack(side=tk.RIGHT)
        
    def switch_view(self, view_type):
        """切换视图"""
        self.current_view = view_type
        self.status_label.config(text=f"切换到{self.get_view_name(view_type)}视图")
        
        if view_type == "by_exam":
            self.load_exam_summary()
        elif view_type == "all":
            self.load_all_questions()
        elif view_type == "favorites":
            self.load_favorite_questions()
        elif view_type == "statistics":
            self.show_statistics_view()
            
    def get_view_name(self, view_type):
        """获取视图名称"""
        names = {
            "by_exam": "按试卷分类",
            "all": "全部错题",
            "favorites": "收藏题目",
            "statistics": "统计分析"
        }
        return names.get(view_type, "未知")
        
    def load_exam_summary(self):
        """加载试卷摘要"""
        try:
            self.exam_listbox.delete(0, tk.END)
            self.clear_question_tree()
            
            # 获取试卷错题摘要（增强版）
            exam_summary = self.get_enhanced_exam_summary()
            
            total_exams = 0
            total_questions = 0
            
            for exam_info in exam_summary:
                exam_name = exam_info['exam_name']
                question_count = exam_info['question_count']
                favorite_count = exam_info['favorite_count']
                exam_count = exam_info['exam_count']  # 考试次数
                avg_score = exam_info['avg_score']  # 平均分
                
                total_exams += 1
                total_questions += question_count
                
                # 显示格式：试卷名 (错题数, 收藏数, 考试次数, 平均分)
                display_text = f"{exam_name} ({question_count}题"
                if favorite_count > 0:
                    display_text += f", {favorite_count}⭐"
                if exam_count > 0:
                    display_text += f", {exam_count}次考试"
                if avg_score is not None:
                    display_text += f", 平均{avg_score:.1f}分"
                display_text += ")"
                
                self.exam_listbox.insert(tk.END, display_text)
            
            # 更新统计信息
            self.stats_label.config(text=f"共 {total_exams} 套试卷，{total_questions} 道错题")
            self.detail_title_label.config(text="选择试卷查看详细错题")
            self.status_label.config(text="试卷摘要加载完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载试卷摘要失败: {str(e)}")
            self.status_label.config(text=f"加载失败: {str(e)}")
            
    def get_enhanced_exam_summary(self):
        """获取增强版试卷摘要"""
        try:
            # 获取错题按试卷分组的统计
            query = """
                SELECT 
                    COALESCE(wq.exam_title, '未分类错题') as exam_name,
                    COUNT(wq.id) as question_count,
                    SUM(CASE WHEN wq.is_favorite = 1 THEN 1 ELSE 0 END) as favorite_count,
                    wq.exam_id
                FROM wrong_questions wq
                GROUP BY COALESCE(wq.exam_title, '未分类错题'), wq.exam_id
                ORDER BY question_count DESC
            """
            
            basic_summary = self.wrong_question_manager.db.execute_query(query)
            enhanced_summary = []
            
            for row in basic_summary:
                exam_name = row[0]
                question_count = row[1]
                favorite_count = row[2]
                exam_id = row[3]
                
                # 获取考试次数和平均分
                exam_count = 0
                avg_score = None
                
                if exam_id:
                    # 查询该试卷的考试记录
                    exam_records_query = """
                        SELECT COUNT(*) as exam_count, AVG(score) as avg_score
                        FROM exam_records 
                        WHERE exam_id = ?
                    """
                    exam_stats = self.wrong_question_manager.db.execute_query(
                        exam_records_query, (exam_id,))
                    
                    if exam_stats and exam_stats[0]:
                        exam_count = exam_stats[0][0] or 0
                        avg_score = exam_stats[0][1]
                
                enhanced_summary.append({
                    'exam_name': exam_name,
                    'question_count': question_count,
                    'favorite_count': favorite_count,
                    'exam_id': exam_id,
                    'exam_count': exam_count,
                    'avg_score': avg_score
                })
                
            return enhanced_summary
            
        except Exception as e:
            print(f"获取增强版试卷摘要失败: {e}")
            # 回退到基本摘要
            return self.wrong_question_manager.get_exam_wrong_question_summary()
            
    def load_all_questions(self):
        """加载所有错题"""
        try:
            self.exam_listbox.delete(0, tk.END)
            self.exam_listbox.insert(tk.END, "全部错题")
            self.exam_listbox.selection_set(0)
            
            questions = self.get_enhanced_questions()
            self.populate_question_tree(questions)
            
            self.stats_label.config(text=f"共 {len(questions)} 道错题")
            self.detail_title_label.config(text="全部错题")
            self.status_label.config(text="全部错题加载完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载所有错题失败: {str(e)}")
            
    def load_favorite_questions(self):
        """加载收藏的错题"""
        try:
            self.exam_listbox.delete(0, tk.END)
            self.exam_listbox.insert(tk.END, "收藏的错题")
            self.exam_listbox.selection_set(0)
            
            questions = self.get_enhanced_questions(favorites_only=True)
            self.populate_question_tree(questions)
            
            self.stats_label.config(text=f"共 {len(questions)} 道收藏错题")
            self.detail_title_label.config(text="收藏的错题")
            self.status_label.config(text="收藏错题加载完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载收藏错题失败: {str(e)}")
            
    def get_enhanced_questions(self, exam_title=None, favorites_only=False):
        """获取增强版错题数据"""
        try:
            base_query = """
                SELECT
                    wq.id, wq.question_text, wq.question_type, wq.correct_answer,
                    wq.user_answer, wq.explanation, wq.is_favorite, wq.created_at,
                    wq.exam_id, wq.exam_title, wq.exam_record_id,
                    COALESCE(wq.exam_title, '未分类') as display_exam_title,
                    wq.question_number
                FROM wrong_questions wq
            """
            
            conditions = []
            params = []
            
            if exam_title and exam_title != "未分类错题":
                conditions.append("wq.exam_title = ?")
                params.append(exam_title)
            elif exam_title == "未分类错题":
                conditions.append("(wq.exam_id IS NULL OR wq.exam_title IS NULL)")
                
            if favorites_only:
                conditions.append("wq.is_favorite = 1")
                
            if conditions:
                base_query += " WHERE " + " AND ".join(conditions)
                
            base_query += " ORDER BY wq.created_at DESC"
            
            questions = self.wrong_question_manager.db.execute_query(base_query, params)
            
            # 增强每个错题的信息
            enhanced_questions = []
            for question in questions:
                enhanced_question = list(question)
                
                # 添加额外信息
                question_id = question[0]
                exam_id = question[8]
                exam_record_id = question[10]
                question_number_raw = question[12] if len(question) > 12 else None  # 从数据库获取题号

                # 获取考试次数（该试卷被考了多少次）
                exam_count = self.get_exam_count(exam_id) if exam_id else 0

                # 获取题号（在试卷中的位置）
                question_number = self.get_question_number(question_number_raw)

                # 获取错误次数（该题目被错了多少次）
                error_count = self.get_question_error_count(question[1])  # 基于题目文本

                # 添加到增强信息
                enhanced_question.extend([exam_count, question_number, error_count])
                enhanced_questions.append(enhanced_question)
                
            return enhanced_questions
            
        except Exception as e:
            print(f"获取增强版错题数据失败: {e}")
            # 回退到基本查询
            return self.wrong_question_manager.get_all_wrong_questions()
            
    def get_exam_count(self, exam_id):
        """获取试卷考试次数"""
        try:
            if not exam_id:
                return 0
                
            query = "SELECT COUNT(*) FROM exam_records WHERE exam_id = ?"
            result = self.wrong_question_manager.db.execute_query(query, (exam_id,))
            return result[0][0] if result and result[0] else 0
        except:
            return 0
            
    def get_question_number(self, question_number):
        """获取题目在试卷中的序号"""
        try:
            if question_number is not None and question_number > 0:
                return f"第{question_number}题"
            else:
                return "未知"
        except:
            return "未知"
            
    def get_question_error_count(self, question_text):
        """获取相同题目的错误次数"""
        try:
            query = "SELECT COUNT(*) FROM wrong_questions WHERE question_text = ?"
            result = self.wrong_question_manager.db.execute_query(query, (question_text,))
            return result[0][0] if result and result[0] else 1
        except:
            return 1

    def on_exam_select(self, event):
        """试卷选择事件"""
        if self.current_view not in ["by_exam"]:
            return

        selection = self.exam_listbox.curselection()
        if not selection:
            return

        try:
            selected_index = selection[0]
            exam_summary = self.get_enhanced_exam_summary()

            if selected_index < len(exam_summary):
                exam_info = exam_summary[selected_index]
                exam_name = exam_info['exam_name']

                # 加载该试卷的错题
                questions = self.get_enhanced_questions(exam_title=exam_name)
                self.populate_question_tree(questions)

                # 更新详情标题
                self.detail_title_label.config(text=f"{exam_name} - 错题详情")

                # 更新试卷详情信息
                self.update_exam_info(exam_info, questions)

                self.status_label.config(text=f"已加载 {exam_name} 的 {len(questions)} 道错题")

        except Exception as e:
            messagebox.showerror("错误", f"加载试卷错题失败: {str(e)}")

    def update_exam_info(self, exam_info, questions):
        """更新试卷详情信息"""
        try:
            self.exam_info_text.config(state=tk.NORMAL)
            self.exam_info_text.delete(1.0, tk.END)

            info_text = f"""📊 试卷统计信息

📝 试卷名称: {exam_info['exam_name']}
❌ 错题数量: {exam_info['question_count']} 道
⭐ 收藏题目: {exam_info['favorite_count']} 道
🎯 考试次数: {exam_info['exam_count']} 次
📈 平均得分: {exam_info['avg_score']:.1f}分 (如有)

📋 题型分布:"""

            # 统计题型分布
            type_stats = {}
            for question in questions:
                q_type = question[2]  # question_type
                type_stats[q_type] = type_stats.get(q_type, 0) + 1

            for q_type, count in type_stats.items():
                type_name = self.get_question_type_name(q_type)
                info_text += f"\n  • {type_name}: {count} 道"

            # 最近错题时间
            if questions:
                latest_time = questions[0][7]  # created_at (已按时间倒序)
                info_text += f"\n\n🕒 最新错题: {latest_time[:16] if latest_time else '未知'}"

            self.exam_info_text.insert(1.0, info_text)
            self.exam_info_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f"更新试卷详情失败: {e}")

    def get_question_type_name(self, question_type):
        """获取题目类型中文名称"""
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        return type_names.get(question_type, question_type)

    def populate_question_tree(self, questions):
        """填充错题列表"""
        self.clear_question_tree()

        for question in questions:
            try:
                question_id = question[0]
                question_text = question[1]
                question_type = question[2]
                correct_answer = question[3]
                user_answer = question[4]
                is_favorite = question[6]
                created_at = question[7]
                exam_title = question[11] if len(question) > 11 else question[9]  # display_exam_title

                # 增强信息 - 安全处理
                try:
                    exam_count = int(question[12]) if len(question) > 12 and question[12] is not None else 0
                except (ValueError, TypeError):
                    exam_count = 0

                try:
                    question_number = str(question[13]) if len(question) > 13 and question[13] is not None else "未知"
                except (ValueError, TypeError):
                    question_number = "未知"

                try:
                    error_count = int(question[14]) if len(question) > 14 and question[14] is not None else 1
                except (ValueError, TypeError):
                    error_count = 1

                # 截断长题目
                display_question = question_text[:30] + "..." if len(question_text) > 30 else question_text
                favorite_text = "⭐" if is_favorite else ""

                # 格式化时间
                time_str = created_at[:16] if created_at else "未知"

                # 格式化答案显示
                correct_display = str(correct_answer)[:10] + "..." if len(str(correct_answer)) > 10 else str(correct_answer)
                user_display = str(user_answer)[:10] + "..." if len(str(user_answer)) > 10 else str(user_answer)

                # 格式化试卷名称
                exam_display = exam_title[:15] + "..." if len(exam_title) > 15 else exam_title

                self.question_tree.insert('', tk.END, values=(
                    question_id,
                    display_question,
                    self.get_question_type_name(question_type),
                    correct_display,
                    user_display,
                    exam_display,
                    f"{exam_count}次" if exam_count > 0 else "未知",
                    question_number,
                    f"{error_count}次",
                    favorite_text,
                    time_str
                ))

            except Exception as e:
                print(f"添加错题到列表失败: {e}")
                continue

        # 更新快速统计
        self.update_quick_stats(questions)

    def update_quick_stats(self, questions):
        """更新快速统计信息"""
        try:
            total = len(questions)
            favorites = sum(1 for q in questions if q[6])  # is_favorite

            # 统计最近7天的错题
            recent_count = 0
            week_ago = datetime.now() - timedelta(days=7)

            for question in questions:
                try:
                    created_at = question[7]
                    if created_at:
                        q_time = datetime.strptime(created_at[:19], '%Y-%m-%d %H:%M:%S')
                        if q_time >= week_ago:
                            recent_count += 1
                except:
                    continue

            stats_text = f"总计: {total} | 收藏: {favorites} | 最近7天: {recent_count}"
            self.quick_stats_label.config(text=stats_text)

        except Exception as e:
            print(f"更新快速统计失败: {e}")

    def clear_question_tree(self):
        """清空错题列表"""
        for item in self.question_tree.get_children():
            self.question_tree.delete(item)

    def view_question_explanation(self):
        """查看错题解析"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题")
            return

        try:
            item = self.question_tree.item(selection[0])
            question_id = item['values'][0]

            # 获取错题详情
            question_data = self.wrong_question_manager.get_wrong_question_by_id(question_id)
            if question_data:
                self.show_question_detail_window(question_data)
            else:
                messagebox.showerror("错误", "无法获取错题详情")

        except Exception as e:
            messagebox.showerror("错误", f"查看解析失败: {str(e)}")

    def show_question_detail_window(self, question_data):
        """显示错题详情窗口"""
        detail_window = tk.Toplevel(self.window)
        detail_window.title("错题详情")
        detail_window.geometry("800x600")
        detail_window.transient(self.window)

        # 创建滚动文本框
        text_frame = ttk.Frame(detail_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD,
                                               font=("Microsoft YaHei", 11))
        text_widget.pack(fill=tk.BOTH, expand=True)

        # 格式化显示内容
        content = f"""📝 错题详情

🆔 题目ID: {question_data[0]}
📚 来源试卷: {question_data[9] if len(question_data) > 9 and question_data[9] else '未分类'}
📅 添加时间: {question_data[7]}
⭐ 收藏状态: {'已收藏' if question_data[6] else '未收藏'}

❓ 题目内容:
{question_data[1]}

📊 题目类型: {self.get_question_type_name(question_data[2])}

✅ 正确答案:
{question_data[3]}

❌ 我的答案:
{question_data[4]}

💡 解析说明:
{question_data[5] if question_data[5] else '暂无解析'}

📈 统计信息:
• 该题目累计错误次数: {self.get_question_error_count(question_data[1])}
• 相同试卷错题数量: {self.get_same_exam_error_count(question_data[9] if len(question_data) > 9 else None)}
"""

        text_widget.insert(1.0, content)
        text_widget.config(state=tk.DISABLED)

        # 操作按钮
        button_frame = ttk.Frame(detail_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="⭐ 切换收藏",
                  command=lambda: self.toggle_question_favorite(question_data[0], detail_window)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🗑️ 删除错题",
                  command=lambda: self.delete_question_by_id(question_data[0], detail_window)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="📋 复制内容",
                  command=lambda: self.copy_question_content(question_data)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭",
                  command=detail_window.destroy).pack(side=tk.RIGHT)

    def get_same_exam_error_count(self, exam_title):
        """获取相同试卷的错题数量"""
        try:
            if not exam_title:
                return 0
            query = "SELECT COUNT(*) FROM wrong_questions WHERE exam_title = ?"
            result = self.wrong_question_manager.db.execute_query(query, (exam_title,))
            return result[0][0] if result and result[0] else 0
        except:
            return 0

    def toggle_favorite(self):
        """切换收藏状态"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题")
            return

        try:
            item = self.question_tree.item(selection[0])
            question_id = item['values'][0]

            self.wrong_question_manager.toggle_favorite(question_id)
            self.refresh_current_view()
            self.status_label.config(text="收藏状态已更新")

        except Exception as e:
            messagebox.showerror("错误", f"切换收藏失败: {str(e)}")

    def delete_question(self):
        """删除错题"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题")
            return

        if not messagebox.askyesno("确认删除", "确定要删除这道错题吗？"):
            return

        try:
            item = self.question_tree.item(selection[0])
            question_id = item['values'][0]

            self.wrong_question_manager.delete_wrong_question(question_id)
            self.refresh_current_view()
            self.status_label.config(text="错题已删除")

        except Exception as e:
            messagebox.showerror("错误", f"删除错题失败: {str(e)}")

    def show_question_stats(self):
        """显示题目统计"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题")
            return

        try:
            item = self.question_tree.item(selection[0])
            question_id = item['values'][0]
            question_text = item['values'][1]

            # 获取统计信息
            stats = self.get_question_detailed_stats(question_id, question_text)

            # 显示统计窗口
            self.show_stats_window(stats)

        except Exception as e:
            messagebox.showerror("错误", f"获取统计信息失败: {str(e)}")

    def get_question_detailed_stats(self, question_id, question_text):
        """获取题目详细统计"""
        try:
            stats = {
                'question_id': question_id,
                'question_text': question_text,
                'total_errors': self.get_question_error_count(question_text),
                'first_error_date': None,
                'last_error_date': None,
                'error_frequency': 0,
                'related_exams': []
            }

            # 获取该题目的所有错误记录
            query = """
                SELECT created_at, exam_title, exam_id
                FROM wrong_questions
                WHERE question_text = ?
                ORDER BY created_at
            """
            records = self.wrong_question_manager.db.execute_query(query, (question_text,))

            if records:
                stats['first_error_date'] = records[0][0]
                stats['last_error_date'] = records[-1][0]

                # 统计相关试卷
                exam_set = set()
                for record in records:
                    if record[1]:  # exam_title
                        exam_set.add(record[1])
                stats['related_exams'] = list(exam_set)

                # 计算错误频率（每月平均错误次数）
                if len(records) > 1:
                    try:
                        first_date = datetime.strptime(records[0][0][:19], '%Y-%m-%d %H:%M:%S')
                        last_date = datetime.strptime(records[-1][0][:19], '%Y-%m-%d %H:%M:%S')
                        days_diff = (last_date - first_date).days
                        if days_diff > 0:
                            stats['error_frequency'] = len(records) / (days_diff / 30.0)
                    except:
                        pass

            return stats

        except Exception as e:
            print(f"获取题目详细统计失败: {e}")
            return {}

    def show_stats_window(self, stats):
        """显示统计窗口"""
        stats_window = tk.Toplevel(self.window)
        stats_window.title("题目统计分析")
        stats_window.geometry("600x400")
        stats_window.transient(self.window)

        text_widget = scrolledtext.ScrolledText(stats_window, wrap=tk.WORD,
                                               font=("Microsoft YaHei", 11))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        content = f"""📊 题目统计分析

🆔 题目ID: {stats.get('question_id', '未知')}

❓ 题目内容:
{stats.get('question_text', '未知')[:100]}...

📈 错误统计:
• 累计错误次数: {stats.get('total_errors', 0)} 次
• 首次错误时间: {stats.get('first_error_date', '未知')[:16] if stats.get('first_error_date') else '未知'}
• 最近错误时间: {stats.get('last_error_date', '未知')[:16] if stats.get('last_error_date') else '未知'}
• 错误频率: {stats.get('error_frequency', 0):.2f} 次/月

📚 相关试卷:
"""

        related_exams = stats.get('related_exams', [])
        if related_exams:
            for exam in related_exams:
                content += f"• {exam}\n"
        else:
            content += "• 无相关试卷信息\n"

        content += f"""
💡 学习建议:
"""

        # 根据统计数据给出建议
        total_errors = stats.get('total_errors', 0)
        if total_errors >= 3:
            content += "• ⚠️ 该题目错误次数较多，建议重点复习\n"
        if stats.get('error_frequency', 0) > 1:
            content += "• 📅 错误频率较高，建议制定专项练习计划\n"
        if len(related_exams) > 1:
            content += "• 📚 该题目在多套试卷中出现，属于重点考查内容\n"

        text_widget.insert(1.0, content)
        text_widget.config(state=tk.DISABLED)

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            # 选中右键点击的项目
            item = self.question_tree.identify_row(event.y)
            if item:
                self.question_tree.selection_set(item)

                # 创建右键菜单
                context_menu = tk.Menu(self.window, tearoff=0)
                context_menu.add_command(label="📝 查看解析", command=self.view_question_explanation)
                context_menu.add_command(label="⭐ 切换收藏", command=self.toggle_favorite)
                context_menu.add_separator()
                context_menu.add_command(label="📊 题目统计", command=self.show_question_stats)
                context_menu.add_command(label="📋 复制题目", command=self.copy_selected_question)
                context_menu.add_separator()
                context_menu.add_command(label="🗑️ 删除错题", command=self.delete_question)

                # 显示菜单
                context_menu.post(event.x_root, event.y_root)

        except Exception as e:
            print(f"显示右键菜单失败: {e}")

    def copy_selected_question(self):
        """复制选中的题目"""
        selection = self.question_tree.selection()
        if not selection:
            return

        try:
            item = self.question_tree.item(selection[0])
            question_id = item['values'][0]

            question_data = self.wrong_question_manager.get_wrong_question_by_id(question_id)
            if question_data:
                content = f"题目: {question_data[1]}\n正确答案: {question_data[3]}\n我的答案: {question_data[4]}"
                self.window.clipboard_clear()
                self.window.clipboard_append(content)
                self.status_label.config(text="题目内容已复制到剪贴板")

        except Exception as e:
            print(f"复制题目失败: {e}")

    def copy_question_content(self, question_data):
        """复制题目内容"""
        try:
            content = f"""题目: {question_data[1]}
类型: {self.get_question_type_name(question_data[2])}
正确答案: {question_data[3]}
我的答案: {question_data[4]}
解析: {question_data[5] if question_data[5] else '暂无解析'}"""

            self.window.clipboard_clear()
            self.window.clipboard_append(content)
            messagebox.showinfo("提示", "题目内容已复制到剪贴板")

        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")

    def toggle_question_favorite(self, question_id, window):
        """切换题目收藏状态"""
        try:
            self.wrong_question_manager.toggle_favorite(question_id)
            messagebox.showinfo("提示", "收藏状态已更新")
            window.destroy()
            self.refresh_current_view()
        except Exception as e:
            messagebox.showerror("错误", f"更新收藏状态失败: {str(e)}")

    def delete_question_by_id(self, question_id, window):
        """根据ID删除题目"""
        if not messagebox.askyesno("确认删除", "确定要删除这道错题吗？"):
            return

        try:
            self.wrong_question_manager.delete_wrong_question(question_id)
            messagebox.showinfo("提示", "错题已删除")
            window.destroy()
            self.refresh_current_view()
        except Exception as e:
            messagebox.showerror("错误", f"删除错题失败: {str(e)}")

    def show_statistics_view(self):
        """显示统计分析视图"""
        try:
            self.exam_listbox.delete(0, tk.END)
            self.exam_listbox.insert(tk.END, "📊 统计分析")
            self.exam_listbox.selection_set(0)

            # 清空错题列表，显示统计信息
            self.clear_question_tree()
            self.detail_title_label.config(text="错题统计分析")

            # 在试卷详情区域显示统计信息
            self.show_comprehensive_stats()

            self.status_label.config(text="统计分析视图已加载")

        except Exception as e:
            messagebox.showerror("错误", f"加载统计视图失败: {str(e)}")

    def show_comprehensive_stats(self):
        """显示综合统计信息"""
        try:
            self.exam_info_text.config(state=tk.NORMAL)
            self.exam_info_text.delete(1.0, tk.END)

            # 获取各种统计数据
            total_questions = len(self.wrong_question_manager.get_all_wrong_questions())
            favorite_questions = len(self.wrong_question_manager.get_favorite_questions())

            # 按题型统计
            type_stats = self.get_question_type_stats()

            # 按试卷统计
            exam_stats = self.get_enhanced_exam_summary()

            # 按时间统计
            time_stats = self.get_time_based_stats()

            stats_content = f"""📊 错题本综合统计

📈 总体概况:
• 错题总数: {total_questions} 道
• 收藏题目: {favorite_questions} 道
• 涉及试卷: {len(exam_stats)} 套
• 收藏率: {(favorite_questions/total_questions*100):.1f}% (如有错题)

📋 题型分布:"""

            for q_type, count in type_stats.items():
                percentage = (count / total_questions * 100) if total_questions > 0 else 0
                stats_content += f"\n• {self.get_question_type_name(q_type)}: {count} 道 ({percentage:.1f}%)"

            stats_content += f"\n\n📚 试卷错题排行:"

            # 按错题数量排序试卷
            sorted_exams = sorted(exam_stats, key=lambda x: x['question_count'], reverse=True)[:5]
            for i, exam in enumerate(sorted_exams, 1):
                stats_content += f"\n{i}. {exam['exam_name']}: {exam['question_count']} 道错题"

            stats_content += f"\n\n📅 时间分布:"
            stats_content += f"\n• 今天: {time_stats.get('today', 0)} 道"
            stats_content += f"\n• 本周: {time_stats.get('this_week', 0)} 道"
            stats_content += f"\n• 本月: {time_stats.get('this_month', 0)} 道"

            # 学习建议
            stats_content += f"\n\n💡 学习建议:"
            if total_questions > 20:
                stats_content += f"\n• 错题数量较多，建议分批复习"
            if favorite_questions < total_questions * 0.2:
                stats_content += f"\n• 建议标记更多重点错题进行收藏"
            if len(exam_stats) > 5:
                stats_content += f"\n• 涉及试卷较多，建议按试卷分类复习"

            self.exam_info_text.insert(1.0, stats_content)
            self.exam_info_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f"显示综合统计失败: {e}")

    def get_question_type_stats(self):
        """获取题型统计"""
        try:
            query = """
                SELECT question_type, COUNT(*) as count
                FROM wrong_questions
                GROUP BY question_type
                ORDER BY count DESC
            """
            results = self.wrong_question_manager.db.execute_query(query)
            return {row[0]: row[1] for row in results}
        except:
            return {}

    def get_time_based_stats(self):
        """获取基于时间的统计"""
        try:
            now = datetime.now()
            today = now.date()
            week_ago = now - timedelta(days=7)
            month_ago = now - timedelta(days=30)

            stats = {'today': 0, 'this_week': 0, 'this_month': 0}

            questions = self.wrong_question_manager.get_all_wrong_questions()
            for question in questions:
                try:
                    created_at = question[7]  # created_at
                    if created_at:
                        q_time = datetime.strptime(created_at[:19], '%Y-%m-%d %H:%M:%S')

                        if q_time.date() == today:
                            stats['today'] += 1
                        if q_time >= week_ago:
                            stats['this_week'] += 1
                        if q_time >= month_ago:
                            stats['this_month'] += 1
                except:
                    continue

            return stats
        except:
            return {'today': 0, 'this_week': 0, 'this_month': 0}

    def refresh_data(self):
        """刷新数据"""
        self.status_label.config(text="正在刷新数据...")
        self.refresh_current_view()

    def refresh_current_view(self):
        """刷新当前视图"""
        if self.current_view == "by_exam":
            self.load_exam_summary()
        elif self.current_view == "all":
            self.load_all_questions()
        elif self.current_view == "favorites":
            self.load_favorite_questions()
        elif self.current_view == "statistics":
            self.show_statistics_view()

    # 批量操作方法
    def select_all_questions(self):
        """全选所有错题"""
        try:
            for item in self.question_tree.get_children():
                self.question_tree.selection_add(item)

            selected_count = len(self.question_tree.selection())
            self.status_label.config(text=f"已选择 {selected_count} 道错题")

        except Exception as e:
            messagebox.showerror("错误", f"全选失败: {str(e)}")

    def deselect_all_questions(self):
        """取消选择所有错题"""
        try:
            self.question_tree.selection_remove(self.question_tree.selection())
            self.status_label.config(text="已取消所有选择")

        except Exception as e:
            messagebox.showerror("错误", f"取消选择失败: {str(e)}")

    def batch_toggle_favorite(self):
        """批量切换收藏状态"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要操作的错题")
            return

        if not messagebox.askyesno("确认操作", f"确定要切换 {len(selection)} 道错题的收藏状态吗？"):
            return

        try:
            success_count = 0
            for item in selection:
                question_id = self.question_tree.item(item)['values'][0]
                self.wrong_question_manager.toggle_favorite(question_id)
                success_count += 1

            self.refresh_current_view()
            self.status_label.config(text=f"已切换 {success_count} 道错题的收藏状态")

        except Exception as e:
            messagebox.showerror("错误", f"批量收藏操作失败: {str(e)}")

    def batch_delete_questions(self):
        """批量删除错题"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的错题")
            return

        if not messagebox.askyesno("确认删除",
                                  f"确定要删除 {len(selection)} 道错题吗？\n此操作不可撤销！"):
            return

        try:
            success_count = 0
            for item in selection:
                question_id = self.question_tree.item(item)['values'][0]
                self.wrong_question_manager.delete_wrong_question(question_id)
                success_count += 1

            self.refresh_current_view()
            self.status_label.config(text=f"已删除 {success_count} 道错题")

        except Exception as e:
            messagebox.showerror("错误", f"批量删除失败: {str(e)}")

    def batch_export_questions(self):
        """批量导出错题"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要导出的错题")
            return

        try:
            from tkinter import filedialog

            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                title="导出错题",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if not filename:
                return

            # 收集错题数据
            export_content = f"错题导出 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            export_content += "=" * 50 + "\n\n"

            for i, item in enumerate(selection, 1):
                values = self.question_tree.item(item)['values']
                question_id = values[0]

                # 获取完整错题信息
                question_data = self.wrong_question_manager.get_wrong_question_by_id(question_id)
                if question_data:
                    export_content += f"题目 {i}:\n"
                    export_content += f"ID: {question_data[0]}\n"
                    export_content += f"题目: {question_data[1]}\n"
                    export_content += f"类型: {self.get_question_type_name(question_data[2])}\n"
                    export_content += f"正确答案: {question_data[3]}\n"
                    export_content += f"我的答案: {question_data[4]}\n"
                    export_content += f"解析: {question_data[5] if question_data[5] else '暂无解析'}\n"
                    export_content += f"试卷: {question_data[9] if len(question_data) > 9 and question_data[9] else '未分类'}\n"
                    export_content += f"添加时间: {question_data[7]}\n"
                    export_content += "-" * 30 + "\n\n"

            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(export_content)

            messagebox.showinfo("导出成功", f"已导出 {len(selection)} 道错题到:\n{filename}")
            self.status_label.config(text=f"已导出 {len(selection)} 道错题")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")

    def switch_to_favorites_view(self):
        """切换到收藏视图"""
        try:
            self.current_view = "favorites"
            # 更新视图按钮状态
            if hasattr(self, 'view_by_exam_btn'):
                self.view_by_exam_btn.config(state="normal")
            if hasattr(self, 'view_all_btn'):
                self.view_all_btn.config(state="normal")
            if hasattr(self, 'view_favorites_btn'):
                self.view_favorites_btn.config(state="disabled")
            if hasattr(self, 'view_statistics_btn'):
                self.view_statistics_btn.config(state="normal")

            # 加载收藏的题目
            self.load_favorite_questions()
        except Exception as e:
            print(f"切换到收藏视图失败: {e}")

    def load_favorite_questions(self):
        """加载收藏的题目"""
        try:
            # 清空现有内容
            for widget in self.questions_frame.winfo_children():
                widget.destroy()

            # 获取收藏的错题
            try:
                favorite_questions = self.wrong_question_manager.get_favorite_questions()
            except AttributeError:
                # 如果没有收藏功能，显示提示
                no_feature_label = ttk.Label(self.questions_frame,
                                           text="⭐ 收藏功能开发中\n\n该功能将在后续版本中提供",
                                           font=("Microsoft YaHei", 12),
                                           justify=tk.CENTER)
                no_feature_label.pack(expand=True)
                return

            if not favorite_questions:
                no_data_label = ttk.Label(self.questions_frame,
                                        text="⭐ 暂无收藏的题目\n\n您可以在错题列表中点击星号收藏重要题目",
                                        font=("Microsoft YaHei", 12),
                                        justify=tk.CENTER)
                no_data_label.pack(expand=True)
                return

            # 显示收藏题目标题
            title_frame = ttk.Frame(self.questions_frame)
            title_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(title_frame, text=f"⭐ 收藏题目 ({len(favorite_questions)}道)",
                     font=("Microsoft YaHei", 14, "bold")).pack(side=tk.LEFT)

            # 显示收藏的题目
            for question in favorite_questions:
                self.create_question_card(question, show_exam_info=True)

        except Exception as e:
            messagebox.showerror("错误", f"加载收藏题目失败: {str(e)}")
            print(f"加载收藏题目错误: {e}")
