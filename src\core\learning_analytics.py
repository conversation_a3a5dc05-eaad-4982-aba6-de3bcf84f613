#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能学习分析系统
提供学习曲线分析、知识点掌握度分析、个性化推荐等功能
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from collections import defaultdict

# 尝试导入可选依赖
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("⚠️ numpy不可用，使用基础数学计算")

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib不可用，图表功能受限")

class LearningAnalytics:
    def __init__(self, db_manager):
        """初始化学习分析系统"""
        self.db_manager = db_manager
        self.setup_analytics_tables()
        
        # 设置中文字体（如果matplotlib可用）
        if MATPLOTLIB_AVAILABLE:
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
        
    def setup_analytics_tables(self):
        """创建分析相关的数据表"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 学习会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT DEFAULT 'default',
                    session_start TIMESTAMP,
                    session_end TIMESTAMP,
                    duration INTEGER,
                    questions_answered INTEGER,
                    correct_answers INTEGER,
                    topics_covered TEXT,
                    difficulty_level TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 知识点掌握度表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS knowledge_mastery (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT DEFAULT 'default',
                    topic TEXT,
                    subtopic TEXT,
                    mastery_level REAL,
                    confidence_score REAL,
                    last_practiced TIMESTAMP,
                    practice_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 学习目标表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_goals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT DEFAULT 'default',
                    goal_type TEXT,
                    target_value REAL,
                    current_value REAL,
                    deadline DATE,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 学习推荐表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_recommendations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT DEFAULT 'default',
                    recommendation_type TEXT,
                    content TEXT,
                    priority INTEGER,
                    reason TEXT,
                    status TEXT DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            print("✅ 学习分析数据表创建成功")
            
        except Exception as e:
            print(f"❌ 创建学习分析数据表失败: {e}")
            
    def record_learning_session(self, session_data: Dict):
        """记录学习会话"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO learning_sessions 
                (user_id, session_start, session_end, duration, questions_answered, 
                 correct_answers, topics_covered, difficulty_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session_data.get('user_id', 'default'),
                session_data.get('session_start'),
                session_data.get('session_end'),
                session_data.get('duration'),
                session_data.get('questions_answered'),
                session_data.get('correct_answers'),
                json.dumps(session_data.get('topics_covered', [])),
                session_data.get('difficulty_level')
            ))
            
            conn.commit()
            print("✅ 学习会话记录成功")
            
        except Exception as e:
            print(f"❌ 记录学习会话失败: {e}")
            
    def update_knowledge_mastery(self, topic: str, subtopic: str, performance: float):
        """更新知识点掌握度"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 查询现有掌握度
            cursor.execute('''
                SELECT mastery_level, practice_count FROM knowledge_mastery 
                WHERE topic = ? AND subtopic = ?
            ''', (topic, subtopic))
            
            result = cursor.fetchone()
            
            if result:
                # 更新现有记录
                old_mastery, practice_count = result
                # 使用加权平均更新掌握度
                new_mastery = (old_mastery * practice_count + performance) / (practice_count + 1)
                confidence = min(0.95, practice_count * 0.1 + 0.5)
                
                cursor.execute('''
                    UPDATE knowledge_mastery 
                    SET mastery_level = ?, confidence_score = ?, 
                        last_practiced = ?, practice_count = practice_count + 1,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE topic = ? AND subtopic = ?
                ''', (new_mastery, confidence, datetime.now(), topic, subtopic))
            else:
                # 创建新记录
                cursor.execute('''
                    INSERT INTO knowledge_mastery 
                    (topic, subtopic, mastery_level, confidence_score, 
                     last_practiced, practice_count)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (topic, subtopic, performance, 0.5, datetime.now(), 1))
            
            conn.commit()
            print(f"✅ 知识点掌握度更新: {topic}/{subtopic} -> {performance:.2f}")
            
        except Exception as e:
            print(f"❌ 更新知识点掌握度失败: {e}")
            
    def get_learning_curve(self, days: int = 30) -> Dict:
        """获取学习曲线数据"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 获取最近N天的学习数据
            start_date = datetime.now() - timedelta(days=days)
            
            cursor.execute('''
                SELECT DATE(session_start) as date,
                       COUNT(*) as sessions,
                       SUM(duration) as total_time,
                       SUM(questions_answered) as total_questions,
                       AVG(CAST(correct_answers AS FLOAT) / questions_answered) as avg_accuracy
                FROM learning_sessions 
                WHERE session_start >= ?
                GROUP BY DATE(session_start)
                ORDER BY date
            ''', (start_date,))
            
            results = cursor.fetchall()
            
            # 处理数据
            dates = []
            sessions = []
            study_time = []
            questions = []
            accuracy = []
            
            for row in results:
                dates.append(row[0])
                sessions.append(row[1])
                study_time.append(row[2] or 0)
                questions.append(row[3] or 0)
                accuracy.append(row[4] or 0)
            
            return {
                'dates': dates,
                'sessions': sessions,
                'study_time': study_time,
                'questions': questions,
                'accuracy': accuracy
            }
            
        except Exception as e:
            print(f"❌ 获取学习曲线失败: {e}")
            return {}
            
    def get_knowledge_mastery_overview(self) -> Dict:
        """获取知识点掌握度概览"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT topic, subtopic, mastery_level, confidence_score, practice_count
                FROM knowledge_mastery
                ORDER BY topic, subtopic
            ''')
            
            results = cursor.fetchall()
            
            # 按主题分组
            topics = defaultdict(list)
            for row in results:
                topic, subtopic, mastery, confidence, count = row
                topics[topic].append({
                    'subtopic': subtopic,
                    'mastery_level': mastery,
                    'confidence_score': confidence,
                    'practice_count': count
                })
            
            # 计算主题平均掌握度
            topic_summary = {}
            for topic, subtopics in topics.items():
                if NUMPY_AVAILABLE:
                    avg_mastery = np.mean([s['mastery_level'] for s in subtopics])
                    avg_confidence = np.mean([s['confidence_score'] for s in subtopics])
                else:
                    # 使用基础Python计算平均值
                    mastery_values = [s['mastery_level'] for s in subtopics]
                    confidence_values = [s['confidence_score'] for s in subtopics]
                    avg_mastery = sum(mastery_values) / len(mastery_values) if mastery_values else 0
                    avg_confidence = sum(confidence_values) / len(confidence_values) if confidence_values else 0

                total_practice = sum([s['practice_count'] for s in subtopics])
                
                topic_summary[topic] = {
                    'average_mastery': avg_mastery,
                    'average_confidence': avg_confidence,
                    'total_practice': total_practice,
                    'subtopics': subtopics
                }
            
            return topic_summary
            
        except Exception as e:
            print(f"❌ 获取知识点掌握度概览失败: {e}")
            return {}
            
    def generate_personalized_recommendations(self) -> List[Dict]:
        """生成个性化学习推荐"""
        try:
            recommendations = []
            
            # 获取知识点掌握度
            mastery_data = self.get_knowledge_mastery_overview()
            
            # 获取最近学习数据
            learning_curve = self.get_learning_curve(7)
            
            # 推荐1: 薄弱知识点
            weak_topics = []
            for topic, data in mastery_data.items():
                if data['average_mastery'] < 0.6:
                    weak_topics.append((topic, data['average_mastery']))
            
            if weak_topics:
                weak_topics.sort(key=lambda x: x[1])  # 按掌握度排序
                recommendations.append({
                    'type': 'weak_topics',
                    'title': '薄弱知识点强化',
                    'content': f"建议重点练习: {', '.join([t[0] for t in weak_topics[:3]])}",
                    'priority': 1,
                    'reason': '这些知识点的掌握度较低，需要加强练习'
                })
            
            # 推荐2: 学习时间建议
            if learning_curve.get('study_time'):
                avg_daily_time = np.mean(learning_curve['study_time'])
                if avg_daily_time < 1800:  # 少于30分钟
                    recommendations.append({
                        'type': 'study_time',
                        'title': '增加学习时间',
                        'content': f"建议每日学习时间增加到45-60分钟（当前平均: {avg_daily_time/60:.1f}分钟）",
                        'priority': 2,
                        'reason': '适当增加学习时间有助于提高学习效果'
                    })
            
            # 推荐3: 复习提醒
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT topic, subtopic FROM knowledge_mastery 
                WHERE last_practiced < ? AND mastery_level > 0.7
                ORDER BY last_practiced
                LIMIT 5
            ''', (datetime.now() - timedelta(days=7),))
            
            review_topics = cursor.fetchall()
            if review_topics:
                recommendations.append({
                    'type': 'review',
                    'title': '复习提醒',
                    'content': f"建议复习: {', '.join([f'{t[0]}/{t[1]}' for t in review_topics[:3]])}",
                    'priority': 3,
                    'reason': '这些知识点已经掌握较好，但需要定期复习巩固'
                })
            
            # 保存推荐到数据库
            for rec in recommendations:
                cursor.execute('''
                    INSERT INTO learning_recommendations 
                    (recommendation_type, content, priority, reason)
                    VALUES (?, ?, ?, ?)
                ''', (rec['type'], rec['content'], rec['priority'], rec['reason']))
            
            conn.commit()
            return recommendations
            
        except Exception as e:
            print(f"❌ 生成个性化推荐失败: {e}")
            return []
            
    def get_learning_statistics(self) -> Dict:
        """获取学习统计数据"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 总体统计
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_sessions,
                    SUM(duration) as total_time,
                    SUM(questions_answered) as total_questions,
                    AVG(CAST(correct_answers AS FLOAT) / questions_answered) as overall_accuracy
                FROM learning_sessions
            ''')
            
            overall_stats = cursor.fetchone()
            
            # 最近7天统计
            week_ago = datetime.now() - timedelta(days=7)
            cursor.execute('''
                SELECT 
                    COUNT(*) as week_sessions,
                    SUM(duration) as week_time,
                    SUM(questions_answered) as week_questions,
                    AVG(CAST(correct_answers AS FLOAT) / questions_answered) as week_accuracy
                FROM learning_sessions
                WHERE session_start >= ?
            ''', (week_ago,))
            
            week_stats = cursor.fetchone()
            
            # 知识点统计
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_topics,
                    AVG(mastery_level) as avg_mastery,
                    COUNT(CASE WHEN mastery_level >= 0.8 THEN 1 END) as mastered_topics,
                    COUNT(CASE WHEN mastery_level < 0.6 THEN 1 END) as weak_topics
                FROM knowledge_mastery
            ''')
            
            topic_stats = cursor.fetchone()
            
            return {
                'overall': {
                    'total_sessions': overall_stats[0] or 0,
                    'total_time': overall_stats[1] or 0,
                    'total_questions': overall_stats[2] or 0,
                    'overall_accuracy': overall_stats[3] or 0
                },
                'recent_week': {
                    'sessions': week_stats[0] or 0,
                    'time': week_stats[1] or 0,
                    'questions': week_stats[2] or 0,
                    'accuracy': week_stats[3] or 0
                },
                'knowledge': {
                    'total_topics': topic_stats[0] or 0,
                    'average_mastery': topic_stats[1] or 0,
                    'mastered_topics': topic_stats[2] or 0,
                    'weak_topics': topic_stats[3] or 0
                }
            }
            
        except Exception as e:
            print(f"❌ 获取学习统计失败: {e}")
            return {}
            
    def predict_performance(self, topic: str, subtopic: str = None) -> Dict:
        """预测学习表现"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 获取历史数据
            if subtopic:
                cursor.execute('''
                    SELECT mastery_level, practice_count, confidence_score
                    FROM knowledge_mastery 
                    WHERE topic = ? AND subtopic = ?
                ''', (topic, subtopic))
            else:
                cursor.execute('''
                    SELECT AVG(mastery_level), SUM(practice_count), AVG(confidence_score)
                    FROM knowledge_mastery 
                    WHERE topic = ?
                ''', (topic,))
            
            result = cursor.fetchone()
            
            if result and result[0] is not None:
                mastery, practice_count, confidence = result
                
                # 简单的预测模型
                # 基于当前掌握度、练习次数和置信度预测未来表现
                predicted_accuracy = min(0.95, mastery + (practice_count * 0.01))
                improvement_potential = (1 - mastery) * confidence
                
                return {
                    'predicted_accuracy': predicted_accuracy,
                    'improvement_potential': improvement_potential,
                    'recommended_practice': max(1, int((1 - mastery) * 10)),
                    'confidence_level': confidence
                }
            else:
                return {
                    'predicted_accuracy': 0.5,
                    'improvement_potential': 0.8,
                    'recommended_practice': 5,
                    'confidence_level': 0.3
                }
                
        except Exception as e:
            print(f"❌ 预测学习表现失败: {e}")
            return {}
            
    def export_learning_report(self, format='json') -> str:
        """导出学习报告"""
        try:
            # 收集所有数据
            statistics = self.get_learning_statistics()
            learning_curve = self.get_learning_curve(30)
            mastery_overview = self.get_knowledge_mastery_overview()
            recommendations = self.generate_personalized_recommendations()
            
            report_data = {
                'generated_at': datetime.now().isoformat(),
                'statistics': statistics,
                'learning_curve': learning_curve,
                'knowledge_mastery': mastery_overview,
                'recommendations': recommendations
            }
            
            if format == 'json':
                return json.dumps(report_data, indent=2, ensure_ascii=False)
            else:
                # 可以扩展支持其他格式
                return str(report_data)
                
        except Exception as e:
            print(f"❌ 导出学习报告失败: {e}")
            return ""
