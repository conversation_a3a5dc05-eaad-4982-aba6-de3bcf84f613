#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中转API网络诊断工具
"""

import requests
import socket
import time
import json
from urllib.parse import urlparse

def test_relay_api_connectivity(base_url, api_key, model):
    """
    全面测试中转API连接性
    """
    print("=" * 60)
    print("🔍 中转API网络诊断开始")
    print("=" * 60)
    
    results = []
    
    # 1. 解析URL
    try:
        parsed_url = urlparse(base_url)
        host = parsed_url.hostname
        port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        
        print(f"📋 配置信息:")
        print(f"   Base URL: {base_url}")
        print(f"   Host: {host}")
        print(f"   Port: {port}")
        print(f"   Scheme: {parsed_url.scheme}")
        print(f"   Model: {model}")
        print(f"   API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else '***'}")
        print()
        
    except Exception as e:
        print(f"❌ URL解析失败: {e}")
        return False
    
    # 2. DNS解析测试
    print("🔍 步骤1: DNS解析测试")
    try:
        ip_address = socket.gethostbyname(host)
        print(f"✅ DNS解析成功: {host} -> {ip_address}")
        results.append("DNS解析: ✅")
    except Exception as e:
        print(f"❌ DNS解析失败: {e}")
        results.append("DNS解析: ❌")
        return False
    print()
    
    # 3. TCP连接测试
    print("🔍 步骤2: TCP连接测试")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ TCP连接成功: {host}:{port}")
            results.append("TCP连接: ✅")
        else:
            print(f"❌ TCP连接失败: {host}:{port} (错误码: {result})")
            results.append("TCP连接: ❌")
            return False
    except Exception as e:
        print(f"❌ TCP连接测试出错: {e}")
        results.append("TCP连接: ❌")
        return False
    print()
    
    # 4. HTTP基础连接测试
    print("🔍 步骤3: HTTP基础连接测试")
    try:
        # 测试根路径
        root_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
        response = requests.get(root_url, timeout=10)
        print(f"✅ HTTP连接成功: {response.status_code}")
        print(f"   响应头: {dict(list(response.headers.items())[:3])}")
        results.append("HTTP连接: ✅")
    except requests.exceptions.Timeout:
        print("❌ HTTP连接超时")
        results.append("HTTP连接: ❌ (超时)")
    except requests.exceptions.ConnectionError as e:
        print(f"❌ HTTP连接失败: {e}")
        results.append("HTTP连接: ❌")
    except Exception as e:
        print(f"⚠️ HTTP连接测试: {e}")
        results.append("HTTP连接: ⚠️")
    print()
    
    # 5. API端点测试
    print("🔍 步骤4: API端点测试")
    try:
        # 测试/models端点
        models_url = f"{base_url}/models"
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        print(f"📤 测试模型列表端点: {models_url}")
        response = requests.get(models_url, headers=headers, timeout=15)
        print(f"📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 模型列表端点正常")
            try:
                models_data = response.json()
                print(f"   返回数据类型: {type(models_data)}")
                if isinstance(models_data, dict) and 'data' in models_data:
                    print(f"   模型数量: {len(models_data['data'])}")
                results.append("模型端点: ✅")
            except:
                print("   响应不是有效JSON")
                results.append("模型端点: ⚠️")
        else:
            print(f"⚠️ 模型列表端点异常: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            results.append("模型端点: ⚠️")
            
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        results.append("模型端点: ❌")
    print()
    
    # 6. 聊天API测试
    print("🔍 步骤5: 聊天API测试")
    try:
        chat_url = f"{base_url}/chat/completions"
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "max_tokens": 5,
            "temperature": 0.1
        }
        
        print(f"📤 测试聊天端点: {chat_url}")
        print(f"📤 使用模型: {model}")
        
        response = requests.post(chat_url, headers=headers, json=data, timeout=30)
        print(f"📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    print("✅ 聊天API测试成功")
                    print(f"   模型响应: {result['choices'][0].get('message', {}).get('content', '')[:50]}...")
                    results.append("聊天API: ✅")
                else:
                    print("⚠️ 聊天API响应格式异常")
                    print(f"   响应结构: {list(result.keys())}")
                    results.append("聊天API: ⚠️")
            except Exception as e:
                print(f"❌ 解析聊天API响应失败: {e}")
                print(f"   原始响应: {response.text[:200]}")
                results.append("聊天API: ❌")
        else:
            print(f"❌ 聊天API请求失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text[:300]}")
            results.append("聊天API: ❌")
            
    except requests.exceptions.Timeout:
        print("❌ 聊天API请求超时")
        results.append("聊天API: ❌ (超时)")
    except Exception as e:
        print(f"❌ 聊天API测试失败: {e}")
        results.append("聊天API: ❌")
    print()
    
    # 7. 总结
    print("=" * 60)
    print("📊 诊断结果总结")
    print("=" * 60)
    
    for result in results:
        print(f"   {result}")
    
    success_count = sum(1 for r in results if "✅" in r)
    total_count = len(results)
    
    print(f"\n🎯 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有测试通过！API配置正确。")
        return True
    elif success_count >= total_count - 1:
        print("⚠️ 大部分测试通过，可能存在小问题。")
        return True
    else:
        print("❌ 多项测试失败，请检查配置。")
        return False

def get_troubleshooting_tips(base_url, api_key, model):
    """
    获取故障排除建议
    """
    print("\n" + "=" * 60)
    print("💡 故障排除建议")
    print("=" * 60)
    
    print("1. 检查基础配置:")
    print(f"   - Base URL是否正确: {base_url}")
    print(f"   - API Key是否有效: {api_key[:10]}...")
    print(f"   - 模型名称是否正确: {model}")
    
    print("\n2. 网络连接检查:")
    print("   - 确认服务器IP和端口可访问")
    print("   - 检查防火墙设置")
    print("   - 尝试使用curl命令测试")
    
    print("\n3. API服务检查:")
    print("   - 确认中转服务正在运行")
    print("   - 检查服务日志")
    print("   - 验证API Key权限")
    
    print("\n4. 模型配置检查:")
    print("   - 确认模型名称拼写正确")
    print("   - 检查模型是否在服务中可用")
    print("   - 尝试使用其他模型测试")
    
    print(f"\n5. 手动测试命令:")
    print(f"   curl -X POST {base_url}/chat/completions \\")
    print(f"     -H 'Authorization: Bearer {api_key}' \\")
    print(f"     -H 'Content-Type: application/json' \\")
    print(f"     -d '{{'")
    print(f"       \"model\": \"{model}\",")
    print(f"       \"messages\": [{{\"role\": \"user\", \"content\": \"Hello\"}}],")
    print(f"       \"max_tokens\": 5")
    print(f"     }}'")

if __name__ == "__main__":
    # 示例测试
    base_url = "http://*************:3000/v1"
    api_key = "your_api_key_here"
    model = "gemini-2.5-flash"
    
    test_relay_api_connectivity(base_url, api_key, model)
    get_troubleshooting_tips(base_url, api_key, model)
