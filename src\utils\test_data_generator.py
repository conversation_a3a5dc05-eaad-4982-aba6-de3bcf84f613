#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据生成器
用于生成测试数据，方便系统测试
"""

import random
from datetime import datetime, timedelta
from src.core.material_manager import MaterialManager
from src.core.exam_manager import ExamManager
from src.core.wrong_question_manager import WrongQuestionManager
from src.core.knowledge_graph_manager import KnowledgeGraphManager

class TestDataGenerator:
    def __init__(self, db_manager):
        """初始化测试数据生成器"""
        self.db = db_manager
        self.material_manager = MaterialManager(db_manager)
        self.exam_manager = ExamManager(db_manager)
        self.wrong_question_manager = WrongQuestionManager(db_manager)
        self.kg_manager = KnowledgeGraphManager(db_manager)
    
    def generate_sample_materials(self):
        """生成示例学习材料"""
        materials = [
            {
                "title": "Python基础教程",
                "content": """
Python是一种高级编程语言，由Guido van Rossum于1989年发明。Python具有简洁、易读的语法，
是初学者学习编程的理想选择。

Python的主要特点包括：
1. 简洁明了的语法
2. 强大的标准库
3. 跨平台兼容性
4. 面向对象编程支持
5. 动态类型系统

Python广泛应用于Web开发、数据科学、人工智能、自动化脚本等领域。
常用的Python框架包括Django、Flask、NumPy、Pandas等。

变量和数据类型：
Python中的变量不需要声明类型，支持整数、浮点数、字符串、列表、字典等数据类型。

控制结构：
Python支持if-else条件语句、for和while循环语句。

函数定义：
使用def关键字定义函数，支持参数传递和返回值。
""",
                "file_type": "text"
            },
            {
                "title": "数据结构与算法",
                "content": """
数据结构是计算机科学中的基础概念，用于组织和存储数据。
常见的数据结构包括数组、链表、栈、队列、树、图等。

数组（Array）：
数组是最基本的数据结构，元素在内存中连续存储，支持随机访问。
时间复杂度：访问O(1)，插入和删除O(n)。

链表（Linked List）：
链表由节点组成，每个节点包含数据和指向下一个节点的指针。
时间复杂度：访问O(n)，插入和删除O(1)。

栈（Stack）：
栈是后进先出（LIFO）的数据结构，只能在栈顶进行插入和删除操作。
主要操作：push（入栈）、pop（出栈）、top（查看栈顶元素）。

队列（Queue）：
队列是先进先出（FIFO）的数据结构，在队尾插入，在队头删除。
主要操作：enqueue（入队）、dequeue（出队）、front（查看队头元素）。

树（Tree）：
树是层次化的数据结构，由节点和边组成。
二叉树是最常见的树结构，每个节点最多有两个子节点。

算法复杂度分析：
时间复杂度和空间复杂度是评估算法效率的重要指标。
常见的时间复杂度有O(1)、O(log n)、O(n)、O(n log n)、O(n²)等。
""",
                "file_type": "text"
            },
            {
                "title": "机器学习入门",
                "content": """
机器学习是人工智能的一个分支，通过算法让计算机从数据中学习模式。

机器学习的主要类型：
1. 监督学习：使用标记数据训练模型
2. 无监督学习：从无标记数据中发现模式
3. 强化学习：通过与环境交互学习最优策略

监督学习算法：
- 线性回归：用于预测连续值
- 逻辑回归：用于分类问题
- 决策树：基于特征进行决策
- 支持向量机：寻找最优分类边界
- 神经网络：模拟人脑神经元结构

无监督学习算法：
- K-means聚类：将数据分为K个簇
- 层次聚类：构建数据的层次结构
- 主成分分析：降维和特征提取

模型评估：
- 准确率：正确预测的比例
- 精确率：预测为正例中真正为正例的比例
- 召回率：真正为正例中被预测为正例的比例
- F1分数：精确率和召回率的调和平均

过拟合和欠拟合：
过拟合是模型在训练数据上表现很好但在测试数据上表现差。
欠拟合是模型过于简单，无法捕捉数据的复杂模式。

交叉验证：
将数据分为训练集和验证集，多次训练和验证以获得更可靠的评估结果。
""",
                "file_type": "text"
            }
        ]
        
        created_ids = []
        for material in materials:
            try:
                material_id = self.material_manager.save_material(
                    material["title"], 
                    material["content"], 
                    material["file_type"]
                )
                created_ids.append(material_id)
            except Exception as e:
                print(f"创建材料失败: {e}")
        
        return created_ids
    
    def generate_sample_questions(self):
        """生成示例题目"""
        questions = [
            {
                "type": "single_choice",
                "question": "Python是由谁发明的？",
                "options": ["Guido van Rossum", "Dennis Ritchie", "Bjarne Stroustrup", "James Gosling"],
                "correct_answer": "A",
                "explanation": "Python是由Guido van Rossum于1989年发明的。",
                "score": 1
            },
            {
                "type": "multiple_choice",
                "question": "Python的主要特点包括哪些？",
                "options": ["简洁明了的语法", "强大的标准库", "跨平台兼容性", "静态类型系统"],
                "correct_answer": ["A", "B", "C"],
                "explanation": "Python具有简洁语法、强大标准库和跨平台兼容性，但它是动态类型系统。",
                "score": 2
            },
            {
                "type": "true_false",
                "question": "栈是先进先出（FIFO）的数据结构。",
                "correct_answer": "错",
                "explanation": "栈是后进先出（LIFO）的数据结构，队列才是先进先出（FIFO）的。",
                "score": 1
            },
            {
                "type": "short_answer",
                "question": "请简述监督学习和无监督学习的区别。",
                "correct_answer": "监督学习使用标记数据训练模型，而无监督学习从无标记数据中发现模式。",
                "explanation": "监督学习需要输入和输出的对应关系，无监督学习只有输入数据。",
                "score": 3
            },
            {
                "type": "case_analysis",
                "question": "假设你要开发一个推荐系统，请分析应该选择哪种机器学习方法，并说明理由。",
                "correct_answer": "可以使用协同过滤（无监督学习）或基于内容的推荐（监督学习），具体选择取决于数据类型和业务需求。",
                "explanation": "推荐系统可以采用多种方法，需要根据具体场景选择合适的算法。",
                "score": 5
            }
        ]
        
        return questions
    
    def generate_sample_exam(self):
        """生成示例考试"""
        questions = self.generate_sample_questions()
        
        try:
            exam_id = self.exam_manager.create_exam(
                title="Python与数据结构综合测试",
                description="涵盖Python基础、数据结构和机器学习的综合测试",
                questions=questions,
                time_limit=90
            )
            return exam_id
        except Exception as e:
            print(f"创建考试失败: {e}")
            return None
    
    def generate_sample_wrong_questions(self):
        """生成示例错题"""
        wrong_questions = [
            {
                "question_text": "Python是编译型语言。",
                "question_type": "true_false",
                "correct_answer": "错",
                "user_answer": "对",
                "explanation": "Python是解释型语言，不是编译型语言。"
            },
            {
                "question_text": "数组的访问时间复杂度是多少？",
                "question_type": "single_choice",
                "correct_answer": "O(1)",
                "user_answer": "O(n)",
                "explanation": "数组支持随机访问，时间复杂度为O(1)。"
            },
            {
                "question_text": "什么是过拟合？",
                "question_type": "short_answer",
                "correct_answer": "模型在训练数据上表现很好但在测试数据上表现差的现象。",
                "user_answer": "模型训练不充分。",
                "explanation": "过拟合是指模型过度学习训练数据的特征，导致泛化能力差。"
            }
        ]
        
        created_ids = []
        for wq in wrong_questions:
            try:
                question_id = self.wrong_question_manager.add_wrong_question(
                    wq["question_text"],
                    wq["question_type"],
                    wq["correct_answer"],
                    wq["user_answer"],
                    wq["explanation"]
                )
                created_ids.append(question_id)
            except Exception as e:
                print(f"创建错题失败: {e}")
        
        return created_ids
    
    def generate_sample_knowledge_points(self):
        """生成示例知识点"""
        knowledge_points = [
            {"name": "编程语言", "description": "计算机编程语言的总称", "parent_id": None},
            {"name": "Python", "description": "高级编程语言", "parent_id": 1},
            {"name": "数据结构", "description": "组织和存储数据的方式", "parent_id": None},
            {"name": "线性结构", "description": "元素之间存在一对一关系的结构", "parent_id": 3},
            {"name": "数组", "description": "连续存储的数据结构", "parent_id": 4},
            {"name": "链表", "description": "通过指针连接的数据结构", "parent_id": 4},
            {"name": "栈", "description": "后进先出的数据结构", "parent_id": 4},
            {"name": "队列", "description": "先进先出的数据结构", "parent_id": 4},
            {"name": "树形结构", "description": "层次化的数据结构", "parent_id": 3},
            {"name": "二叉树", "description": "每个节点最多有两个子节点的树", "parent_id": 9},
            {"name": "机器学习", "description": "让计算机从数据中学习的技术", "parent_id": None},
            {"name": "监督学习", "description": "使用标记数据训练模型", "parent_id": 11},
            {"name": "无监督学习", "description": "从无标记数据中发现模式", "parent_id": 11}
        ]
        
        created_ids = []
        for i, kp in enumerate(knowledge_points, 1):
            try:
                # 调整parent_id
                parent_id = kp["parent_id"]
                if parent_id and parent_id <= len(created_ids):
                    parent_id = created_ids[parent_id - 1]
                else:
                    parent_id = None
                
                point_id = self.kg_manager.add_knowledge_point(
                    kp["name"],
                    kp["description"],
                    parent_id
                )
                created_ids.append(point_id)
            except Exception as e:
                print(f"创建知识点失败: {e}")
        
        return created_ids
    
    def generate_all_sample_data(self):
        """生成所有示例数据"""
        print("开始生成测试数据...")
        
        # 生成材料
        print("生成学习材料...")
        material_ids = self.generate_sample_materials()
        print(f"创建了 {len(material_ids)} 个学习材料")
        
        # 生成考试
        print("生成示例考试...")
        exam_id = self.generate_sample_exam()
        if exam_id:
            print(f"创建了考试，ID: {exam_id}")
        
        # 生成错题
        print("生成示例错题...")
        wrong_question_ids = self.generate_sample_wrong_questions()
        print(f"创建了 {len(wrong_question_ids)} 道错题")
        
        # 生成知识点
        print("生成知识点...")
        knowledge_point_ids = self.generate_sample_knowledge_points()
        print(f"创建了 {len(knowledge_point_ids)} 个知识点")
        
        print("测试数据生成完成！")
        
        return {
            "materials": material_ids,
            "exam": exam_id,
            "wrong_questions": wrong_question_ids,
            "knowledge_points": knowledge_point_ids
        }
