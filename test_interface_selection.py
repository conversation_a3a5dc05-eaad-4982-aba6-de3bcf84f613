#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面选择窗口的自适应布局
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_interface_selection():
    """测试界面选择窗口"""
    try:
        print("🧪 测试界面选择窗口的自适应布局...")
        
        # 导入必要的模块
        from src.core.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.ui.exam_selection_window import ExamSelectionWindow
        
        # 初始化管理器
        db_manager = DatabaseManager()
        exam_manager = ExamManager(db_manager)
        wrong_question_manager = WrongQuestionManager(db_manager)
        
        # 创建测试考试
        test_questions = [
            {
                'type': 'single_choice',
                'question': '界面选择窗口现在是否能自适应分辨率？',
                'options': ['是的，很好', '还需要改进', '不确定', '没有变化'],
                'correct_answer': 'A',
                'explanation': '现在界面选择窗口已经优化为自适应布局',
                'score': 1
            }
        ]
        
        # 创建考试
        exam_id = exam_manager.create_exam(
            title="🧪 界面选择测试",
            description="测试自适应界面选择窗口",
            questions=test_questions,
            time_limit=5
        )
        
        print(f"✅ 创建测试考试成功，ID: {exam_id}")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("🧪 界面选择测试")
        root.geometry("400x300")
        
        # 居中显示
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width // 2) - 200
        y = (screen_height // 2) - 150
        root.geometry(f"400x300+{x}+{y}")
        
        # 显示屏幕信息
        info_frame = tk.Frame(root, bg='#F5F5F5')
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        title_label = tk.Label(info_frame,
                              text="🧪 界面选择窗口测试",
                              font=("Microsoft YaHei", 16, "bold"),
                              bg='#F5F5F5', fg='#333333')
        title_label.pack(pady=(0, 20))
        
        screen_info = tk.Label(info_frame,
                              text=f"📺 当前屏幕分辨率: {screen_width} x {screen_height}",
                              font=("Microsoft YaHei", 12),
                              bg='#F5F5F5', fg='#666666')
        screen_info.pack(pady=(0, 10))
        
        test_info = tk.Label(info_frame,
                            text="点击下方按钮测试界面选择窗口\n"
                                 "检查是否能看到完整的界面选项和开始考试按钮",
                            font=("Microsoft YaHei", 11),
                            bg='#F5F5F5', fg='#666666',
                            justify=tk.CENTER)
        test_info.pack(pady=(0, 20))
        
        def start_test():
            """启动界面选择测试"""
            try:
                # 创建考试选择窗口
                exam_selection = ExamSelectionWindow(root, exam_manager, wrong_question_manager)
                
                # 直接调用界面选择
                exam_data = exam_manager.get_exam_by_id(exam_id)
                if exam_data:
                    # 模拟点击开始考试
                    exam_selection.start_exam(exam_id)
                else:
                    messagebox.showerror("错误", "无法获取考试数据")
                    
            except Exception as e:
                messagebox.showerror("错误", f"测试失败：{str(e)}")
                print(f"测试错误: {e}")
                import traceback
                traceback.print_exc()
        
        # 测试按钮
        test_btn = tk.Button(info_frame,
                            text="🚀 测试界面选择窗口",
                            font=("Microsoft YaHei", 14, "bold"),
                            bg='#007AFF', fg='white',
                            relief='flat', padx=30, pady=15,
                            command=start_test)
        test_btn.pack(pady=10)
        
        # 说明文字
        note_label = tk.Label(info_frame,
                             text="✅ 优化内容：\n"
                                  "• 自适应窗口大小\n"
                                  "• 紧凑的界面布局\n"
                                  "• 确保按钮可见\n"
                                  "• 支持不同分辨率",
                             font=("Microsoft YaHei", 10),
                             bg='#F5F5F5', fg='#666666',
                             justify=tk.LEFT)
        note_label.pack(pady=(20, 0))
        
        # 退出按钮
        exit_btn = tk.Button(info_frame,
                            text="❌ 退出",
                            font=("Microsoft YaHei", 12),
                            bg='#8E8E93', fg='white',
                            relief='flat', padx=20, pady=10,
                            command=root.quit)
        exit_btn.pack(pady=(20, 0))
        
        print("🖥️ 界面选择测试窗口已启动")
        print(f"📺 屏幕分辨率: {screen_width} x {screen_height}")
        
        # 运行界面
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误对话框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", f"界面选择测试失败：\n{str(e)}")
        root.destroy()

if __name__ == "__main__":
    print("🧪 界面选择窗口自适应测试")
    print("=" * 40)
    test_interface_selection()
