#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清晰版AI考试生成系统 - 无模糊效果
专门为高DPI显示器优化
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFrame, QScrollArea,
                            QSpinBox, QSlider, QComboBox, QTextEdit, QGroupBox,
                            QGridLayout, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor

class ClearExamGenerator(QMainWindow):
    """清晰版AI考试生成系统主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧠 AI考试生成系统 - 清晰版")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置窗口背景色
        self.setStyleSheet("background-color: #f5f5f5;")
        
        # 初始化UI
        self.init_ui()
        self.setup_styles()

        # 设置实时更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_preview)
        self.update_timer.setSingleShot(True)
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建标题栏
        self.create_title_bar(main_layout)
        
        # 创建内容区域
        self.create_content_area(main_layout)
        
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_frame.setFixedHeight(60)
        parent_layout.addWidget(title_frame)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 0, 20, 0)
        
        # 标题
        title_label = QLabel("🧠 AI考试生成系统")
        title_label.setObjectName("titleLabel")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 功能按钮
        buttons = [
            ("📊 统计", self.show_stats),
            ("⚙️ 设置", self.show_settings),
            ("❌ 关闭", self.close)
        ]
        
        for text, callback in buttons:
            btn = QPushButton(text)
            btn.setObjectName("titleButton")
            btn.clicked.connect(callback)
            btn.setFixedSize(80, 35)
            title_layout.addWidget(btn)
            
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        content_widget = QWidget()
        parent_layout.addWidget(content_widget)
        
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # 左侧参数设置区
        self.create_parameter_panel(content_layout)
        
        # 右侧预览区
        self.create_preview_panel(content_layout)
        
    def create_parameter_panel(self, parent_layout):
        """创建参数设置面板"""
        # 参数面板容器
        param_frame = QFrame()
        param_frame.setObjectName("paramFrame")
        param_frame.setFixedWidth(400)
        parent_layout.addWidget(param_frame)
        
        param_layout = QVBoxLayout(param_frame)
        param_layout.setContentsMargins(20, 20, 20, 20)
        param_layout.setSpacing(20)
        
        # 基础设置
        self.create_basic_settings(param_layout)
        
        # 题型设置
        self.create_question_types(param_layout)
        
        # 难度设置
        self.create_difficulty_settings(param_layout)
        
        # 生成按钮
        self.create_action_buttons(param_layout)
        
        param_layout.addStretch()
        
    def create_basic_settings(self, parent_layout):
        """创建基础设置"""
        group = QGroupBox("📝 基础设置")
        group.setObjectName("settingsGroup")
        parent_layout.addWidget(group)
        
        layout = QVBoxLayout(group)
        
        # 考试名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("考试名称:"))
        self.exam_name_input = QComboBox()
        self.exam_name_input.setEditable(True)
        self.exam_name_input.addItems(["数学期末考试", "英语模拟考试", "物理单元测试"])
        self.exam_name_input.setObjectName("clearInput")
        name_layout.addWidget(self.exam_name_input)
        layout.addLayout(name_layout)
        
        # 题目总数
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("题目总数:"))
        self.question_count = QSpinBox()
        self.question_count.setRange(5, 100)
        self.question_count.setValue(20)
        self.question_count.setObjectName("clearSpinBox")
        self.question_count.valueChanged.connect(self.delayed_update_preview)
        count_layout.addWidget(self.question_count)
        layout.addLayout(count_layout)
        
        # 考试时长
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("考试时长:"))
        self.exam_duration = QSpinBox()
        self.exam_duration.setRange(30, 300)
        self.exam_duration.setValue(90)
        self.exam_duration.setSuffix(" 分钟")
        self.exam_duration.setObjectName("clearSpinBox")
        time_layout.addWidget(self.exam_duration)
        layout.addLayout(time_layout)
        
    def create_question_types(self, parent_layout):
        """创建题型设置"""
        group = QGroupBox("🎯 题型分布")
        group.setObjectName("settingsGroup")
        parent_layout.addWidget(group)
        
        layout = QVBoxLayout(group)
        
        # 题型滑块
        question_types = [
            ("单选题", 10),
            ("多选题", 5),
            ("判断题", 3),
            ("填空题", 2)
        ]
        
        self.type_sliders = {}
        for type_name, default_value in question_types:
            type_layout = QHBoxLayout()
            
            label = QLabel(type_name)
            label.setMinimumWidth(60)
            type_layout.addWidget(label)
            
            slider = QSlider(Qt.Orientation.Horizontal)
            slider.setRange(0, 20)
            slider.setValue(default_value)
            slider.setObjectName("clearSlider")
            slider.valueChanged.connect(self.delayed_update_preview)
            type_layout.addWidget(slider)
            
            value_label = QLabel(str(default_value))
            value_label.setMinimumWidth(30)
            value_label.setObjectName("valueLabel")
            slider.valueChanged.connect(lambda v, lbl=value_label: lbl.setText(str(v)))
            type_layout.addWidget(value_label)
            
            self.type_sliders[type_name] = slider
            layout.addLayout(type_layout)
            
    def create_difficulty_settings(self, parent_layout):
        """创建难度设置"""
        group = QGroupBox("⚡ 难度分布")
        group.setObjectName("settingsGroup")
        parent_layout.addWidget(group)
        
        layout = QVBoxLayout(group)
        
        # 难度滑块
        difficulties = [
            ("简单", 30),
            ("中等", 50),
            ("困难", 20)
        ]
        
        self.difficulty_sliders = {}
        for diff_name, default_value in difficulties:
            diff_layout = QHBoxLayout()
            
            label = QLabel(diff_name)
            label.setMinimumWidth(60)
            diff_layout.addWidget(label)
            
            slider = QSlider(Qt.Orientation.Horizontal)
            slider.setRange(0, 100)
            slider.setValue(default_value)
            slider.setObjectName("clearSlider")
            slider.valueChanged.connect(self.delayed_update_preview)
            diff_layout.addWidget(slider)
            
            percent_label = QLabel(f"{default_value}%")
            percent_label.setMinimumWidth(40)
            percent_label.setObjectName("valueLabel")
            slider.valueChanged.connect(lambda v, lbl=percent_label: lbl.setText(f"{v}%"))
            diff_layout.addWidget(percent_label)
            
            self.difficulty_sliders[diff_name] = slider
            layout.addLayout(diff_layout)
            
    def create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_layout = QVBoxLayout()
        
        # 主生成按钮
        self.generate_btn = QPushButton("🚀 生成智能试卷")
        self.generate_btn.setObjectName("primaryButton")
        self.generate_btn.setFixedHeight(50)
        self.generate_btn.clicked.connect(self.generate_exam)
        button_layout.addWidget(self.generate_btn)
        
        # 辅助按钮
        secondary_layout = QHBoxLayout()

        preview_btn = QPushButton("👁️ 预览")
        preview_btn.setObjectName("secondaryButton")
        preview_btn.clicked.connect(self.preview_exam)
        secondary_layout.addWidget(preview_btn)

        save_btn = QPushButton("💾 保存")
        save_btn.setObjectName("secondaryButton")
        save_btn.clicked.connect(self.save_exam)
        secondary_layout.addWidget(save_btn)
        
        button_layout.addLayout(secondary_layout)
        parent_layout.addLayout(button_layout)
        
    def create_preview_panel(self, parent_layout):
        """创建预览面板"""
        preview_frame = QFrame()
        preview_frame.setObjectName("previewFrame")
        parent_layout.addWidget(preview_frame)
        
        layout = QVBoxLayout(preview_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 预览标题
        title = QLabel("📋 试卷预览")
        title.setObjectName("previewTitle")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 预览内容
        self.preview_content = QTextEdit()
        self.preview_content.setObjectName("previewContent")
        self.preview_content.setReadOnly(True)
        layout.addWidget(self.preview_content)
        
        # 初始化预览
        self.update_preview()
        
    def delayed_update_preview(self):
        """延迟更新预览"""
        self.update_timer.stop()
        self.update_timer.start(300)

    def update_preview(self):
        """更新预览内容"""
        try:
            exam_name = self.exam_name_input.currentText()
            total_questions = self.question_count.value()
            duration = self.exam_duration.value()
            
            # 获取题型分布
            type_distribution = {}
            for type_name, slider in self.type_sliders.items():
                type_distribution[type_name] = slider.value()
            
            # 获取难度分布
            difficulty_distribution = {}
            for diff_name, slider in self.difficulty_sliders.items():
                difficulty_distribution[diff_name] = slider.value()
            
            # 生成预览内容
            preview_text = f"""
<div style="font-family: 'Microsoft YaHei'; line-height: 1.6;">
<h2 style="color: #2196F3; text-align: center; margin-bottom: 20px;">
📋 {exam_name}
</h2>

<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
<p><strong>📊 考试信息</strong></p>
<p>• 题目总数：{total_questions} 题</p>
<p>• 考试时长：{duration} 分钟</p>
<p>• 平均每题：{duration/total_questions:.1f} 分钟</p>
</div>

<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
<p><strong>🎯 题型分布</strong></p>
"""
            
            for type_name, count in type_distribution.items():
                if count > 0:
                    percentage = (count / total_questions) * 100
                    preview_text += f"<p>• {type_name}：{count} 题 ({percentage:.1f}%)</p>"
            
            preview_text += """
</div>

<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
<p><strong>⚡ 难度分布</strong></p>
"""
            
            for diff_name, percentage in difficulty_distribution.items():
                if percentage > 0:
                    preview_text += f"<p>• {diff_name}：{percentage}%</p>"
            
            preview_text += """
</div>

<div style="background: #e3f2fd; padding: 15px; border-radius: 8px;">
<p><strong>💡 智能建议</strong></p>
<p>• 建议在考试前进行模拟练习</p>
<p>• 注意时间分配，避免在难题上花费过多时间</p>
<p>• 先完成简单题目，再攻克难题</p>
</div>
</div>
"""
            
            self.preview_content.setHtml(preview_text)
            
        except Exception as e:
            print(f"更新预览失败: {e}")
            
    def setup_styles(self):
        """设置样式表"""
        style = """
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        #titleFrame {
            background-color: #2196F3;
            border-radius: 8px;
        }
        
        #titleLabel {
            color: white;
            font-weight: bold;
        }
        
        #titleButton {
            background-color: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            color: white;
            font-weight: bold;
        }
        
        #titleButton:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        #paramFrame, #previewFrame {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        #settingsGroup {
            font-weight: bold;
            color: #333;
        }
        
        #clearInput, #clearSpinBox {
            background-color: white;
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            font-size: 14px;
        }
        
        #clearInput:focus, #clearSpinBox:focus {
            border-color: #2196F3;
        }
        
        #clearSlider::groove:horizontal {
            border: 1px solid #ddd;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
        }
        
        #clearSlider::handle:horizontal {
            background: #2196F3;
            border: 2px solid white;
            width: 18px;
            margin: -7px 0;
            border-radius: 9px;
        }
        
        #clearSlider::handle:horizontal:hover {
            background: #1976D2;
        }
        
        #primaryButton {
            background-color: #2196F3;
            border: none;
            border-radius: 6px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            padding: 15px;
        }
        
        #primaryButton:hover {
            background-color: #1976D2;
        }
        
        #secondaryButton {
            background-color: white;
            border: 2px solid #2196F3;
            border-radius: 6px;
            color: #2196F3;
            font-weight: bold;
            padding: 10px 20px;
        }
        
        #secondaryButton:hover {
            background-color: #e3f2fd;
        }
        
        #previewTitle {
            color: #333;
            margin-bottom: 10px;
        }
        
        #previewContent {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Microsoft YaHei';
            font-size: 14px;
        }
        
        QLabel {
            color: #333;
            font-family: 'Microsoft YaHei';
        }
        
        #valueLabel {
            color: #666;
            font-weight: bold;
        }
        """
        
        self.setStyleSheet(style)
        
    # 事件处理方法
    def show_stats(self):
        """显示统计"""
        print("显示统计")
        
    def show_settings(self):
        """显示设置"""
        print("显示设置")
        
    def generate_exam(self):
        """生成考试"""
        print("生成考试")
        
    def preview_exam(self):
        """预览考试"""
        print("预览考试")
        
    def save_exam(self):
        """保存考试"""
        print("保存考试")

def main():
    """主函数"""
    # 设置高DPI支持
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = ClearExamGenerator()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
