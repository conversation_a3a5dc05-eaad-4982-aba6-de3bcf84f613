#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能出题系统
基于学习分析数据生成个性化试题
"""

import random
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from collections import defaultdict

class IntelligentQuestionGenerator:
    def __init__(self, db_manager, learning_analytics):
        """初始化智能出题系统"""
        self.db_manager = db_manager
        self.learning_analytics = learning_analytics
        self.difficulty_weights = {
            'easy': 0.3,
            'medium': 0.5,
            'hard': 0.2
        }
        
    def generate_adaptive_exam(self, 
                             target_topics: List[str] = None,
                             question_count: int = 20,
                             time_limit: int = 60,
                             user_id: str = 'default') -> Dict:
        """生成自适应考试"""
        try:
            # 获取用户学习数据
            mastery_data = self.learning_analytics.get_knowledge_mastery_overview()
            
            # 确定考试主题
            if not target_topics:
                target_topics = list(mastery_data.keys()) if mastery_data else ['通用']
            
            # 分析用户能力水平
            user_ability = self._estimate_user_ability(mastery_data, target_topics)
            
            # 生成题目分布策略
            question_strategy = self._generate_question_strategy(
                user_ability, question_count, target_topics, mastery_data
            )
            
            # 从数据库获取题目
            questions = self._select_questions_by_strategy(question_strategy)
            
            # 生成考试配置
            exam_config = {
                'exam_id': f"adaptive_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'title': f"智能自适应考试 - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                'description': f"基于您的学习情况生成的个性化考试",
                'question_count': len(questions),
                'time_limit': time_limit,
                'target_topics': target_topics,
                'user_ability_level': user_ability,
                'questions': questions,
                'strategy': question_strategy,
                'created_at': datetime.now().isoformat()
            }
            
            print(f"✅ 生成自适应考试: {len(questions)}题，预估能力水平: {user_ability:.2f}")
            return exam_config
            
        except Exception as e:
            print(f"❌ 生成自适应考试失败: {e}")
            return {}
            
    def _estimate_user_ability(self, mastery_data: Dict, topics: List[str]) -> float:
        """估算用户能力水平"""
        try:
            if not mastery_data:
                return 0.5  # 默认中等水平
            
            # 计算相关主题的平均掌握度
            relevant_masteries = []
            for topic in topics:
                if topic in mastery_data:
                    topic_data = mastery_data[topic]
                    relevant_masteries.append(topic_data['average_mastery'])
            
            if relevant_masteries:
                ability = np.mean(relevant_masteries)
                # 考虑置信度调整
                confidence_adjustment = 0.1  # 可以根据置信度进一步调整
                return max(0.1, min(0.9, ability - confidence_adjustment))
            else:
                return 0.5
                
        except Exception as e:
            print(f"❌ 估算用户能力失败: {e}")
            return 0.5
            
    def _generate_question_strategy(self, 
                                  user_ability: float, 
                                  question_count: int,
                                  topics: List[str],
                                  mastery_data: Dict) -> Dict:
        """生成题目分布策略"""
        try:
            strategy = {
                'total_questions': question_count,
                'difficulty_distribution': {},
                'topic_distribution': {},
                'focus_areas': []
            }
            
            # 根据用户能力调整难度分布
            if user_ability < 0.4:
                # 初学者：更多简单题
                strategy['difficulty_distribution'] = {
                    'easy': int(question_count * 0.6),
                    'medium': int(question_count * 0.3),
                    'hard': int(question_count * 0.1)
                }
            elif user_ability < 0.7:
                # 中等水平：平衡分布
                strategy['difficulty_distribution'] = {
                    'easy': int(question_count * 0.3),
                    'medium': int(question_count * 0.5),
                    'hard': int(question_count * 0.2)
                }
            else:
                # 高水平：更多挑战题
                strategy['difficulty_distribution'] = {
                    'easy': int(question_count * 0.2),
                    'medium': int(question_count * 0.4),
                    'hard': int(question_count * 0.4)
                }
            
            # 确保总数正确
            total_assigned = sum(strategy['difficulty_distribution'].values())
            if total_assigned < question_count:
                strategy['difficulty_distribution']['medium'] += question_count - total_assigned
            
            # 主题分布策略
            topic_weights = {}
            for topic in topics:
                if topic in mastery_data:
                    mastery = mastery_data[topic]['average_mastery']
                    # 薄弱主题获得更多权重
                    weight = 1.0 - mastery + 0.2  # 确保最小权重
                    topic_weights[topic] = weight
                else:
                    topic_weights[topic] = 1.0
            
            # 归一化权重
            total_weight = sum(topic_weights.values())
            for topic in topic_weights:
                topic_weights[topic] /= total_weight
                strategy['topic_distribution'][topic] = int(question_count * topic_weights[topic])
            
            # 确保总数正确
            assigned_questions = sum(strategy['topic_distribution'].values())
            if assigned_questions < question_count:
                # 将剩余题目分配给权重最高的主题
                max_topic = max(topic_weights.keys(), key=lambda k: topic_weights[k])
                strategy['topic_distribution'][max_topic] += question_count - assigned_questions
            
            # 识别重点关注领域
            for topic, data in mastery_data.items():
                if data['average_mastery'] < 0.6:
                    strategy['focus_areas'].append({
                        'topic': topic,
                        'reason': 'low_mastery',
                        'mastery_level': data['average_mastery']
                    })
            
            return strategy
            
        except Exception as e:
            print(f"❌ 生成题目策略失败: {e}")
            return {}
            
    def _select_questions_by_strategy(self, strategy: Dict) -> List[Dict]:
        """根据策略选择题目"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            selected_questions = []
            
            # 按难度和主题选择题目
            for topic, topic_count in strategy.get('topic_distribution', {}).items():
                for difficulty, diff_count in strategy.get('difficulty_distribution', {}).items():
                    # 计算该主题该难度需要的题目数
                    needed = int(topic_count * (diff_count / strategy['total_questions']))
                    
                    if needed > 0:
                        # 从数据库查询题目
                        cursor.execute('''
                            SELECT id, question_text, options, correct_answer, 
                                   explanation, difficulty, topic, subtopic
                            FROM questions 
                            WHERE topic LIKE ? AND difficulty = ?
                            ORDER BY RANDOM()
                            LIMIT ?
                        ''', (f'%{topic}%', difficulty, needed))
                        
                        questions = cursor.fetchall()
                        
                        for q in questions:
                            question_data = {
                                'id': q[0],
                                'question_text': q[1],
                                'options': json.loads(q[2]) if q[2] else [],
                                'correct_answer': q[3],
                                'explanation': q[4],
                                'difficulty': q[5],
                                'topic': q[6],
                                'subtopic': q[7],
                                'selected_reason': f"主题:{topic}, 难度:{difficulty}"
                            }
                            selected_questions.append(question_data)
            
            # 如果题目不够，随机补充
            if len(selected_questions) < strategy['total_questions']:
                needed = strategy['total_questions'] - len(selected_questions)
                
                cursor.execute('''
                    SELECT id, question_text, options, correct_answer, 
                           explanation, difficulty, topic, subtopic
                    FROM questions 
                    ORDER BY RANDOM()
                    LIMIT ?
                ''', (needed,))
                
                additional_questions = cursor.fetchall()
                
                for q in additional_questions:
                    question_data = {
                        'id': q[0],
                        'question_text': q[1],
                        'options': json.loads(q[2]) if q[2] else [],
                        'correct_answer': q[3],
                        'explanation': q[4],
                        'difficulty': q[5],
                        'topic': q[6],
                        'subtopic': q[7],
                        'selected_reason': "随机补充"
                    }
                    selected_questions.append(question_data)
            
            # 打乱题目顺序
            random.shuffle(selected_questions)
            
            return selected_questions[:strategy['total_questions']]
            
        except Exception as e:
            print(f"❌ 选择题目失败: {e}")
            return []
            
    def generate_practice_set(self, 
                            weak_topics: List[str] = None,
                            question_count: int = 10) -> Dict:
        """生成针对薄弱环节的练习题集"""
        try:
            if not weak_topics:
                # 自动识别薄弱主题
                mastery_data = self.learning_analytics.get_knowledge_mastery_overview()
                weak_topics = []
                for topic, data in mastery_data.items():
                    if data['average_mastery'] < 0.6:
                        weak_topics.append(topic)
                
                if not weak_topics:
                    weak_topics = ['通用']  # 默认主题
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            questions = []
            questions_per_topic = max(1, question_count // len(weak_topics))
            
            for topic in weak_topics:
                cursor.execute('''
                    SELECT id, question_text, options, correct_answer, 
                           explanation, difficulty, topic, subtopic
                    FROM questions 
                    WHERE topic LIKE ?
                    ORDER BY RANDOM()
                    LIMIT ?
                ''', (f'%{topic}%', questions_per_topic))
                
                topic_questions = cursor.fetchall()
                
                for q in topic_questions:
                    question_data = {
                        'id': q[0],
                        'question_text': q[1],
                        'options': json.loads(q[2]) if q[2] else [],
                        'correct_answer': q[3],
                        'explanation': q[4],
                        'difficulty': q[5],
                        'topic': q[6],
                        'subtopic': q[7],
                        'practice_reason': f"薄弱主题强化: {topic}"
                    }
                    questions.append(question_data)
            
            # 如果题目不够，随机补充
            if len(questions) < question_count:
                needed = question_count - len(questions)
                cursor.execute('''
                    SELECT id, question_text, options, correct_answer, 
                           explanation, difficulty, topic, subtopic
                    FROM questions 
                    ORDER BY RANDOM()
                    LIMIT ?
                ''', (needed,))
                
                additional = cursor.fetchall()
                for q in additional:
                    question_data = {
                        'id': q[0],
                        'question_text': q[1],
                        'options': json.loads(q[2]) if q[2] else [],
                        'correct_answer': q[3],
                        'explanation': q[4],
                        'difficulty': q[5],
                        'topic': q[6],
                        'subtopic': q[7],
                        'practice_reason': "补充练习"
                    }
                    questions.append(question_data)
            
            practice_set = {
                'set_id': f"practice_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'title': f"薄弱环节强化练习",
                'description': f"针对 {', '.join(weak_topics)} 的专项练习",
                'target_topics': weak_topics,
                'question_count': len(questions),
                'questions': questions[:question_count],
                'created_at': datetime.now().isoformat()
            }
            
            print(f"✅ 生成练习题集: {len(questions)}题，主题: {weak_topics}")
            return practice_set
            
        except Exception as e:
            print(f"❌ 生成练习题集失败: {e}")
            return {}
            
    def analyze_question_effectiveness(self) -> Dict:
        """分析题目有效性"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 分析题目的答题情况
            cursor.execute('''
                SELECT q.id, q.question_text, q.difficulty, q.topic,
                       COUNT(ea.id) as attempt_count,
                       AVG(CASE WHEN ea.selected_answer = q.correct_answer THEN 1.0 ELSE 0.0 END) as success_rate,
                       AVG(ea.time_spent) as avg_time_spent
                FROM questions q
                LEFT JOIN exam_answers ea ON q.id = ea.question_id
                GROUP BY q.id
                HAVING attempt_count > 0
                ORDER BY success_rate, attempt_count DESC
            ''')
            
            results = cursor.fetchall()
            
            analysis = {
                'total_questions_analyzed': len(results),
                'question_effectiveness': [],
                'recommendations': []
            }
            
            for row in results:
                q_id, text, difficulty, topic, attempts, success_rate, avg_time = row
                
                effectiveness = {
                    'question_id': q_id,
                    'question_text': text[:100] + '...' if len(text) > 100 else text,
                    'difficulty': difficulty,
                    'topic': topic,
                    'attempt_count': attempts,
                    'success_rate': success_rate or 0,
                    'average_time': avg_time or 0,
                    'effectiveness_score': self._calculate_effectiveness_score(
                        success_rate or 0, attempts, avg_time or 0, difficulty
                    )
                }
                
                analysis['question_effectiveness'].append(effectiveness)
            
            # 生成改进建议
            analysis['recommendations'] = self._generate_question_recommendations(
                analysis['question_effectiveness']
            )
            
            return analysis
            
        except Exception as e:
            print(f"❌ 分析题目有效性失败: {e}")
            return {}
            
    def _calculate_effectiveness_score(self, success_rate: float, attempts: int, 
                                     avg_time: float, difficulty: str) -> float:
        """计算题目有效性分数"""
        try:
            # 基础分数基于成功率
            base_score = success_rate
            
            # 根据难度调整期望成功率
            expected_rates = {'easy': 0.8, 'medium': 0.6, 'hard': 0.4}
            expected_rate = expected_rates.get(difficulty, 0.6)
            
            # 计算与期望的偏差
            deviation = abs(success_rate - expected_rate)
            deviation_penalty = deviation * 0.5
            
            # 考虑尝试次数（更多尝试意味着更可靠的数据）
            reliability_bonus = min(0.2, attempts * 0.01)
            
            # 时间因素（合理的答题时间）
            time_factor = 1.0
            if avg_time > 0:
                if avg_time < 30:  # 太快可能是猜测
                    time_factor = 0.8
                elif avg_time > 300:  # 太慢可能题目有问题
                    time_factor = 0.9
            
            effectiveness = (base_score - deviation_penalty + reliability_bonus) * time_factor
            return max(0.0, min(1.0, effectiveness))
            
        except Exception as e:
            print(f"❌ 计算有效性分数失败: {e}")
            return 0.5
            
    def _generate_question_recommendations(self, effectiveness_data: List[Dict]) -> List[str]:
        """生成题目改进建议"""
        try:
            recommendations = []
            
            # 找出效果不佳的题目
            poor_questions = [q for q in effectiveness_data if q['effectiveness_score'] < 0.4]
            if poor_questions:
                recommendations.append(
                    f"发现 {len(poor_questions)} 道题目效果不佳，建议重新审核或替换"
                )
            
            # 找出成功率异常的题目
            too_easy = [q for q in effectiveness_data if q['success_rate'] > 0.9 and q['difficulty'] != 'easy']
            if too_easy:
                recommendations.append(
                    f"发现 {len(too_easy)} 道题目可能过于简单，建议调整难度"
                )
            
            too_hard = [q for q in effectiveness_data if q['success_rate'] < 0.2 and q['difficulty'] != 'hard']
            if too_hard:
                recommendations.append(
                    f"发现 {len(too_hard)} 道题目可能过于困难，建议调整难度或提供更好的解释"
                )
            
            # 找出答题时间异常的题目
            time_issues = [q for q in effectiveness_data if q['average_time'] > 300 or q['average_time'] < 10]
            if time_issues:
                recommendations.append(
                    f"发现 {len(time_issues)} 道题目答题时间异常，建议检查题目表述"
                )
            
            return recommendations
            
        except Exception as e:
            print(f"❌ 生成题目建议失败: {e}")
            return []
