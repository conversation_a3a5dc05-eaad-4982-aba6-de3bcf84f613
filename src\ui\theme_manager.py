#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器
提供深色/浅色主题切换和自定义主题功能
"""

import tkinter as tk
from tkinter import ttk
import json
import os

class ThemeManager:
    def __init__(self):
        """初始化主题管理器"""
        self.current_theme = "light"
        self.themes = self.load_themes()
        self.callbacks = []  # 主题变化回调函数
        
    def load_themes(self):
        """加载主题配置"""
        return {
            "light": {
                "name": "浅色主题",
                "colors": {
                    # 背景色
                    "bg_primary": "#FFFFFF",
                    "bg_secondary": "#F5F5F5", 
                    "bg_tertiary": "#EEEEEE",
                    "bg_accent": "#E3F2FD",
                    
                    # 文字色
                    "text_primary": "#212121",
                    "text_secondary": "#757575",
                    "text_tertiary": "#9E9E9E",
                    "text_inverse": "#FFFFFF",
                    
                    # 强调色
                    "accent_primary": "#2196F3",
                    "accent_secondary": "#1976D2",
                    "success": "#4CAF50",
                    "warning": "#FF9800",
                    "error": "#F44336",
                    "info": "#2196F3",
                    
                    # 边框和分割线
                    "border": "#E0E0E0",
                    "divider": "#BDBDBD",
                    
                    # 按钮色
                    "button_primary": "#2196F3",
                    "button_secondary": "#757575",
                    "button_success": "#4CAF50",
                    "button_warning": "#FF9800",
                    "button_danger": "#F44336",
                    
                    # 状态色
                    "hover": "#E3F2FD",
                    "active": "#BBDEFB",
                    "disabled": "#F5F5F5",
                    "selected": "#E3F2FD"
                }
            },
            "dark": {
                "name": "深色主题",
                "colors": {
                    # 背景色
                    "bg_primary": "#121212",
                    "bg_secondary": "#1E1E1E",
                    "bg_tertiary": "#2D2D2D",
                    "bg_accent": "#1A237E",
                    
                    # 文字色
                    "text_primary": "#FFFFFF",
                    "text_secondary": "#B3B3B3",
                    "text_tertiary": "#808080",
                    "text_inverse": "#121212",
                    
                    # 强调色
                    "accent_primary": "#64B5F6",
                    "accent_secondary": "#42A5F5",
                    "success": "#81C784",
                    "warning": "#FFB74D",
                    "error": "#E57373",
                    "info": "#64B5F6",
                    
                    # 边框和分割线
                    "border": "#404040",
                    "divider": "#505050",
                    
                    # 按钮色
                    "button_primary": "#64B5F6",
                    "button_secondary": "#757575",
                    "button_success": "#81C784",
                    "button_warning": "#FFB74D",
                    "button_danger": "#E57373",
                    
                    # 状态色
                    "hover": "#2D2D2D",
                    "active": "#404040",
                    "disabled": "#1E1E1E",
                    "selected": "#1A237E"
                }
            },
            "blue": {
                "name": "蓝色主题",
                "colors": {
                    # 背景色
                    "bg_primary": "#F8FAFF",
                    "bg_secondary": "#E3F2FD",
                    "bg_tertiary": "#BBDEFB",
                    "bg_accent": "#2196F3",
                    
                    # 文字色
                    "text_primary": "#0D47A1",
                    "text_secondary": "#1565C0",
                    "text_tertiary": "#1976D2",
                    "text_inverse": "#FFFFFF",
                    
                    # 强调色
                    "accent_primary": "#2196F3",
                    "accent_secondary": "#1976D2",
                    "success": "#4CAF50",
                    "warning": "#FF9800",
                    "error": "#F44336",
                    "info": "#2196F3",
                    
                    # 边框和分割线
                    "border": "#90CAF9",
                    "divider": "#64B5F6",
                    
                    # 按钮色
                    "button_primary": "#2196F3",
                    "button_secondary": "#1976D2",
                    "button_success": "#4CAF50",
                    "button_warning": "#FF9800",
                    "button_danger": "#F44336",
                    
                    # 状态色
                    "hover": "#E3F2FD",
                    "active": "#BBDEFB",
                    "disabled": "#F5F5F5",
                    "selected": "#E3F2FD"
                }
            }
        }
        
    def get_current_theme(self):
        """获取当前主题"""
        return self.themes.get(self.current_theme, self.themes["light"])
        
    def get_color(self, color_name):
        """获取指定颜色"""
        theme = self.get_current_theme()
        return theme["colors"].get(color_name, "#000000")
        
    def set_theme(self, theme_name):
        """设置主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self.apply_theme()
            self.notify_callbacks()
            
    def apply_theme(self):
        """应用主题到ttk样式"""
        theme = self.get_current_theme()
        colors = theme["colors"]
        
        style = ttk.Style()
        
        # 配置基础样式
        style.configure(".",
                       background=colors["bg_primary"],
                       foreground=colors["text_primary"],
                       bordercolor=colors["border"],
                       focuscolor=colors["accent_primary"])
        
        # 配置按钮样式
        style.configure("Themed.TButton",
                       background=colors["button_primary"],
                       foreground=colors["text_inverse"],
                       borderwidth=0,
                       focuscolor="none")
        
        style.map("Themed.TButton",
                 background=[("active", colors["hover"]),
                           ("pressed", colors["active"])])
        
        # 配置标签样式
        style.configure("Themed.TLabel",
                       background=colors["bg_primary"],
                       foreground=colors["text_primary"])
        
        style.configure("Title.TLabel",
                       background=colors["bg_primary"],
                       foreground=colors["text_primary"],
                       font=("Microsoft YaHei", 16, "bold"))
        
        style.configure("Subtitle.TLabel",
                       background=colors["bg_primary"],
                       foreground=colors["text_secondary"],
                       font=("Microsoft YaHei", 12))
        
        # 配置框架样式
        style.configure("Themed.TFrame",
                       background=colors["bg_primary"],
                       borderwidth=1,
                       relief="solid",
                       bordercolor=colors["border"])
        
        style.configure("Card.TFrame",
                       background=colors["bg_secondary"],
                       borderwidth=1,
                       relief="solid",
                       bordercolor=colors["border"])
        
        # 配置笔记本样式
        style.configure("Themed.TNotebook",
                       background=colors["bg_primary"],
                       borderwidth=0)
        
        style.configure("Themed.TNotebook.Tab",
                       background=colors["bg_secondary"],
                       foreground=colors["text_primary"],
                       padding=[20, 10])
        
        style.map("Themed.TNotebook.Tab",
                 background=[("selected", colors["accent_primary"]),
                           ("active", colors["hover"])],
                 foreground=[("selected", colors["text_inverse"])])
        
        # 配置进度条样式
        style.configure("Themed.Horizontal.TProgressbar",
                       background=colors["accent_primary"],
                       troughcolor=colors["bg_tertiary"],
                       borderwidth=0)
        
    def add_callback(self, callback):
        """添加主题变化回调"""
        self.callbacks.append(callback)
        
    def remove_callback(self, callback):
        """移除主题变化回调"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            
    def notify_callbacks(self):
        """通知所有回调函数"""
        for callback in self.callbacks:
            try:
                callback(self.current_theme)
            except Exception as e:
                print(f"主题回调错误: {e}")
                
    def get_theme_list(self):
        """获取主题列表"""
        return [(name, theme["name"]) for name, theme in self.themes.items()]
        
    def create_themed_widget(self, widget_class, parent, **kwargs):
        """创建带主题的控件"""
        theme = self.get_current_theme()
        colors = theme["colors"]
        
        # 设置默认主题颜色
        if "bg" not in kwargs:
            kwargs["bg"] = colors["bg_primary"]
        if "fg" not in kwargs:
            kwargs["fg"] = colors["text_primary"]
            
        return widget_class(parent, **kwargs)
        
    def apply_to_widget(self, widget, widget_type="default"):
        """将主题应用到指定控件"""
        theme = self.get_current_theme()
        colors = theme["colors"]

        try:
            # 检查控件类型
            widget_class = widget.__class__.__name__

            # 只对支持bg/fg属性的控件应用主题
            if widget_class in ['Tk', 'Toplevel', 'Frame', 'Label', 'Button', 'Entry', 'Text', 'Listbox']:
                if widget_type == "button":
                    widget.configure(
                        bg=colors["button_primary"],
                        fg=colors["text_inverse"],
                        activebackground=colors["hover"],
                        activeforeground=colors["text_inverse"]
                    )
                elif widget_type == "label":
                    widget.configure(
                        bg=colors["bg_primary"],
                        fg=colors["text_primary"]
                    )
                elif widget_type == "frame":
                    widget.configure(bg=colors["bg_primary"])
                elif widget_type == "entry":
                    widget.configure(
                        bg=colors["bg_secondary"],
                        fg=colors["text_primary"],
                        insertbackground=colors["text_primary"]
                    )
                else:
                    # 默认样式，只设置背景色
                    if widget_class in ['Tk', 'Toplevel', 'Frame']:
                        widget.configure(bg=colors["bg_primary"])
                    elif widget_class in ['Label', 'Button']:
                        widget.configure(
                            bg=colors["bg_primary"],
                            fg=colors["text_primary"]
                        )
            # 对于ttk控件，使用样式而不是直接配置
            elif widget_class.startswith('T'):
                # ttk控件通过样式系统处理，在apply_theme方法中已处理
                pass

        except Exception as e:
            # 静默处理不支持的属性，避免干扰用户体验
            pass

# 全局主题管理器实例
theme_manager = ThemeManager()

def get_theme_manager():
    """获取全局主题管理器"""
    return theme_manager
