#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统诊断和修复工具
检查常见问题并提供解决方案
"""

import sys
import os
import subprocess
import importlib
import threading
import time

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("   ❌ Python版本过低，需要3.7或更高版本")
        return False
    else:
        print("   ✅ Python版本符合要求")
        return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = {
        'tkinter': '内置GUI库',
        'sqlite3': '内置数据库',
        'requests': 'HTTP请求库',
        'threading': '内置线程库',
        'json': '内置JSON库'
    }
    
    optional_packages = {
        'numpy': '数值计算（可选）',
        'matplotlib': '图表绘制（可选）',
        'pandas': '数据处理（可选）',
        'PyQt6': '现代UI框架（可选）'
    }
    
    missing_required = []
    missing_optional = []
    
    # 检查必需包
    for package, description in required_packages.items():
        try:
            importlib.import_module(package)
            print(f"   ✅ {package}: {description}")
        except ImportError:
            print(f"   ❌ {package}: {description} - 缺失")
            missing_required.append(package)
    
    # 检查可选包
    for package, description in optional_packages.items():
        try:
            importlib.import_module(package)
            print(f"   ✅ {package}: {description}")
        except ImportError:
            print(f"   ⚠️ {package}: {description} - 未安装")
            missing_optional.append(package)
    
    return missing_required, missing_optional

def check_files():
    """检查关键文件"""
    print("\n📁 检查关键文件...")
    
    critical_files = [
        'main.py',
        'src/ui/main_window.py',
        'src/utils/database_manager.py',
        'src/utils/config_manager.py'
    ]
    
    optional_files = [
        'main_stable.py',
        'main_optimized.py',
        'src/ui/splash_screen.py',
        'src/core/learning_analytics.py',
        'src/core/intelligent_assistant.py'
    ]
    
    missing_critical = []
    missing_optional = []
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - 缺失")
            missing_critical.append(file_path)
    
    for file_path in optional_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ⚠️ {file_path} - 缺失")
            missing_optional.append(file_path)
    
    return missing_critical, missing_optional

def check_database():
    """检查数据库"""
    print("\n💾 检查数据库...")
    
    try:
        import sqlite3
        
        # 检查数据库文件
        db_files = ['exam_system.db', 'data/exam_system.db']
        db_found = False
        
        for db_file in db_files:
            if os.path.exists(db_file):
                print(f"   ✅ 找到数据库文件: {db_file}")
                db_found = True
                
                # 测试数据库连接
                try:
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    print(f"   📊 数据库包含 {len(tables)} 个表")
                    conn.close()
                except Exception as e:
                    print(f"   ⚠️ 数据库连接测试失败: {e}")
                break
        
        if not db_found:
            print("   ⚠️ 未找到数据库文件，将在首次运行时创建")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        return False

def check_threading_issues():
    """检查线程问题"""
    print("\n🧵 检查线程问题...")
    
    try:
        # 测试线程创建和销毁
        def test_thread():
            time.sleep(0.1)
        
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
        thread.join(timeout=1)
        
        if thread.is_alive():
            print("   ⚠️ 线程可能存在问题")
            return False
        else:
            print("   ✅ 线程功能正常")
            return True
            
    except Exception as e:
        print(f"   ❌ 线程测试失败: {e}")
        return False

def suggest_solutions(missing_required, missing_optional, missing_critical, missing_optional_files):
    """提供解决方案建议"""
    print("\n🔧 问题诊断和解决方案:")
    print("=" * 50)
    
    if missing_required:
        print("❌ 严重问题 - 缺少必需依赖:")
        for package in missing_required:
            print(f"   • {package}")
        print("\n解决方案:")
        print("   pip install " + " ".join(missing_required))
    
    if missing_critical:
        print("\n❌ 严重问题 - 缺少关键文件:")
        for file_path in missing_critical:
            print(f"   • {file_path}")
        print("\n解决方案:")
        print("   1. 重新下载完整的项目文件")
        print("   2. 检查文件是否被意外删除")
        print("   3. 确保在正确的目录中运行")
    
    if missing_optional:
        print("\n⚠️ 可选功能受限 - 缺少可选依赖:")
        for package in missing_optional:
            print(f"   • {package}")
        print("\n解决方案 (可选):")
        print("   pip install " + " ".join(missing_optional))
    
    if missing_optional_files:
        print("\n⚠️ 部分功能不可用 - 缺少可选文件:")
        for file_path in missing_optional_files:
            print(f"   • {file_path}")
        print("\n影响:")
        print("   • 某些高级功能可能不可用")
        print("   • 基础功能仍然可以正常使用")

def recommend_startup_method():
    """推荐启动方法"""
    print("\n🚀 推荐的启动方法:")
    print("=" * 30)
    
    if os.path.exists('main_stable.py'):
        print("1. 🥇 推荐: python main_stable.py")
        print("   • 稳定性最好，避免线程问题")
        print("   • 快速启动，无启动画面")
    
    if os.path.exists('run_safe.py'):
        print("2. 🥈 备选: python run_safe.py")
        print("   • 包含异常处理和信号捕获")
        print("   • 安全退出机制")
    
    if os.path.exists('main_optimized.py'):
        print("3. 🥉 可选: python main_optimized.py")
        print("   • 包含启动画面和完整功能")
        print("   • 可能存在线程问题")
    
    print("4. 🔧 基础: python main.py")
    print("   • 标准版本，兼容性好")
    
    if os.path.exists('run_menu.bat'):
        print("\n💡 或者使用启动菜单:")
        print("   双击 run_menu.bat 选择启动方式")

def main():
    """主函数"""
    print("🔍 智能化考试系统 - 诊断和修复工具")
    print("=" * 50)
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查依赖包
    missing_required, missing_optional = check_dependencies()
    
    # 检查文件
    missing_critical, missing_optional_files = check_files()
    
    # 检查数据库
    db_ok = check_database()
    
    # 检查线程问题
    threading_ok = check_threading_issues()
    
    # 提供解决方案
    suggest_solutions(missing_required, missing_optional, missing_critical, missing_optional_files)
    
    # 推荐启动方法
    recommend_startup_method()
    
    # 总结
    print("\n📊 诊断总结:")
    print("=" * 20)
    
    if python_ok and not missing_required and not missing_critical:
        print("✅ 系统状态良好，可以正常运行")
        if missing_optional or missing_optional_files:
            print("⚠️ 部分可选功能可能受限")
    else:
        print("❌ 发现严重问题，需要修复后才能正常运行")
    
    if not threading_ok:
        print("⚠️ 建议使用 main_stable.py 或 run_safe.py 启动")
    
    print("\n🎯 快速启动建议:")
    if os.path.exists('main_stable.py'):
        print("   python main_stable.py")
    elif os.path.exists('run_safe.py'):
        print("   python run_safe.py")
    else:
        print("   python main.py")

if __name__ == "__main__":
    main()
