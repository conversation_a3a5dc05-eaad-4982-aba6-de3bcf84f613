@echo off
chcp 65001 >nul
echo 启动考试系统...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7或更高版本
    echo 您可以从 https://www.python.org/downloads/ 下载安装
    pause
    exit /b 1
)

REM 显示Python版本
echo 检测到Python版本：
python --version

REM 检查依赖是否安装
echo.
echo 🔍 检查依赖包...

REM 检查基础依赖
pip show requests >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 缺少基础依赖，正在安装...
    pip install requests
    if errorlevel 1 (
        echo ❌ 基础依赖安装失败
        echo 请检查网络连接或手动运行：pip install requests
        pause
        exit /b 1
    )
    echo ✅ 基础依赖安装完成！
) else (
    echo ✅ 基础依赖检查完成
)

REM 检查PyQt6（现代UI需要）
echo 🎨 检查现代UI依赖...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ PyQt6 未安装，现代UI功能将不可用
    echo 💡 如需体验现代UI，请运行：pip install PyQt6
    set PYQT6_AVAILABLE=false
) else (
    echo ✅ PyQt6 已安装，支持现代UI功能
    set PYQT6_AVAILABLE=true
)

REM 检查可选依赖
echo 📊 检查智能功能依赖...
python -c "import numpy, matplotlib" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 部分智能功能依赖未安装（numpy, matplotlib）
    echo 💡 如需完整功能，请运行：pip install numpy matplotlib pandas
) else (
    echo ✅ 智能功能依赖完整
)

REM 启动程序
echo.
echo 启动考试系统 v2.0 (智能化版本)...
echo 程序窗口将在几秒钟后出现...
echo.
echo 🚀 正在启动优化后的智能考试系统...
echo 📊 包含学习分析、智能助手、个性化出题等功能
echo.

REM 优先使用稳定版本（避免线程问题）
if exist main_stable.py (
    echo 使用稳定版本启动（推荐）...
    python main_stable.py
) else if exist main_optimized.py (
    echo 使用优化版本启动...
    python main_optimized.py
) else (
    echo 使用标准版本启动...
    python main.py
)

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    echo.
    echo 可尝试的解决方案：
    echo 1. 手动运行：python main_stable.py （推荐）
    echo 2. 或运行：python main_optimized.py
    echo 3. 或运行：python main.py
    echo 4. 检查是否缺少依赖包
    echo.
    pause
)
