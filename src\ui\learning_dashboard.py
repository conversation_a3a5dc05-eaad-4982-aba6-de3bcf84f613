#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习仪表板
提供交互式图表和学习数据可视化
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from typing import Dict, List

# 尝试导入matplotlib相关模块
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib不可用，图表功能将使用文本替代")

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

class LearningDashboard:
    def __init__(self, parent, learning_analytics, theme_manager=None):
        """初始化学习仪表板"""
        self.parent = parent
        self.learning_analytics = learning_analytics
        self.theme_manager = theme_manager
        
        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("📊 学习数据仪表板")
        self.window.geometry("1200x800")
        self.window.transient(parent)
        
        # 设置matplotlib样式（如果可用）
        if MATPLOTLIB_AVAILABLE:
            try:
                plt.style.use('seaborn-v0_8' if 'seaborn-v0_8' in plt.style.available else 'default')
            except:
                pass
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
        
        # 创建界面
        self.create_ui()
        
        # 加载数据
        self.refresh_dashboard()
        
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题栏
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(title_frame, text="📊 学习数据仪表板", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # 刷新按钮
        refresh_btn = ttk.Button(title_frame, text="🔄 刷新数据", 
                                command=self.refresh_dashboard)
        refresh_btn.pack(side=tk.RIGHT)
        
        # 导出按钮
        export_btn = ttk.Button(title_frame, text="📤 导出报告", 
                               command=self.export_report)
        export_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个标签页
        self.create_overview_tab()
        self.create_learning_curve_tab()
        self.create_knowledge_mastery_tab()
        self.create_performance_analysis_tab()
        self.create_recommendations_tab()
        
    def create_overview_tab(self):
        """创建概览标签页"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="📈 学习概览")
        
        # 创建统计卡片区域
        cards_frame = ttk.Frame(overview_frame)
        cards_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 统计卡片
        self.stats_cards = {}
        card_configs = [
            ("total_sessions", "总学习次数", "🎯"),
            ("total_time", "总学习时间", "⏱️"),
            ("total_questions", "总答题数", "📝"),
            ("overall_accuracy", "总体正确率", "🎯")
        ]
        
        for i, (key, title, icon) in enumerate(card_configs):
            card_frame = ttk.LabelFrame(cards_frame, text=f"{icon} {title}", padding=10)
            card_frame.grid(row=0, column=i, padx=5, pady=5, sticky="ew")
            
            value_label = ttk.Label(card_frame, text="--", 
                                   font=("Microsoft YaHei", 14, "bold"))
            value_label.pack()
            
            self.stats_cards[key] = value_label
        
        # 配置网格权重
        for i in range(4):
            cards_frame.grid_columnconfigure(i, weight=1)
        
        # 最近活动区域
        activity_frame = ttk.LabelFrame(overview_frame, text="📅 最近7天活动", padding=10)
        activity_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # 创建图表区域
        if MATPLOTLIB_AVAILABLE:
            self.overview_figure = Figure(figsize=(10, 4), dpi=100)
            self.overview_canvas = FigureCanvasTkAgg(self.overview_figure, activity_frame)
            self.overview_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        else:
            # 使用文本替代图表
            self.overview_text = tk.Text(activity_frame, height=8, wrap=tk.WORD)
            self.overview_text.pack(fill=tk.BOTH, expand=True)
        
    def create_learning_curve_tab(self):
        """创建学习曲线标签页"""
        curve_frame = ttk.Frame(self.notebook)
        self.notebook.add(curve_frame, text="📈 学习曲线")
        
        # 控制面板
        control_frame = ttk.Frame(curve_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(control_frame, text="时间范围:").pack(side=tk.LEFT)
        
        self.time_range_var = tk.StringVar(value="30")
        time_range_combo = ttk.Combobox(control_frame, textvariable=self.time_range_var,
                                       values=["7", "14", "30", "60", "90"], width=10)
        time_range_combo.pack(side=tk.LEFT, padx=(5, 10))
        time_range_combo.bind("<<ComboboxSelected>>", self.update_learning_curve)
        
        ttk.Label(control_frame, text="天").pack(side=tk.LEFT)
        
        # 图表区域
        chart_frame = ttk.Frame(curve_frame)
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.curve_figure = Figure(figsize=(12, 8), dpi=100)
        self.curve_canvas = FigureCanvasTkAgg(self.curve_figure, chart_frame)
        self.curve_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def create_knowledge_mastery_tab(self):
        """创建知识掌握度标签页"""
        mastery_frame = ttk.Frame(self.notebook)
        self.notebook.add(mastery_frame, text="🧠 知识掌握")
        
        # 分割面板
        paned_window = ttk.PanedWindow(mastery_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：知识点列表
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        
        ttk.Label(left_frame, text="📚 知识点掌握情况", 
                 font=("Microsoft YaHei", 12, "bold")).pack(pady=(0, 10))
        
        # 知识点树形视图
        columns = ("topic", "mastery", "confidence", "practice_count")
        self.mastery_tree = ttk.Treeview(left_frame, columns=columns, show="headings", height=15)
        
        self.mastery_tree.heading("topic", text="知识点")
        self.mastery_tree.heading("mastery", text="掌握度")
        self.mastery_tree.heading("confidence", text="置信度")
        self.mastery_tree.heading("practice_count", text="练习次数")
        
        self.mastery_tree.column("topic", width=200)
        self.mastery_tree.column("mastery", width=80)
        self.mastery_tree.column("confidence", width=80)
        self.mastery_tree.column("practice_count", width=80)
        
        # 滚动条
        mastery_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, 
                                         command=self.mastery_tree.yview)
        self.mastery_tree.configure(yscrollcommand=mastery_scrollbar.set)
        
        self.mastery_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        mastery_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧：掌握度可视化
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)
        
        ttk.Label(right_frame, text="📊 掌握度分布", 
                 font=("Microsoft YaHei", 12, "bold")).pack(pady=(0, 10))
        
        self.mastery_figure = Figure(figsize=(8, 6), dpi=100)
        self.mastery_canvas = FigureCanvasTkAgg(self.mastery_figure, right_frame)
        self.mastery_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def create_performance_analysis_tab(self):
        """创建表现分析标签页"""
        performance_frame = ttk.Frame(self.notebook)
        self.notebook.add(performance_frame, text="🎯 表现分析")
        
        # 分析选项
        options_frame = ttk.Frame(performance_frame)
        options_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(options_frame, text="分析维度:").pack(side=tk.LEFT)
        
        self.analysis_type_var = tk.StringVar(value="difficulty")
        analysis_combo = ttk.Combobox(options_frame, textvariable=self.analysis_type_var,
                                     values=["difficulty", "topic", "time_trend"], width=15)
        analysis_combo.pack(side=tk.LEFT, padx=(5, 10))
        analysis_combo.bind("<<ComboboxSelected>>", self.update_performance_analysis)
        
        # 图表区域
        self.performance_figure = Figure(figsize=(12, 8), dpi=100)
        self.performance_canvas = FigureCanvasTkAgg(self.performance_figure, performance_frame)
        self.performance_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
    def create_recommendations_tab(self):
        """创建推荐标签页"""
        rec_frame = ttk.Frame(self.notebook)
        self.notebook.add(rec_frame, text="💡 学习建议")
        
        # 推荐列表
        rec_list_frame = ttk.LabelFrame(rec_frame, text="🎯 个性化学习建议", padding=10)
        rec_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建文本区域显示推荐
        self.recommendations_text = tk.Text(rec_list_frame, wrap=tk.WORD, 
                                           font=("Microsoft YaHei", 11),
                                           height=20)
        
        rec_scrollbar = ttk.Scrollbar(rec_list_frame, orient=tk.VERTICAL, 
                                     command=self.recommendations_text.yview)
        self.recommendations_text.configure(yscrollcommand=rec_scrollbar.set)
        
        self.recommendations_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        rec_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 底部按钮
        button_frame = ttk.Frame(rec_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        generate_btn = ttk.Button(button_frame, text="🔄 重新生成建议", 
                                 command=self.generate_recommendations)
        generate_btn.pack(side=tk.LEFT)
        
        practice_btn = ttk.Button(button_frame, text="🎯 生成练习题", 
                                 command=self.generate_practice_questions)
        practice_btn.pack(side=tk.LEFT, padx=(10, 0))
        
    def refresh_dashboard(self):
        """刷新仪表板数据"""
        try:
            # 获取学习统计数据
            stats = self.learning_analytics.get_learning_statistics()
            
            # 更新统计卡片
            if stats:
                overall = stats.get('overall', {})
                self.stats_cards['total_sessions'].config(text=str(overall.get('total_sessions', 0)))
                
                total_time = overall.get('total_time', 0)
                hours = total_time // 3600
                minutes = (total_time % 3600) // 60
                self.stats_cards['total_time'].config(text=f"{hours}h {minutes}m")
                
                self.stats_cards['total_questions'].config(text=str(overall.get('total_questions', 0)))
                
                accuracy = overall.get('overall_accuracy', 0)
                self.stats_cards['overall_accuracy'].config(text=f"{accuracy:.1%}")
            
            # 更新各个图表
            self.update_overview_chart()
            self.update_learning_curve()
            self.update_knowledge_mastery()
            self.update_performance_analysis()
            self.generate_recommendations()
            
            print("✅ 仪表板数据刷新完成")
            
        except Exception as e:
            print(f"❌ 刷新仪表板失败: {e}")
            messagebox.showerror("错误", f"刷新数据失败：{str(e)}")
            
    def update_overview_chart(self):
        """更新概览图表"""
        try:
            self.overview_figure.clear()
            
            # 获取最近7天数据
            curve_data = self.learning_analytics.get_learning_curve(7)
            
            if curve_data and curve_data.get('dates'):
                ax = self.overview_figure.add_subplot(111)
                
                dates = curve_data['dates']
                sessions = curve_data['sessions']
                accuracy = curve_data['accuracy']
                
                # 双Y轴图表
                ax2 = ax.twinx()
                
                # 学习次数柱状图
                bars = ax.bar(dates, sessions, alpha=0.7, color='skyblue', label='学习次数')
                
                # 正确率折线图
                line = ax2.plot(dates, [a*100 for a in accuracy], 'ro-', color='red', label='正确率(%)')
                
                ax.set_xlabel('日期')
                ax.set_ylabel('学习次数', color='blue')
                ax2.set_ylabel('正确率(%)', color='red')
                
                ax.tick_params(axis='x', rotation=45)
                ax.legend(loc='upper left')
                ax2.legend(loc='upper right')
                
                self.overview_figure.suptitle('最近7天学习活动')
            else:
                ax = self.overview_figure.add_subplot(111)
                ax.text(0.5, 0.5, '暂无学习数据', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=14)
            
            self.overview_figure.tight_layout()
            self.overview_canvas.draw()
            
        except Exception as e:
            print(f"❌ 更新概览图表失败: {e}")
            
    def update_learning_curve(self, event=None):
        """更新学习曲线"""
        try:
            self.curve_figure.clear()
            
            days = int(self.time_range_var.get())
            curve_data = self.learning_analytics.get_learning_curve(days)
            
            if curve_data and curve_data.get('dates'):
                # 创建2x2子图
                ax1 = self.curve_figure.add_subplot(2, 2, 1)
                ax2 = self.curve_figure.add_subplot(2, 2, 2)
                ax3 = self.curve_figure.add_subplot(2, 2, 3)
                ax4 = self.curve_figure.add_subplot(2, 2, 4)
                
                dates = curve_data['dates']
                
                # 学习次数趋势
                ax1.plot(dates, curve_data['sessions'], 'b-o')
                ax1.set_title('学习次数趋势')
                ax1.set_ylabel('次数')
                ax1.tick_params(axis='x', rotation=45)
                
                # 学习时间趋势
                study_time_hours = [t/3600 for t in curve_data['study_time']]
                ax2.plot(dates, study_time_hours, 'g-o')
                ax2.set_title('学习时间趋势')
                ax2.set_ylabel('小时')
                ax2.tick_params(axis='x', rotation=45)
                
                # 答题数量趋势
                ax3.plot(dates, curve_data['questions'], 'orange', marker='o')
                ax3.set_title('答题数量趋势')
                ax3.set_ylabel('题数')
                ax3.tick_params(axis='x', rotation=45)
                
                # 正确率趋势
                accuracy_percent = [a*100 for a in curve_data['accuracy']]
                ax4.plot(dates, accuracy_percent, 'r-o')
                ax4.set_title('正确率趋势')
                ax4.set_ylabel('正确率(%)')
                ax4.tick_params(axis='x', rotation=45)
                
            else:
                ax = self.curve_figure.add_subplot(111)
                ax.text(0.5, 0.5, '暂无学习数据', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=14)
            
            self.curve_figure.tight_layout()
            self.curve_canvas.draw()
            
        except Exception as e:
            print(f"❌ 更新学习曲线失败: {e}")
            
    def update_knowledge_mastery(self):
        """更新知识掌握度"""
        try:
            # 清空树形视图
            for item in self.mastery_tree.get_children():
                self.mastery_tree.delete(item)
            
            # 获取掌握度数据
            mastery_data = self.learning_analytics.get_knowledge_mastery_overview()
            
            if mastery_data:
                # 填充树形视图
                for topic, data in mastery_data.items():
                    # 插入主题节点
                    topic_id = self.mastery_tree.insert("", tk.END, values=(
                        topic,
                        f"{data['average_mastery']:.2f}",
                        f"{data['average_confidence']:.2f}",
                        data['total_practice']
                    ))
                    
                    # 插入子主题节点
                    for subtopic_data in data['subtopics']:
                        self.mastery_tree.insert(topic_id, tk.END, values=(
                            f"  └ {subtopic_data['subtopic']}",
                            f"{subtopic_data['mastery_level']:.2f}",
                            f"{subtopic_data['confidence_score']:.2f}",
                            subtopic_data['practice_count']
                        ))
                
                # 更新掌握度分布图
                self.update_mastery_distribution(mastery_data)
            
        except Exception as e:
            print(f"❌ 更新知识掌握度失败: {e}")
            
    def update_mastery_distribution(self, mastery_data):
        """更新掌握度分布图"""
        try:
            self.mastery_figure.clear()
            
            if mastery_data:
                # 创建饼图和柱状图
                ax1 = self.mastery_figure.add_subplot(1, 2, 1)
                ax2 = self.mastery_figure.add_subplot(1, 2, 2)
                
                # 掌握度分布饼图
                mastery_levels = []
                for topic, data in mastery_data.items():
                    mastery_levels.append(data['average_mastery'])
                
                # 分类掌握度
                excellent = sum(1 for m in mastery_levels if m >= 0.8)
                good = sum(1 for m in mastery_levels if 0.6 <= m < 0.8)
                needs_improvement = sum(1 for m in mastery_levels if m < 0.6)
                
                sizes = [excellent, good, needs_improvement]
                labels = ['优秀(≥80%)', '良好(60-80%)', '需提高(<60%)']
                colors = ['#2ecc71', '#f39c12', '#e74c3c']
                
                if sum(sizes) > 0:
                    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                    ax1.set_title('知识点掌握度分布')
                
                # 各主题掌握度柱状图
                topics = list(mastery_data.keys())
                masteries = [mastery_data[topic]['average_mastery'] for topic in topics]
                
                bars = ax2.bar(range(len(topics)), masteries, color='skyblue')
                ax2.set_xlabel('知识点')
                ax2.set_ylabel('掌握度')
                ax2.set_title('各知识点掌握情况')
                ax2.set_xticks(range(len(topics)))
                ax2.set_xticklabels(topics, rotation=45, ha='right')
                ax2.set_ylim(0, 1)
                
                # 添加数值标签
                for bar, mastery in zip(bars, masteries):
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                            f'{mastery:.2f}', ha='center', va='bottom')
            
            self.mastery_figure.tight_layout()
            self.mastery_canvas.draw()
            
        except Exception as e:
            print(f"❌ 更新掌握度分布图失败: {e}")
            
    def update_performance_analysis(self, event=None):
        """更新表现分析"""
        try:
            self.performance_figure.clear()
            
            analysis_type = self.analysis_type_var.get()
            
            if analysis_type == "difficulty":
                self.analyze_by_difficulty()
            elif analysis_type == "topic":
                self.analyze_by_topic()
            elif analysis_type == "time_trend":
                self.analyze_time_trend()
            
            self.performance_figure.tight_layout()
            self.performance_canvas.draw()
            
        except Exception as e:
            print(f"❌ 更新表现分析失败: {e}")
            
    def analyze_by_difficulty(self):
        """按难度分析表现"""
        try:
            # 这里需要从数据库获取按难度分组的数据
            # 暂时使用模拟数据
            difficulties = ['简单', '中等', '困难']
            accuracies = [0.85, 0.65, 0.45]  # 模拟数据
            
            ax = self.performance_figure.add_subplot(111)
            bars = ax.bar(difficulties, accuracies, color=['#2ecc71', '#f39c12', '#e74c3c'])
            
            ax.set_title('不同难度题目表现')
            ax.set_ylabel('正确率')
            ax.set_ylim(0, 1)
            
            # 添加数值标签
            for bar, acc in zip(bars, accuracies):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{acc:.1%}', ha='center', va='bottom')
                       
        except Exception as e:
            print(f"❌ 按难度分析失败: {e}")
            
    def analyze_by_topic(self):
        """按主题分析表现"""
        try:
            mastery_data = self.learning_analytics.get_knowledge_mastery_overview()
            
            if mastery_data:
                topics = list(mastery_data.keys())
                masteries = [mastery_data[topic]['average_mastery'] for topic in topics]
                
                ax = self.performance_figure.add_subplot(111)
                bars = ax.barh(topics, masteries, color='skyblue')
                
                ax.set_title('各主题掌握情况')
                ax.set_xlabel('掌握度')
                ax.set_xlim(0, 1)
                
                # 添加数值标签
                for bar, mastery in zip(bars, masteries):
                    width = bar.get_width()
                    ax.text(width + 0.01, bar.get_y() + bar.get_height()/2.,
                           f'{mastery:.2f}', ha='left', va='center')
                           
        except Exception as e:
            print(f"❌ 按主题分析失败: {e}")
            
    def analyze_time_trend(self):
        """分析时间趋势"""
        try:
            curve_data = self.learning_analytics.get_learning_curve(30)
            
            if curve_data and curve_data.get('dates'):
                ax = self.performance_figure.add_subplot(111)
                
                dates = curve_data['dates']
                accuracy = [a*100 for a in curve_data['accuracy']]
                
                ax.plot(dates, accuracy, 'b-o', linewidth=2, markersize=6)
                ax.set_title('正确率时间趋势')
                ax.set_xlabel('日期')
                ax.set_ylabel('正确率(%)')
                ax.tick_params(axis='x', rotation=45)
                ax.grid(True, alpha=0.3)
                
                # 添加趋势线
                if len(dates) > 1:
                    z = np.polyfit(range(len(accuracy)), accuracy, 1)
                    p = np.poly1d(z)
                    ax.plot(dates, p(range(len(accuracy))), "r--", alpha=0.8, label='趋势线')
                    ax.legend()
                    
        except Exception as e:
            print(f"❌ 分析时间趋势失败: {e}")
            
    def generate_recommendations(self):
        """生成学习建议"""
        try:
            recommendations = self.learning_analytics.generate_personalized_recommendations()
            
            # 清空文本区域
            self.recommendations_text.delete(1.0, tk.END)
            
            if recommendations:
                content = "🎯 个性化学习建议\n\n"
                
                for i, rec in enumerate(recommendations, 1):
                    priority_icon = "🔴" if rec['priority'] == 1 else "🟡" if rec['priority'] == 2 else "🟢"
                    content += f"{priority_icon} {i}. {rec['title']}\n"
                    content += f"   {rec['content']}\n"
                    content += f"   💡 {rec['reason']}\n\n"
                
                content += "📚 学习小贴士：\n"
                content += "• 建议每天保持30-60分钟的学习时间\n"
                content += "• 重点关注掌握度低于60%的知识点\n"
                content += "• 定期复习已掌握的知识点，避免遗忘\n"
                content += "• 适当挑战更高难度的题目，提升能力\n"
                
            else:
                content = "暂无个性化建议，请先进行一些学习活动。"
            
            self.recommendations_text.insert(1.0, content)
            
        except Exception as e:
            print(f"❌ 生成学习建议失败: {e}")
            
    def generate_practice_questions(self):
        """生成练习题"""
        try:
            # 这里可以集成智能出题系统
            messagebox.showinfo("功能提示", "练习题生成功能正在开发中...")
            
        except Exception as e:
            print(f"❌ 生成练习题失败: {e}")
            
    def export_report(self):
        """导出学习报告"""
        try:
            from tkinter import filedialog
            
            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                title="导出学习报告",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if file_path:
                report = self.learning_analytics.export_learning_report()
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report)
                
                messagebox.showinfo("导出成功", f"学习报告已导出到：\n{file_path}")
                
        except Exception as e:
            print(f"❌ 导出报告失败: {e}")
            messagebox.showerror("导出失败", f"导出报告失败：{str(e)}")

    def show_learning_insights(self):
        """显示学习洞察"""
        try:
            insights_window = tk.Toplevel(self.window)
            insights_window.title("🔍 学习洞察")
            insights_window.geometry("600x400")
            insights_window.transient(self.window)

            # 创建洞察内容
            insights_text = tk.Text(insights_window, wrap=tk.WORD, font=("Microsoft YaHei", 11))
            insights_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 生成洞察内容
            stats = self.learning_analytics.get_learning_statistics()
            mastery_data = self.learning_analytics.get_knowledge_mastery_overview()

            insights_content = "🔍 学习洞察分析\n\n"

            if stats:
                overall = stats.get('overall', {})
                recent = stats.get('recent_week', {})
                knowledge = stats.get('knowledge', {})

                insights_content += f"📊 整体表现：\n"
                insights_content += f"• 总学习次数：{overall.get('total_sessions', 0)} 次\n"
                insights_content += f"• 总学习时长：{overall.get('total_time', 0)//3600:.1f} 小时\n"
                insights_content += f"• 整体正确率：{overall.get('overall_accuracy', 0):.1%}\n\n"

                insights_content += f"📈 最近一周：\n"
                insights_content += f"• 学习次数：{recent.get('sessions', 0)} 次\n"
                insights_content += f"• 学习时长：{recent.get('time', 0)//3600:.1f} 小时\n"
                insights_content += f"• 正确率：{recent.get('accuracy', 0):.1%}\n\n"

                insights_content += f"🧠 知识掌握：\n"
                insights_content += f"• 总知识点：{knowledge.get('total_topics', 0)} 个\n"
                insights_content += f"• 已掌握：{knowledge.get('mastered_topics', 0)} 个\n"
                insights_content += f"• 需提高：{knowledge.get('weak_topics', 0)} 个\n"
                insights_content += f"• 平均掌握度：{knowledge.get('average_mastery', 0):.1%}\n\n"

            # 学习建议
            insights_content += "💡 智能建议：\n"
            if mastery_data:
                weak_topics = [topic for topic, data in mastery_data.items()
                              if data['average_mastery'] < 0.6]
                if weak_topics:
                    insights_content += f"• 重点关注：{', '.join(weak_topics[:3])}\n"

                strong_topics = [topic for topic, data in mastery_data.items()
                               if data['average_mastery'] >= 0.8]
                if strong_topics:
                    insights_content += f"• 保持优势：{', '.join(strong_topics[:3])}\n"

            insights_text.insert(1.0, insights_content)
            insights_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f"❌ 显示学习洞察失败: {e}")
