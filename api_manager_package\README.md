# 🚀 通用API管理系统

从Kaoshi考试系统提取的完整API管理功能，支持多种AI服务提供商。

## ✨ 功能特性

- 🔌 **多API支持**: OpenAI、DeepSeek、Gemini、豆包、通义千问等
- 🔄 **自动检测**: 自动获取可用模型列表  
- 🧪 **连接测试**: 实时测试API连接状态
- 💾 **配置管理**: 支持配置保存和加载
- 🎨 **GUI界面**: 专业的可视化管理界面
- 🔧 **易于扩展**: 模块化设计，易于添加新的API

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests
```

### 2. 基础使用
```python
from api_manager_package.core.universal_api_manager import UniversalAPIManager

# 创建API管理器
api_manager = UniversalAPIManager()

# 添加自定义API
api_manager.add_custom_provider(
    name="my_api",
    display_name="我的API",
    base_url="https://api.example.com/v1",
    api_key="your_api_key",
    test_model="gpt-3.5-turbo"
)

# 测试连接
success = api_manager.test_provider_connection("my_api")
print(f"连接测试: {'成功' if success else '失败'}")
```

### 3. GUI使用
```python
python api_manager_package/quick_start.py
```
