#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
舒适版考试窗口
优化UI设计，减少手腕疲劳，提高答题效率
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from datetime import datetime
import json
from src.config.ui_config import ui_config

class ComfortableExamWindow:
    def __init__(self, parent, exam_manager, wrong_question_manager, exam_data):
        """初始化舒适版考试窗口"""
        self.parent = parent
        self.exam_manager = exam_manager
        self.wrong_question_manager = wrong_question_manager
        self.exam_data = exam_data

        # 调试信息
        print(f"🎯 初始化舒适版考试界面")
        print(f"📝 考试标题: {exam_data['title']}")
        print(f"📊 题目数量: {len(exam_data.get('questions', []))}")

        self.window = tk.Toplevel(parent)
        self.window.title(f"🎯 舒适版考试 - {exam_data['title']}")

        # 使用配置文件中的窗口大小
        try:
            window_size = ui_config.get_exam_interface_config('comfortable')['window_size']
        except:
            window_size = '1200x800'  # 默认大小
        self.window.geometry(window_size)
        self.window.transient(parent)
        self.window.grab_set()

        # 考试状态
        self.current_question = 0
        self.answers = {}
        self.start_time = datetime.now()
        self.time_left = exam_data['time_limit'] * 60
        self.is_exam_finished = False
        self.timer_thread = None
        self.marked_questions = set()  # 添加标记题目集合

        # 存储答案控件的引用
        self.answer_widgets = {}

        # 防止关闭窗口
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        print("🔧 开始设置UI...")
        self.setup_comfortable_ui()

        print("📖 开始加载题目...")
        self.load_question()

        print("⏰ 启动计时器...")
        self.start_timer()

        # 绑定键盘快捷键
        print("⌨️ 设置键盘快捷键...")
        self.setup_keyboard_shortcuts()

        print("✅ 舒适版考试界面初始化完成")
    
    def setup_comfortable_ui(self):
        """设置舒适的用户界面"""
        try:
            print("🔧 创建主框架...")
            # 主框架 - 使用更大的边距
            main_frame = ttk.Frame(self.window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

            print("🔧 创建顶部信息栏...")
            # 顶部信息栏 - 更大的字体和间距
            self.create_top_info_bar(main_frame)

            print("🔧 创建主要内容区域...")
            # 主要内容区域 - 左右分栏
            content_frame = ttk.Frame(main_frame)
            content_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))

            print("🔧 创建题目内容区域...")
            # 左侧：题目内容区域（占70%）
            self.create_question_area(content_frame)

            print("🔧 创建导航和控制区域...")
            # 右侧：导航和控制区域（占30%）
            self.create_navigation_area(content_frame)

            print("🔧 创建底部按钮区域...")
            # 底部：大按钮区域
            self.create_bottom_buttons(main_frame)

            print("✅ UI创建完成")

        except Exception as e:
            print(f"❌ UI创建失败: {e}")
            import traceback
            traceback.print_exc()
        
    def create_top_info_bar(self, parent):
        """创建顶部信息栏"""
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 考试标题 - 更大字体
        title_label = ttk.Label(info_frame, text=f"📝 {self.exam_data['title']}", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # 时间显示 - 更醒目
        self.time_label = ttk.Label(info_frame, text="", 
                                   font=("Microsoft YaHei", 14, "bold"), 
                                   foreground="red")
        self.time_label.pack(side=tk.RIGHT)
        
        # 进度条
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.progress_label = ttk.Label(progress_frame, text="", 
                                       font=("Microsoft YaHei", 12))
        self.progress_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(20, 0))
        
    def create_question_area(self, parent):
        """创建题目内容区域"""
        try:
            print("  📝 创建左侧题目框架...")
            # 左侧框架
            left_frame = ttk.Frame(parent)
            left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))

            print("  📝 创建题目标题标签...")
            # 题目标题
            self.question_title_label = ttk.Label(left_frame, text="正在加载题目...",
                                                 font=("Microsoft YaHei", 14, "bold"))
            self.question_title_label.pack(anchor=tk.W, pady=(0, 10))

            print("  📝 创建题目内容框架...")
            # 题目内容 - 更大的显示区域
            question_frame = ttk.LabelFrame(left_frame, text="题目内容", padding=15)
            question_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            print("  📝 创建题目文本区域...")
            # 题目文本 - 更大字体
            self.question_text = scrolledtext.ScrolledText(
                question_frame, wrap=tk.WORD, height=8,
                font=("Microsoft YaHei", 12), state=tk.DISABLED)
            self.question_text.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            print("  📝 创建答案区域...")
            # 答案区域 - 更大的选项按钮
            answer_label = ttk.Label(question_frame, text="请选择答案：",
                                    font=("Microsoft YaHei", 12, "bold"))
            answer_label.pack(anchor=tk.W, pady=(0, 10))

            self.answer_frame = ttk.Frame(question_frame)
            self.answer_frame.pack(fill=tk.X)

            print("  ✅ 题目内容区域创建完成")

        except Exception as e:
            print(f"  ❌ 创建题目内容区域失败: {e}")
            import traceback
            traceback.print_exc()
        
    def create_navigation_area(self, parent):
        """创建导航和控制区域"""
        # 右侧框架
        right_frame = ttk.Frame(parent)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(15, 0))
        
        # 题目导航 - 大按钮
        nav_frame = ttk.LabelFrame(right_frame, text="题目导航", padding=15)
        nav_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 导航按钮 - 更大更舒适
        button_style = {
            'width': 12,
            'font': ('Microsoft YaHei', 11)
        }
        
        self.prev_btn = ttk.Button(nav_frame, text="⬅️ 上一题", 
                                  command=self.prev_question, **button_style)
        self.prev_btn.pack(fill=tk.X, pady=(0, 8))
        
        self.next_btn = ttk.Button(nav_frame, text="下一题 ➡️", 
                                  command=self.next_question, **button_style)
        self.next_btn.pack(fill=tk.X, pady=(0, 8))
        
        # 快速跳转
        jump_frame = ttk.Frame(nav_frame)
        jump_frame.pack(fill=tk.X, pady=(8, 0))
        
        ttk.Label(jump_frame, text="跳转到第", font=("Microsoft YaHei", 10)).pack(side=tk.LEFT)
        
        self.jump_var = tk.StringVar()
        jump_entry = ttk.Entry(jump_frame, textvariable=self.jump_var, width=5)
        jump_entry.pack(side=tk.LEFT, padx=(5, 5))
        
        ttk.Label(jump_frame, text="题", font=("Microsoft YaHei", 10)).pack(side=tk.LEFT)
        
        ttk.Button(jump_frame, text="跳转", command=self.jump_to_question_by_input,
                  width=6).pack(side=tk.RIGHT)
        
        # 题目状态 - 网格显示
        status_frame = ttk.LabelFrame(right_frame, text="答题状态", padding=15)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 创建题目状态网格
        self.create_question_status_grid(status_frame)
        
        # 操作按钮
        action_frame = ttk.LabelFrame(right_frame, text="操作", padding=15)
        action_frame.pack(fill=tk.X)
        
        ttk.Button(action_frame, text="🔖 标记题目", 
                  command=self.mark_question, **button_style).pack(fill=tk.X, pady=(0, 8))
        
        ttk.Button(action_frame, text="🗑️ 清除答案", 
                  command=self.clear_answer, **button_style).pack(fill=tk.X, pady=(0, 8))
        
        ttk.Button(action_frame, text="📋 题目列表", 
                  command=self.show_question_list, **button_style).pack(fill=tk.X)
        
    def create_question_status_grid(self, parent):
        """创建题目状态网格"""
        # 滚动框架
        canvas = tk.Canvas(parent, height=200)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 创建题目状态按钮网格
        self.status_buttons = []
        total_questions = len(self.exam_data['questions'])
        
        for i in range(total_questions):
            row = i // 5  # 每行5个
            col = i % 5
            
            btn = tk.Button(scrollable_frame, text=str(i+1), width=4, height=2,
                           font=("Microsoft YaHei", 10),
                           command=lambda idx=i: self.jump_to_question(idx))
            btn.grid(row=row, column=col, padx=2, pady=2)
            
            self.status_buttons.append(btn)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_bottom_buttons(self, parent):
        """创建底部大按钮区域"""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 大按钮样式
        big_button_style = {
            'font': ('Microsoft YaHei', 14, 'bold'),
            'width': 15
        }
        
        # 左侧按钮
        left_buttons = ttk.Frame(bottom_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="💾 保存答案", 
                  command=self.save_current_answer, **big_button_style).pack(side=tk.LEFT, padx=(0, 10))
        
        # 右侧按钮
        right_buttons = ttk.Frame(bottom_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        self.submit_btn = ttk.Button(right_buttons, text="✅ 提交试卷", 
                                    command=self.submit_exam, **big_button_style)
        self.submit_btn.pack(side=tk.RIGHT)
        
    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        # 绑定键盘事件
        self.window.bind('<Left>', lambda e: self.prev_question())
        self.window.bind('<Right>', lambda e: self.next_question())
        self.window.bind('<Control-s>', lambda e: self.save_current_answer())
        self.window.bind('<Control-Return>', lambda e: self.submit_exam())
        self.window.bind('<F1>', lambda e: self.show_help())
        
        # 数字键快速选择（1-4对应A-D）
        for i in range(1, 5):
            self.window.bind(f'<Key-{i}>', lambda e, idx=i-1: self.quick_select_option(idx))
        
        # 焦点设置
        self.window.focus_set()
        
    def quick_select_option(self, option_index):
        """快速选择选项（键盘快捷键）"""
        question = self.exam_data['questions'][self.current_question]
        question_id = str(self.current_question)
        
        if question['type'] == 'single_choice' and question_id in self.answer_widgets:
            options = question.get('options', [])
            if option_index < len(options):
                option_value = chr(65 + option_index)  # A, B, C, D
                self.answer_widgets[question_id].set(option_value)
                print(f"⌨️ 快捷键选择: {option_value}")
                
    def show_help(self):
        """显示帮助信息"""
        help_text = """
🎯 考试界面快捷键：

⬅️ ➡️ 方向键：上一题/下一题
1-4 数字键：快速选择选项A-D
Ctrl+S：保存当前答案
Ctrl+Enter：提交试卷
F1：显示此帮助

🖱️ 鼠标操作：
• 点击右侧数字按钮快速跳转
• 双击题目状态按钮跳转并标记
• 大按钮设计减少鼠标移动距离

💡 提示：
• 绿色按钮：已答题
• 黄色按钮：已标记
• 红色按钮：当前题目
        """
        messagebox.showinfo("使用帮助", help_text)
        
    def load_question(self):
        """加载当前题目"""
        print(f"📖 加载题目 {self.current_question + 1}")

        if not hasattr(self, 'exam_data') or not self.exam_data:
            print("❌ 考试数据为空")
            return

        questions = self.exam_data.get('questions', [])
        if not questions:
            print("❌ 题目列表为空")
            return

        if self.current_question >= len(questions):
            print(f"❌ 题目索引超出范围: {self.current_question} >= {len(questions)}")
            return

        question = questions[self.current_question]
        print(f"📝 当前题目: {question.get('question', '无题目内容')[:50]}...")

        # 更新题目标题
        total = len(questions)
        answered = len(self.answers)

        title_text = f"第 {self.current_question + 1} 题 / 共 {total} 题"
        if hasattr(self, 'question_title_label'):
            self.question_title_label.config(text=title_text)
            print(f"✅ 更新题目标题: {title_text}")

        # 更新进度信息
        progress_text = f"已答题：{answered} / {total}"
        if hasattr(self, 'progress_label'):
            self.progress_label.config(text=progress_text)
            print(f"✅ 更新进度信息: {progress_text}")

        # 更新进度条
        if hasattr(self, 'progress_bar'):
            progress_value = (answered / total) * 100
            self.progress_bar['value'] = progress_value
            print(f"✅ 更新进度条: {progress_value:.1f}%")

        # 显示题目内容
        if hasattr(self, 'question_text'):
            self.question_text.config(state=tk.NORMAL)
            self.question_text.delete(1.0, tk.END)

            question_content = f"【{self.get_question_type_name(question['type'])}】{question.get('score', 1)}分\n\n"
            question_content += question.get('question', '无题目内容')

            self.question_text.insert(1.0, question_content)
            self.question_text.config(state=tk.DISABLED)
            print(f"✅ 更新题目内容: {len(question_content)} 字符")

        # 清空答案框架
        if hasattr(self, 'answer_frame'):
            for widget in self.answer_frame.winfo_children():
                widget.destroy()
            print("✅ 清空答案框架")

            # 创建答案控件
            self.create_comfortable_answer_widgets(question)

            # 恢复之前的答案
            self.restore_answer()

        # 更新题目状态按钮
        if hasattr(self, 'status_buttons'):
            self.update_status_buttons()
            print("✅ 更新状态按钮")

        # 更新导航按钮状态
        if hasattr(self, 'prev_btn') and hasattr(self, 'next_btn'):
            self.update_navigation_buttons()
            print("✅ 更新导航按钮")

        print(f"✅ 题目 {self.current_question + 1} 加载完成")
        
    def create_comfortable_answer_widgets(self, question):
        """创建舒适的答案控件"""
        question_id = str(self.current_question)
        
        if question['type'] == 'single_choice':
            # 单选题 - 更大的单选按钮
            self.answer_widgets[question_id] = tk.StringVar()
            
            options = question.get('options', [])
            if not options:
                options = ["选项A", "选项B", "选项C", "选项D"]
                
            for i, option in enumerate(options):
                option_frame = ttk.Frame(self.answer_frame)
                option_frame.pack(fill=tk.X, pady=5)
                
                rb = ttk.Radiobutton(option_frame, 
                                   text=f"{chr(65+i)}. {option}",
                                   variable=self.answer_widgets[question_id], 
                                   value=chr(65+i),
                                   style='Large.TRadiobutton')
                rb.pack(anchor=tk.W)
                
        elif question['type'] == 'true_false':
            # 判断题 - 更大的按钮
            self.answer_widgets[question_id] = tk.StringVar()
            
            button_frame = ttk.Frame(self.answer_frame)
            button_frame.pack(fill=tk.X, pady=10)
            
            true_btn = ttk.Radiobutton(button_frame, text="✅ 正确", 
                                     variable=self.answer_widgets[question_id], 
                                     value="对",
                                     style='Large.TRadiobutton')
            true_btn.pack(side=tk.LEFT, padx=(0, 20))
            
            false_btn = ttk.Radiobutton(button_frame, text="❌ 错误", 
                                      variable=self.answer_widgets[question_id], 
                                      value="错",
                                      style='Large.TRadiobutton')
            false_btn.pack(side=tk.LEFT)
            
        elif question['type'] == 'multiple_choice':
            # 多选题 - 更大的复选框
            self.answer_widgets[question_id] = {}
            
            options = question.get('options', [])
            if not options:
                options = ["选项A", "选项B", "选项C", "选项D"]
                
            for i, option in enumerate(options):
                var = tk.BooleanVar()
                self.answer_widgets[question_id][chr(65+i)] = var
                
                option_frame = ttk.Frame(self.answer_frame)
                option_frame.pack(fill=tk.X, pady=5)
                
                cb = ttk.Checkbutton(option_frame, 
                                   text=f"{chr(65+i)}. {option}", 
                                   variable=var,
                                   style='Large.TCheckbutton')
                cb.pack(anchor=tk.W)
                
        elif question['type'] in ['short_answer', 'case_analysis']:
            # 简答题 - 更大的文本框
            ttk.Label(self.answer_frame, text="请输入答案：", 
                     font=("Microsoft YaHei", 12)).pack(anchor=tk.W, pady=(0, 10))
            
            height = 12 if question['type'] == 'case_analysis' else 8
            self.answer_widgets[question_id] = scrolledtext.ScrolledText(
                self.answer_frame, wrap=tk.WORD, height=height,
                font=("Microsoft YaHei", 11))
            self.answer_widgets[question_id].pack(fill=tk.BOTH, expand=True)
            
    def update_status_buttons(self):
        """更新题目状态按钮"""
        for i, btn in enumerate(self.status_buttons):
            if i == self.current_question:
                # 当前题目 - 红色
                btn.config(bg='#ff6b6b', fg='white', relief='raised')
            elif str(i) in self.answers:
                # 已答题 - 绿色
                btn.config(bg='#51cf66', fg='white', relief='flat')
            else:
                # 未答题 - 默认
                btn.config(bg='#f8f9fa', fg='black', relief='flat')
                
    def update_navigation_buttons(self):
        """更新导航按钮状态"""
        # 上一题按钮
        if self.current_question <= 0:
            self.prev_btn.config(state='disabled')
        else:
            self.prev_btn.config(state='normal')
            
        # 下一题按钮
        if self.current_question >= len(self.exam_data['questions']) - 1:
            self.next_btn.config(state='disabled')
        else:
            self.next_btn.config(state='normal')
            
    def jump_to_question_by_input(self):
        """根据输入跳转到题目"""
        try:
            question_num = int(self.jump_var.get())
            if 1 <= question_num <= len(self.exam_data['questions']):
                self.jump_to_question(question_num - 1)
                self.jump_var.set('')
            else:
                messagebox.showwarning("提示", f"请输入1-{len(self.exam_data['questions'])}之间的数字")
        except ValueError:
            messagebox.showwarning("提示", "请输入有效的数字")
            
    def jump_to_question(self, question_index):
        """跳转到指定题目"""
        if 0 <= question_index < len(self.exam_data['questions']):
            self.save_current_answer()
            self.current_question = question_index
            self.load_question()
            
    # 继承原有的其他方法
    def get_question_type_name(self, question_type):
        """获取题目类型名称"""
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题', 
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        return type_names.get(question_type, '未知题型')
        
    def save_current_answer(self):
        """保存当前题目的答案"""
        question_id = str(self.current_question)
        question = self.exam_data['questions'][self.current_question]
        
        if question_id not in self.answer_widgets:
            return
            
        if question['type'] in ['single_choice', 'true_false']:
            answer = self.answer_widgets[question_id].get()
            if answer:
                self.answers[question_id] = answer
            elif question_id in self.answers:
                del self.answers[question_id]
                
        elif question['type'] == 'multiple_choice':
            selected = []
            for option, var in self.answer_widgets[question_id].items():
                if var.get():
                    selected.append(option)
            
            if selected:
                self.answers[question_id] = selected
            elif question_id in self.answers:
                del self.answers[question_id]
                
        elif question['type'] in ['short_answer', 'case_analysis']:
            answer = self.answer_widgets[question_id].get(1.0, tk.END).strip()
            if answer:
                self.answers[question_id] = answer
            elif question_id in self.answers:
                del self.answers[question_id]
                
        # 更新状态显示
        self.update_status_buttons()
        self.load_question()  # 刷新进度显示
        
    def restore_answer(self):
        """恢复之前保存的答案"""
        question_id = str(self.current_question)
        question = self.exam_data['questions'][self.current_question]
        
        if question_id not in self.answers or question_id not in self.answer_widgets:
            return
            
        answer = self.answers[question_id]
        
        if question['type'] in ['single_choice', 'true_false']:
            self.answer_widgets[question_id].set(answer)
            
        elif question['type'] == 'multiple_choice':
            if isinstance(answer, list):
                for option in answer:
                    if option in self.answer_widgets[question_id]:
                        self.answer_widgets[question_id][option].set(True)
                        
        elif question['type'] in ['short_answer', 'case_analysis']:
            self.answer_widgets[question_id].delete(1.0, tk.END)
            self.answer_widgets[question_id].insert(1.0, answer)
            
    def prev_question(self):
        """上一题"""
        if self.current_question > 0:
            self.save_current_answer()
            self.current_question -= 1
            self.load_question()
            
    def next_question(self):
        """下一题"""
        if self.current_question < len(self.exam_data['questions']) - 1:
            self.save_current_answer()
            self.current_question += 1
            self.load_question()
            
    def mark_question(self):
        """标记题目"""
        # 这里可以添加标记逻辑
        messagebox.showinfo("提示", f"已标记第{self.current_question + 1}题")
        
    def clear_answer(self):
        """清除当前题目答案"""
        question_id = str(self.current_question)
        question = self.exam_data['questions'][self.current_question]
        
        if question['type'] in ['single_choice', 'true_false']:
            if question_id in self.answer_widgets:
                self.answer_widgets[question_id].set("")
                
        elif question['type'] == 'multiple_choice':
            if question_id in self.answer_widgets:
                for var in self.answer_widgets[question_id].values():
                    var.set(False)
                    
        elif question['type'] in ['short_answer', 'case_analysis']:
            if question_id in self.answer_widgets:
                self.answer_widgets[question_id].delete(1.0, tk.END)
                
        # 从答案字典中删除
        if question_id in self.answers:
            del self.answers[question_id]
            
        self.update_status_buttons()
        self.load_question()  # 刷新进度显示
        
    def show_question_list(self):
        """显示题目列表"""
        messagebox.showinfo("题目列表", "题目列表功能开发中...")
        
    def start_timer(self):
        """启动计时器"""
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()
        
    def timer_worker(self):
        """计时器工作线程"""
        while self.time_left > 0 and not self.is_exam_finished:
            minutes = self.time_left // 60
            seconds = self.time_left % 60
            time_text = f"⏰ 剩余时间：{minutes:02d}:{seconds:02d}"
            
            # 在主线程中更新UI
            self.window.after(0, lambda: self.time_label.config(text=time_text))
            
            self.time_left -= 1
            threading.Event().wait(1)
            
        if not self.is_exam_finished:
            self.window.after(0, self.time_up)
            
    def time_up(self):
        """时间到"""
        messagebox.showwarning("时间到", "考试时间已到，将自动提交试卷！")
        self.submit_exam()
        
    def submit_exam(self):
        """提交试卷"""
        self.save_current_answer()
        
        answered = len(self.answers)
        total = len(self.exam_data['questions'])
        
        if answered < total:
            if not messagebox.askyesno("确认提交", 
                                     f"您还有{total - answered}道题目未作答，确定要提交吗？"):
                return
                
        self.is_exam_finished = True
        
        # 计算分数
        score, total_score = self.exam_manager.calculate_score(
            self.exam_data['questions'], self.answers)
        
        end_time = datetime.now()
        
        # 保存考试记录
        try:
            record_id = self.exam_manager.save_exam_record(
                exam_id=self.exam_data['id'],
                answers=self.answers,
                score=score,
                total_score=total_score,
                start_time=self.start_time,
                end_time=end_time
            )
            
            # 自动添加错题到错题本
            try:
                record = self.exam_manager.get_exam_record_by_id(record_id)
                if record:
                    added_count = self.wrong_question_manager.batch_add_wrong_questions_from_exam(record)
                    print(f"✅ 自动添加了 {added_count} 道错题")
            except Exception as e:
                print(f"❌ 自动添加错题失败: {e}")
                
            # 显示结果
            self.show_result(score, total_score, record_id)
            
        except Exception as e:
            messagebox.showerror("错误", f"保存考试记录失败：{str(e)}")
            
    def show_result(self, score, total_score, record_id):
        """显示考试结果"""
        duration = datetime.now() - self.start_time
        percentage = (score / total_score) * 100 if total_score > 0 else 0
        
        result_text = f"""
🎉 考试完成！

得分：{score:.1f} / {total_score:.1f} ({percentage:.1f}%)
用时：{str(duration).split('.')[0]}
记录ID：{record_id}

是否查看详细解析？
"""
        
        if messagebox.askyesno("考试结果", result_text):
            # 这里可以打开详细结果窗口
            messagebox.showinfo("提示", "详细解析功能开发中...")
            
        self.window.destroy()
        
    def on_closing(self):
        """窗口关闭事件"""
        if not self.is_exam_finished:
            if messagebox.askyesno("确认退出", "考试尚未完成，确定要退出吗？\n退出后将无法继续考试。"):
                self.is_exam_finished = True
                self.window.destroy()
        else:
            self.window.destroy()
