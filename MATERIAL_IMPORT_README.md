# 材料导入系统

一个功能完整的学习材料导入和管理系统，可以轻松集成到其他项目中。

## 功能特性

### 核心功能
- ✅ **文本文件导入** - 支持 UTF-8、GBK 等多种编码
- ✅ **PDF文件导入** - 自动提取PDF中的文本内容
- ✅ **手动输入** - 支持直接输入文本内容
- ✅ **材料管理** - 增删改查、搜索、统计
- ✅ **批量导入** - 一次性导入多个文件
- ✅ **数据持久化** - SQLite数据库存储

### 界面功能
- 🎨 **图形界面** - 基于tkinter的友好界面
- 🔍 **实时搜索** - 支持标题和内容搜索
- 📊 **统计信息** - 材料数量和类型统计
- 📝 **内容预览** - 选中材料时显示内容预览
- ✏️ **在线编辑** - 支持材料内容的在线编辑

### 集成特性
- 🔌 **模块化设计** - 核心功能与UI分离
- 📚 **API接口** - 提供完整的编程接口
- ⚙️ **配置系统** - 灵活的配置选项
- 🛠️ **易于集成** - 可轻松集成到现有系统

## 快速开始

### 1. 安装依赖

```bash
pip install PyPDF2
```

### 2. 初始化数据库

```python
from database_setup import DatabaseSetup

# 创建数据库
db_setup = DatabaseSetup("data/materials.db")
conn = db_setup.create_database()

# 可选：插入示例数据
db_setup.insert_sample_data(conn)
```

### 3. 基本使用

```python
from material_import_core import MaterialImporter, MaterialManager
import sqlite3

# 连接数据库
conn = sqlite3.connect("data/materials.db")

# 创建导入器
importer = MaterialImporter(conn)

# 导入文本文件
result = importer.import_text_file("example.txt")
if result['success']:
    print(f"导入成功: {result['title']}")

# 导入PDF文件
result = importer.import_pdf_file("document.pdf")
if result['success']:
    print(f"PDF导入成功，页数: {result['page_count']}")

# 材料管理
manager = MaterialManager(conn)
materials = manager.get_all_materials()
for material in materials:
    print(f"ID: {material['id']}, 标题: {material['title']}")
```

### 4. GUI界面

```python
import tkinter as tk
from material_import_ui import MaterialListWidget

root = tk.Tk()
root.title("材料管理")

# 创建材料列表组件
material_list = MaterialListWidget(root, conn)

root.mainloop()
```

## 文件结构

```
material_import_system/
├── material_import_core.py      # 核心功能模块
├── material_import_ui.py        # 用户界面组件
├── database_setup.py           # 数据库初始化
├── database_schema.sql         # 数据库结构
├── config.py                   # 配置文件
├── integration_example.py      # 集成示例
├── material_import_requirements.txt  # 依赖列表
└── MATERIAL_IMPORT_README.md   # 说明文档
```

## API文档

### MaterialImporter 类

#### 方法

- `import_text_file(file_path)` - 导入文本文件
- `import_pdf_file(file_path)` - 导入PDF文件
- `import_from_text(title, content)` - 从文本内容导入

#### 返回格式

```python
{
    'success': bool,           # 是否成功
    'material_id': int,        # 材料ID（成功时）
    'title': str,              # 材料标题
    'content': str,            # 材料内容
    'file_type': str,          # 文件类型
    'file_size': int,          # 内容大小
    'page_count': int,         # 页数（PDF文件）
    'created_at': datetime,    # 创建时间
    'error': str               # 错误信息（失败时）
}
```

### MaterialManager 类

#### 方法

- `get_all_materials()` - 获取所有材料
- `get_material_by_id(material_id)` - 根据ID获取材料
- `search_materials(keyword)` - 搜索材料
- `delete_material(material_id)` - 删除材料
- `update_material(material_id, title, content)` - 更新材料
- `get_material_stats()` - 获取统计信息

## 配置选项

### 数据库配置

```python
from config import config

# 修改数据库路径
config.DATABASE['path'] = 'custom/path/materials.db'

# 使用其他数据库类型
config.DATABASE['type'] = 'mysql'
config.DATABASE['host'] = 'localhost'
config.DATABASE['username'] = 'user'
config.DATABASE['password'] = 'password'
```

### 文件处理配置

```python
# 修改最大文件大小 (50MB)
config.FILE_PROCESSING['max_file_size'] = 50 * 1024 * 1024

# 添加支持的文件类型
config.FILE_PROCESSING['supported_file_types']['doc'] = ['.doc', '.docx']
```

## 集成到现有系统

### 1. 作为独立模块

```python
# 在你的项目中导入
from material_import_core import MaterialManager
from your_database import get_connection

# 使用现有数据库连接
conn = get_connection()
manager = MaterialManager(conn)

# 调用功能
materials = manager.get_all_materials()
```

### 2. 集成UI组件

```python
import tkinter as tk
from material_import_ui import MaterialImportDialog

# 在现有窗口中添加导入功能
def show_import():
    MaterialImportDialog(parent_window, db_connection, refresh_callback)

# 添加到菜单或按钮
menu.add_command(label="导入材料", command=show_import)
```

### 3. 自定义扩展

```python
from material_import_core import MaterialImporter

class CustomImporter(MaterialImporter):
    def import_word_file(self, file_path):
        # 自定义Word文件导入逻辑
        pass
    
    def import_excel_file(self, file_path):
        # 自定义Excel文件导入逻辑
        pass
```

## 运行示例

```bash
# 运行完整示例
python integration_example.py

# 运行数据库设置
python database_setup.py

# 运行配置测试
python config.py
```

## 系统要求

- Python 3.7+
- tkinter (通常随Python安装)
- PyPDF2 >= 3.0.0

## 可选依赖

- `python-docx` - Word文档支持
- `openpyxl` - Excel文档支持
- `jieba` - 中文文本处理
- `ttkthemes` - 界面主题支持

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本
- 支持文本和PDF文件导入
- 完整的材料管理功能
- 图形用户界面
- API接口支持
