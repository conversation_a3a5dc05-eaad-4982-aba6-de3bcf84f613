#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用AI客户端
基于通用API管理器的AI客户端
"""

import json
from typing import List, Dict, Any
from src.api.base_ai_client import BaseAIClient
from src.api.universal_api_manager import UniversalAPIManager

class UniversalAIClient(BaseAIClient):
    """通用AI客户端"""
    
    def __init__(self, provider_name: str, model: str):
        self.provider_name = provider_name
        self.model = model
        self.api_manager = UniversalAPIManager()
        
        # 加载配置
        try:
            self.api_manager.load_config("config/api_providers.json")
        except:
            pass
        
        # 验证提供商和模型
        if provider_name not in self.api_manager.providers:
            raise ValueError(f"未找到API提供商: {provider_name}")
        
        self.provider = self.api_manager.providers[provider_name]
        
        if not self.provider.active:
            raise ValueError(f"API提供商 {provider_name} 未启用")
    
    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        return self.provider.models if self.provider.models else [self.model]
    
    def test_connection(self) -> bool:
        """测试API连接"""
        return self.api_manager.test_provider_connection(self.provider_name)
    
    def generate_questions(self, material: str, question_types: List[str], 
                          num_questions: int = 20) -> List[Dict[str, Any]]:
        """生成题目"""
        try:
            prompt = self.create_prompt_for_questions(material, question_types, num_questions)
            
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的教育工作者，擅长根据学习材料生成高质量的考试题目。请严格按照要求的JSON格式返回题目。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            # 使用通用API管理器发送请求
            response = self.api_manager.chat_completion(
                provider_name=self.provider_name,
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=4000
            )
            
            # 解析响应
            if 'choices' in response and len(response['choices']) > 0:
                content = response['choices'][0]['message']['content']
                questions = self.parse_questions_response(content)
                return self.validate_questions(questions)
            else:
                raise Exception("API响应格式异常")
                
        except Exception as e:
            raise Exception(f"生成题目失败: {str(e)}")
    
    def evaluate_answer(self, question: str, answer: str, correct_answer: str) -> Dict[str, Any]:
        """评估答案"""
        try:
            prompt = f"""
请评估以下答案的正确性：

题目：{question}
标准答案：{correct_answer}
学生答案：{answer}

请按以下JSON格式返回评估结果：
{{
    "score": 分数(0-10),
    "max_score": 10,
    "is_correct": true/false,
    "feedback": "详细反馈",
    "suggestions": "改进建议"
}}
"""
            
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的教师，擅长评估学生答案并给出建设性反馈。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            response = self.api_manager.chat_completion(
                provider_name=self.provider_name,
                model=self.model,
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )
            
            if 'choices' in response and len(response['choices']) > 0:
                content = response['choices'][0]['message']['content']
                
                # 尝试解析JSON
                try:
                    evaluation = json.loads(content)
                    return evaluation
                except json.JSONDecodeError:
                    # 如果不是JSON格式，返回基本评估
                    return {
                        "score": 5,
                        "max_score": 10,
                        "is_correct": False,
                        "feedback": content,
                        "suggestions": "请参考标准答案"
                    }
            else:
                raise Exception("API响应格式异常")
                
        except Exception as e:
            return {
                "score": 0,
                "max_score": 10,
                "is_correct": False,
                "feedback": f"评估失败: {str(e)}",
                "suggestions": "请检查网络连接或API配置"
            }
    
    def create_prompt_for_questions(self, material: str, question_types: List[str], num_questions: int) -> str:
        """创建题目生成提示"""
        
        type_descriptions = {
            'single_choice': '单选题：提供4个选项，只有一个正确答案',
            'multiple_choice': '多选题：提供4个选项，可能有多个正确答案',
            'true_false': '判断题：判断陈述是否正确',
            'short_answer': '简答题：需要简短的文字回答',
            'case_analysis': '案例分析题：基于材料进行分析'
        }
        
        selected_types = [type_descriptions.get(t, t) for t in question_types]
        
        prompt = f"""
基于以下学习材料，生成 {num_questions} 道考试题目。

学习材料：
{material}

题目类型要求：
{chr(10).join(f"- {t}" for t in selected_types)}

请严格按照以下JSON格式返回：
[
    {{
        "type": "题目类型",
        "question": "题目内容",
        "options": ["选项A", "选项B", "选项C", "选项D"],  // 仅选择题需要
        "correct_answer": "正确答案",
        "explanation": "答案解析",
        "score": 分值
    }}
]

要求：
1. 题目要基于提供的材料内容
2. 难度适中，覆盖材料的重点知识
3. 答案解析要详细清楚
4. 单选题答案用A/B/C/D表示
5. 多选题答案用["A", "B"]格式表示
6. 判断题答案用"对"或"错"表示
7. 分值根据题目难度设置（1-5分）
8. 必须返回有效的JSON格式
"""
        
        return prompt
    
    def parse_questions_response(self, response: str) -> List[Dict[str, Any]]:
        """解析题目响应"""
        try:
            # 尝试直接解析JSON
            questions = json.loads(response)
            return questions
        except json.JSONDecodeError:
            # 如果不是纯JSON，尝试提取JSON部分
            import re
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                try:
                    questions = json.loads(json_match.group())
                    return questions
                except json.JSONDecodeError:
                    pass
            
            # 如果都失败了，返回空列表
            print(f"无法解析题目响应: {response[:200]}...")
            return []
    
    def validate_questions(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证题目格式"""
        validated_questions = []
        
        for i, question in enumerate(questions):
            try:
                # 检查必要字段
                if not isinstance(question, dict):
                    continue
                
                if 'question' not in question or 'correct_answer' not in question:
                    continue
                
                # 设置默认值
                validated_question = {
                    'type': question.get('type', 'single_choice'),
                    'question': question['question'],
                    'correct_answer': question['correct_answer'],
                    'explanation': question.get('explanation', '暂无解析'),
                    'score': question.get('score', 2)
                }
                
                # 处理选择题选项
                if validated_question['type'] in ['single_choice', 'multiple_choice']:
                    validated_question['options'] = question.get('options', ['选项A', '选项B', '选项C', '选项D'])
                
                validated_questions.append(validated_question)
                
            except Exception as e:
                print(f"验证题目 {i+1} 时出错: {e}")
                continue
        
        return validated_questions

# 工厂函数
def create_universal_client(provider_name: str, model: str) -> UniversalAIClient:
    """创建通用AI客户端"""
    return UniversalAIClient(provider_name, model)

def get_available_providers() -> List[str]:
    """获取可用的API提供商"""
    api_manager = UniversalAPIManager()
    try:
        api_manager.load_config("config/api_providers.json")
    except:
        pass
    
    return [name for name, provider in api_manager.providers.items() if provider.active and provider.api_key]

def get_provider_models(provider_name: str) -> List[str]:
    """获取指定提供商的模型列表"""
    api_manager = UniversalAPIManager()
    try:
        api_manager.load_config("config/api_providers.json")
    except:
        pass
    
    return api_manager.get_provider_models(provider_name)
