#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段优化功能测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_optimizations():
    """测试第一阶段优化功能"""
    print("🔧 第一阶段优化功能测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 5
    
    # 测试1: 启动画面
    print("1️⃣ 测试启动画面...")
    try:
        from src.ui.splash_screen import SplashScreen
        print("   ✅ 启动画面模块导入成功")
        success_count += 1
    except Exception as e:
        print(f"   ❌ 启动画面模块导入失败: {e}")
    
    # 测试2: 主题管理器
    print("2️⃣ 测试主题管理器...")
    try:
        from src.ui.theme_manager import ThemeManager
        theme_manager = ThemeManager()
        print(f"   ✅ 主题管理器创建成功，当前主题: {theme_manager.current_theme}")
        
        # 测试主题切换
        theme_manager.set_theme("dark")
        print(f"   ✅ 主题切换成功: {theme_manager.current_theme}")
        
        success_count += 1
    except Exception as e:
        print(f"   ❌ 主题管理器测试失败: {e}")
    
    # 测试3: 快捷键管理器
    print("3️⃣ 测试快捷键管理器...")
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        from src.ui.shortcut_manager import ShortcutManager
        shortcut_manager = ShortcutManager(root)
        
        # 测试快捷键注册
        test_called = False
        def test_callback(event=None):
            nonlocal test_called
            test_called = True
            
        shortcut_manager.register_action("test", test_callback)
        print("   ✅ 快捷键管理器创建成功")
        
        root.destroy()
        success_count += 1
    except Exception as e:
        print(f"   ❌ 快捷键管理器测试失败: {e}")
    
    # 测试4: 备份管理器
    print("4️⃣ 测试备份管理器...")
    try:
        from src.utils.backup_manager import BackupManager
        
        # 创建测试数据库文件
        test_db = "test_backup.db"
        with open(test_db, 'w') as f:
            f.write("test content")
            
        backup_manager = BackupManager(test_db, "test_backups")
        print("   ✅ 备份管理器创建成功")
        
        # 清理测试文件
        if os.path.exists(test_db):
            os.remove(test_db)
        if os.path.exists("test_backups"):
            import shutil
            shutil.rmtree("test_backups")
            
        success_count += 1
    except Exception as e:
        print(f"   ❌ 备份管理器测试失败: {e}")
    
    # 测试5: 性能优化器
    print("5️⃣ 测试性能优化器...")
    try:
        from src.utils.performance_optimizer import PerformanceOptimizer
        optimizer = PerformanceOptimizer()
        print("   ✅ 性能优化器创建成功")
        
        # 测试基本功能
        optimizer.optimize_startup()
        stats = optimizer.get_performance_stats()
        print(f"   📊 性能统计: 内存使用 {stats.get('memory_usage', 0):.1f}MB")
        
        success_count += 1
    except Exception as e:
        print(f"   ❌ 性能优化器测试失败: {e}")
    
    # 测试结果
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有优化功能测试通过！")
        print("✅ 第一阶段优化已成功集成")
    elif success_count >= 3:
        print("⚠️ 大部分功能正常，可以继续使用")
    else:
        print("❌ 多个功能存在问题，需要检查")
    
    print("\n🚀 现在可以启动优化后的主程序：")
    print("   python main.py")
    print("=" * 40)

if __name__ == "__main__":
    test_optimizations()
