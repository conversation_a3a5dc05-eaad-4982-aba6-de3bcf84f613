#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试判断题修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.managers.exam_manager import <PERSON>amManager
from src.managers.wrong_question_manager import WrongQuestionManager

def test_true_false_questions():
    """测试判断题数据"""
    print("🔍 检查判断题数据...")
    
    exam_manager = ExamManager()
    
    # 获取所有考试
    exams = exam_manager.get_all_exams()
    print(f"📊 找到 {len(exams)} 个考试")
    
    # 检查每个考试中的判断题
    for exam in exams:
        exam_data = exam_manager.get_exam_by_id(exam[0])
        if exam_data and exam_data.get('questions'):
            true_false_count = 0
            for question in exam_data['questions']:
                if question.get('type') == 'true_false':
                    true_false_count += 1
                    print(f"✅ 找到判断题: {question.get('question', '')[:50]}...")
                    print(f"   答案: {question.get('answer', '')}")
                    
            if true_false_count > 0:
                print(f"📝 考试 '{exam_data['title']}' 包含 {true_false_count} 道判断题")
                return exam_data  # 返回包含判断题的考试
    
    print("❌ 没有找到判断题")
    return None

def create_test_exam_with_true_false():
    """创建包含判断题的测试考试"""
    print("🔧 创建测试判断题考试...")
    
    exam_manager = ExamManager()
    
    # 创建测试考试
    exam_id = exam_manager.create_exam(
        title="判断题测试考试",
        description="用于测试判断题功能的考试",
        time_limit=10
    )
    
    # 添加判断题
    questions = [
        {
            "question": "Python是一种编程语言。",
            "type": "true_false",
            "answer": "T",
            "explanation": "Python确实是一种高级编程语言。",
            "score": 5
        },
        {
            "question": "HTML是一种编程语言。",
            "type": "true_false", 
            "answer": "F",
            "explanation": "HTML是标记语言，不是编程语言。",
            "score": 5
        },
        {
            "question": "数据库可以存储大量数据。",
            "type": "true_false",
            "answer": "T", 
            "explanation": "数据库的主要功能就是存储和管理大量数据。",
            "score": 5
        }
    ]
    
    for question_data in questions:
        exam_manager.add_question_to_exam(
            exam_id=exam_id,
            question=question_data["question"],
            question_type=question_data["type"],
            options=[],  # 判断题不需要选项
            answer=question_data["answer"],
            explanation=question_data["explanation"],
            score=question_data["score"]
        )
    
    print(f"✅ 创建测试考试成功，ID: {exam_id}")
    return exam_manager.get_exam_by_id(exam_id)

if __name__ == "__main__":
    print("🧪 开始测试判断题修复...")
    
    # 首先检查现有的判断题
    exam_data = test_true_false_questions()
    
    # 如果没有找到，创建测试考试
    if not exam_data:
        exam_data = create_test_exam_with_true_false()
    
    if exam_data:
        print(f"\n✅ 测试数据准备完成！")
        print(f"📝 考试名称: {exam_data['title']}")
        print(f"📊 题目数量: {len(exam_data['questions'])}")
        
        # 显示判断题详情
        for i, question in enumerate(exam_data['questions']):
            if question.get('type') == 'true_false':
                print(f"\n第{i+1}题 (判断题):")
                print(f"  题目: {question.get('question', '')}")
                print(f"  答案: {'正确' if question.get('answer') == 'T' else '错误'}")
                print(f"  解释: {question.get('explanation', '')}")
        
        print(f"\n🚀 现在可以启动程序测试判断题功能了！")
        print(f"   python main.py")
        print(f"   选择考试: {exam_data['title']}")
        print(f"   选择美观界面，查看判断题是否正常显示")
    else:
        print("❌ 测试数据准备失败")
