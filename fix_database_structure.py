#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def fix_database_structure():
    """修复数据库结构"""
    print("修复数据库结构...")
    
    db_path = "data/exam_system.db"
    if not os.path.exists(db_path):
        print("数据库文件不存在")
        return False
        
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(wrong_questions)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"当前字段: {column_names}")
        
        # 添加缺失的字段
        if 'exam_id' not in column_names:
            cursor.execute("ALTER TABLE wrong_questions ADD COLUMN exam_id INTEGER")
            print("添加 exam_id 字段")
            
        if 'exam_title' not in column_names:
            cursor.execute("ALTER TABLE wrong_questions ADD COLUMN exam_title TEXT")
            print("添加 exam_title 字段")
            
        if 'exam_record_id' not in column_names:
            cursor.execute("ALTER TABLE wrong_questions ADD COLUMN exam_record_id INTEGER")
            print("添加 exam_record_id 字段")
        
        conn.commit()
        print("数据库结构修复完成")
        
        # 验证修复结果
        cursor.execute("PRAGMA table_info(wrong_questions)")
        new_columns = cursor.fetchall()
        new_column_names = [col[1] for col in new_columns]
        print(f"修复后字段: {new_column_names}")
        
        return True
        
    except Exception as e:
        print(f"修复失败: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    fix_database_structure()
