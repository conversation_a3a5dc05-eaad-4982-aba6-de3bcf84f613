#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试窗口
提供在线考试功能，包括计时、答题、自动评分等
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime, timedelta
import json

class ExamWindow:
    def __init__(self, parent, exam_manager, wrong_question_manager, exam_data):
        """初始化考试窗口"""
        self.parent = parent
        self.exam_manager = exam_manager
        self.wrong_question_manager = wrong_question_manager
        self.exam_data = exam_data
        
        self.window = tk.Toplevel(parent)
        self.window.title(f"考试 - {exam_data['title']}")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        # 考试状态
        self.current_question = 0
        self.answers = {}
        self.start_time = datetime.now()
        self.time_left = exam_data['time_limit'] * 60  # 转换为秒
        self.is_exam_finished = False
        self.timer_thread = None
        
        # 防止关闭窗口
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.setup_ui()
        self.load_question()
        self.start_timer()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部信息栏
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 考试信息
        ttk.Label(info_frame, text=f"考试：{self.exam_data['title']}", 
                 font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        
        # 时间显示
        self.time_label = ttk.Label(info_frame, text="", font=("Arial", 12, "bold"), 
                                   foreground="red")
        self.time_label.pack(side=tk.RIGHT)
        
        # 进度信息
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.progress_label = ttk.Label(progress_frame, text="")
        self.progress_label.pack(side=tk.LEFT)
        
        # 题目导航
        nav_frame = ttk.Frame(progress_frame)
        nav_frame.pack(side=tk.RIGHT)
        
        ttk.Button(nav_frame, text="上一题", command=self.prev_question).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(nav_frame, text="下一题", command=self.next_question).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(nav_frame, text="题目列表", command=self.show_question_list).pack(side=tk.LEFT)
        
        # 题目内容框架
        question_frame = ttk.LabelFrame(main_frame, text="题目内容", padding=10)
        question_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 题目文本
        self.question_text = scrolledtext.ScrolledText(question_frame, wrap=tk.WORD, 
                                                      height=8, state=tk.DISABLED)
        self.question_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 答案框架
        self.answer_frame = ttk.Frame(question_frame)
        self.answer_frame.pack(fill=tk.X)
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="标记题目", command=self.mark_question).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清除答案", command=self.clear_answer).pack(side=tk.LEFT, padx=(0, 5))
        
        self.submit_btn = ttk.Button(button_frame, text="提交试卷", command=self.submit_exam)
        self.submit_btn.pack(side=tk.RIGHT)
        
        # 存储答案控件的引用
        self.answer_widgets = {}
        self.marked_questions = set()
    
    def load_question(self):
        """加载当前题目"""
        if self.current_question >= len(self.exam_data['questions']):
            return
        
        question = self.exam_data['questions'][self.current_question]
        
        # 更新进度信息
        total = len(self.exam_data['questions'])
        answered = len(self.answers)
        marked = len(self.marked_questions)
        
        progress_text = f"题目 {self.current_question + 1}/{total} | 已答 {answered} | 已标记 {marked}"
        self.progress_label.config(text=progress_text)
        
        # 显示题目内容
        self.question_text.config(state=tk.NORMAL)
        self.question_text.delete(1.0, tk.END)
        
        question_content = f"第{self.current_question + 1}题 ({self.get_question_type_name(question['type'])}) [{question.get('score', 1)}分]\n\n"
        question_content += question['question']
        
        self.question_text.insert(1.0, question_content)
        self.question_text.config(state=tk.DISABLED)
        
        # 清空答案框架
        for widget in self.answer_frame.winfo_children():
            widget.destroy()
        
        # 根据题目类型创建答案控件
        self.create_answer_widgets(question)
        
        # 恢复之前的答案
        self.restore_answer()
    
    def get_question_type_name(self, question_type):
        """获取题目类型名称"""
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        return type_names.get(question_type, question_type)
    
    def create_answer_widgets(self, question):
        """根据题目类型创建答案控件"""
        question_id = str(self.current_question)
        
        if question['type'] == 'single_choice':
            # 单选题
            self.answer_widgets[question_id] = tk.StringVar()

            # 获取选项，如果没有则创建默认选项
            options = question.get('options', [])
            if not options:
                print(f"⚠️ 单选题缺少选项，添加默认选项")
                options = ["选项A", "选项B", "选项C", "选项D"]
                question['options'] = options  # 更新题目数据

            for i, option in enumerate(options):
                rb = ttk.Radiobutton(self.answer_frame, text=f"{chr(65+i)}. {option}",
                                   variable=self.answer_widgets[question_id], value=chr(65+i))
                rb.pack(anchor=tk.W, pady=2)
        
        elif question['type'] == 'multiple_choice':
            # 多选题
            self.answer_widgets[question_id] = {}

            # 获取选项，如果没有则创建默认选项
            options = question.get('options', [])
            if not options:
                print(f"⚠️ 多选题缺少选项，添加默认选项")
                options = ["选项A", "选项B", "选项C", "选项D"]
                question['options'] = options  # 更新题目数据

            for i, option in enumerate(options):
                var = tk.BooleanVar()
                self.answer_widgets[question_id][chr(65+i)] = var

                cb = ttk.Checkbutton(self.answer_frame, text=f"{chr(65+i)}. {option}", variable=var)
                cb.pack(anchor=tk.W, pady=2)
        
        elif question['type'] == 'true_false':
            # 判断题
            self.answer_widgets[question_id] = tk.StringVar()
            
            ttk.Radiobutton(self.answer_frame, text="对", 
                          variable=self.answer_widgets[question_id], value="对").pack(anchor=tk.W, pady=2)
            ttk.Radiobutton(self.answer_frame, text="错", 
                          variable=self.answer_widgets[question_id], value="错").pack(anchor=tk.W, pady=2)
        
        elif question['type'] in ['short_answer', 'case_analysis']:
            # 简答题和案例分析题
            ttk.Label(self.answer_frame, text="请输入答案：").pack(anchor=tk.W, pady=(0, 5))
            
            height = 10 if question['type'] == 'case_analysis' else 5
            self.answer_widgets[question_id] = scrolledtext.ScrolledText(
                self.answer_frame, wrap=tk.WORD, height=height)
            self.answer_widgets[question_id].pack(fill=tk.BOTH, expand=True)
    
    def restore_answer(self):
        """恢复之前保存的答案"""
        question_id = str(self.current_question)
        
        if question_id not in self.answers:
            return
        
        answer = self.answers[question_id]
        question = self.exam_data['questions'][self.current_question]
        
        if question['type'] in ['single_choice', 'true_false']:
            if question_id in self.answer_widgets:
                self.answer_widgets[question_id].set(answer)
        
        elif question['type'] == 'multiple_choice':
            if question_id in self.answer_widgets and isinstance(answer, list):
                for option, var in self.answer_widgets[question_id].items():
                    var.set(option in answer)
        
        elif question['type'] in ['short_answer', 'case_analysis']:
            if question_id in self.answer_widgets:
                self.answer_widgets[question_id].delete(1.0, tk.END)
                self.answer_widgets[question_id].insert(1.0, answer)
    
    def save_current_answer(self):
        """保存当前题目的答案"""
        question_id = str(self.current_question)
        question = self.exam_data['questions'][self.current_question]
        
        if question_id not in self.answer_widgets:
            return
        
        if question['type'] in ['single_choice', 'true_false']:
            answer = self.answer_widgets[question_id].get()
            if answer:
                self.answers[question_id] = answer
            elif question_id in self.answers:
                del self.answers[question_id]
        
        elif question['type'] == 'multiple_choice':
            selected = []
            for option, var in self.answer_widgets[question_id].items():
                if var.get():
                    selected.append(option)
            
            if selected:
                self.answers[question_id] = selected
            elif question_id in self.answers:
                del self.answers[question_id]
        
        elif question['type'] in ['short_answer', 'case_analysis']:
            answer = self.answer_widgets[question_id].get(1.0, tk.END).strip()
            if answer:
                self.answers[question_id] = answer
            elif question_id in self.answers:
                del self.answers[question_id]
    
    def prev_question(self):
        """上一题"""
        if self.current_question > 0:
            self.save_current_answer()
            self.current_question -= 1
            self.load_question()
    
    def next_question(self):
        """下一题"""
        if self.current_question < len(self.exam_data['questions']) - 1:
            self.save_current_answer()
            self.current_question += 1
            self.load_question()
    
    def mark_question(self):
        """标记/取消标记题目"""
        if self.current_question in self.marked_questions:
            self.marked_questions.remove(self.current_question)
            messagebox.showinfo("提示", "已取消标记")
        else:
            self.marked_questions.add(self.current_question)
            messagebox.showinfo("提示", "已标记题目")
        
        # 更新进度显示
        self.load_question()
    
    def clear_answer(self):
        """清除当前题目答案"""
        question_id = str(self.current_question)
        question = self.exam_data['questions'][self.current_question]
        
        if question['type'] in ['single_choice', 'true_false']:
            if question_id in self.answer_widgets:
                self.answer_widgets[question_id].set("")
        
        elif question['type'] == 'multiple_choice':
            if question_id in self.answer_widgets:
                for var in self.answer_widgets[question_id].values():
                    var.set(False)
        
        elif question['type'] in ['short_answer', 'case_analysis']:
            if question_id in self.answer_widgets:
                self.answer_widgets[question_id].delete(1.0, tk.END)
        
        # 从答案字典中删除
        if question_id in self.answers:
            del self.answers[question_id]
    
    def show_question_list(self):
        """显示题目列表"""
        QuestionListWindow(self.window, self.exam_data['questions'], 
                          self.answers, self.marked_questions, self.jump_to_question)
    
    def jump_to_question(self, question_index):
        """跳转到指定题目"""
        if 0 <= question_index < len(self.exam_data['questions']):
            self.save_current_answer()
            self.current_question = question_index
            self.load_question()
    
    def start_timer(self):
        """启动计时器"""
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()
    
    def timer_worker(self):
        """计时器工作线程"""
        while self.time_left > 0 and not self.is_exam_finished:
            # 更新时间显示
            minutes = self.time_left // 60
            seconds = self.time_left % 60
            time_text = f"剩余时间: {minutes:02d}:{seconds:02d}"
            
            self.window.after(0, lambda: self.time_label.config(text=time_text))
            
            time.sleep(1)
            self.time_left -= 1
        
        # 时间到，自动提交
        if not self.is_exam_finished:
            self.window.after(0, self.time_up)
    
    def time_up(self):
        """时间到"""
        messagebox.showwarning("时间到", "考试时间已到，系统将自动提交试卷！")
        self.submit_exam()
    
    def submit_exam(self):
        """提交试卷"""
        # 保存当前答案
        self.save_current_answer()
        
        # 确认提交
        answered = len(self.answers)
        total = len(self.exam_data['questions'])
        
        if answered < total:
            if not messagebox.askyesno("确认提交", 
                                     f"您还有{total - answered}道题目未作答，确定要提交吗？"):
                return
        
        self.is_exam_finished = True
        
        # 计算分数
        score, total_score = self.exam_manager.calculate_score(
            self.exam_data['questions'], self.answers)
        
        end_time = datetime.now()
        
        # 保存考试记录
        try:
            record_id = self.exam_manager.save_exam_record(
                exam_id=self.exam_data['id'],
                answers=self.answers,
                score=score,
                total_score=total_score,
                start_time=self.start_time,
                end_time=end_time
            )
            
            # 自动添加错题到错题本
            try:
                print("🔍 考试提交后自动添加错题...")
                record = self.exam_manager.get_exam_record_by_id(record_id)
                if record:
                    added_count = self.wrong_question_manager.batch_add_wrong_questions_from_exam(record)
                    print(f"✅ 自动添加了 {added_count} 道错题")
                else:
                    print("❌ 无法获取考试记录，跳过自动添加错题")
            except Exception as e:
                print(f"❌ 自动添加错题失败: {e}")

            # 显示结果
            self.show_result(score, total_score, record_id)
            
        except Exception as e:
            messagebox.showerror("错误", f"保存考试记录失败：{str(e)}")
    
    def show_result(self, score, total_score, record_id):
        """显示考试结果"""
        percentage = (score / total_score * 100) if total_score > 0 else 0
        duration = datetime.now() - self.start_time
        
        result_text = f"""
考试完成！

得分：{score:.1f} / {total_score:.1f} ({percentage:.1f}%)
用时：{str(duration).split('.')[0]}
记录ID：{record_id}

是否查看详细解析？
"""
        
        if messagebox.askyesno("考试结果", result_text):
            self.show_detailed_result(record_id)
        else:
            self.window.destroy()
    
    def show_detailed_result(self, record_id):
        """显示详细结果"""
        ExamResultWindow(self.window, self.exam_manager, self.wrong_question_manager, record_id)
        self.window.destroy()
    
    def on_closing(self):
        """窗口关闭事件"""
        if not self.is_exam_finished:
            if messagebox.askyesno("确认退出", "考试尚未完成，确定要退出吗？\n退出后将无法继续考试。"):
                self.is_exam_finished = True
                self.window.destroy()
        else:
            self.window.destroy()


class QuestionListWindow:
    def __init__(self, parent, questions, answers, marked_questions, jump_callback):
        """题目列表窗口"""
        self.questions = questions
        self.answers = answers
        self.marked_questions = marked_questions
        self.jump_callback = jump_callback
        
        self.window = tk.Toplevel(parent)
        self.window.title("题目列表")
        self.window.geometry("400x500")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(main_frame, text="题目列表", font=("Arial", 14, "bold")).pack(pady=(0, 10))
        
        # 创建列表框
        listbox_frame = ttk.Frame(main_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        self.listbox = tk.Listbox(listbox_frame, font=("Arial", 10))
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.listbox.yview)
        self.listbox.configure(yscrollcommand=scrollbar.set)
        
        self.listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 填充题目列表
        for i, question in enumerate(self.questions):
            status = ""
            if str(i) in self.answers:
                status += "✓"
            if i in self.marked_questions:
                status += "★"
            
            question_text = question['question'][:50] + "..." if len(question['question']) > 50 else question['question']
            item_text = f"{i+1}. {status} {question_text}"
            
            self.listbox.insert(tk.END, item_text)
        
        # 双击跳转
        self.listbox.bind('<Double-Button-1>', self.on_double_click)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="跳转", command=self.jump_to_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def on_double_click(self, event):
        """双击事件"""
        self.jump_to_selected()
    
    def jump_to_selected(self):
        """跳转到选中题目"""
        selection = self.listbox.curselection()
        if selection:
            question_index = selection[0]
            self.jump_callback(question_index)
            self.window.destroy()


class ExamResultWindow:
    def __init__(self, parent, exam_manager, wrong_question_manager, record_id):
        """考试结果详情窗口"""
        self.exam_manager = exam_manager
        self.wrong_question_manager = wrong_question_manager
        self.record_id = record_id
        
        self.window = tk.Toplevel(parent)
        self.window.title("考试结果详情")
        self.window.geometry("900x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        # 获取考试记录
        self.record = exam_manager.get_exam_record_by_id(record_id)

        print(f"🔍 考试结果窗口初始化:")
        print(f"  记录ID: {record_id}")
        print(f"  记录获取结果: {'成功' if self.record else '失败'}")
        if self.record:
            print(f"  考试ID: {self.record.get('exam_id', '未知')}")
            print(f"  题目数量: {len(self.record.get('questions', []))}")
            print(f"  答案数量: {len(self.record.get('answers', {}))}")
        else:
            print("  ❌ 无法获取考试记录，错题添加功能将不可用")
        
        self.setup_ui()
        self.load_results()
    
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题和统计信息
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_frame, text="考试结果详情", font=("Arial", 16, "bold")).pack()
        
        if self.record:
            percentage = (self.record['score'] / self.record['total_score'] * 100) if self.record['total_score'] > 0 else 0
            stats_text = f"得分：{self.record['score']:.1f}/{self.record['total_score']:.1f} ({percentage:.1f}%)"
            ttk.Label(info_frame, text=stats_text, font=("Arial", 12)).pack(pady=(5, 0))
        
        # 结果列表
        result_frame = ttk.LabelFrame(main_frame, text="答题详情", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建树形视图
        columns = ('题号', '题目', '我的答案', '正确答案', '结果', '分数')
        self.tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.tree.heading(col, text=col)
            if col == '题号':
                self.tree.column(col, width=50)
            elif col in ['结果', '分数']:
                self.tree.column(col, width=80)
            elif col == '题目':
                self.tree.column(col, width=200)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.tree.bind('<Double-Button-1>', self.on_double_click)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="📝 查看解析", command=self.show_explanation).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="❌ 添加错题到错题本", command=self.add_wrong_questions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="📄 导出结果", command=self.export_result).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🔄 重新考试", command=self.retake_exam).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def load_results(self):
        """加载结果数据"""
        if not self.record:
            return
        
        questions = self.record['questions']
        answers = self.record['answers']
        
        for i, question in enumerate(questions):
            question_id = str(i)
            user_answer = answers.get(question_id, '未作答')
            correct_answer = question.get('correct_answer', '')
            
            # 判断答案是否正确
            is_correct = self.is_answer_correct(question, user_answer, correct_answer)
            result_text = "正确" if is_correct else "错误"
            score = question.get('score', 1) if is_correct else 0
            
            # 格式化答案显示
            user_answer_text = self.format_answer(user_answer)
            correct_answer_text = self.format_answer(correct_answer)
            
            # 截断题目文本
            question_text = question['question'][:30] + "..." if len(question['question']) > 30 else question['question']
            
            self.tree.insert('', tk.END, values=(
                i + 1, question_text, user_answer_text, correct_answer_text, result_text, score
            ))

    def on_double_click(self, event):
        """双击事件处理"""
        self.show_explanation()
    
    def is_answer_correct(self, question, user_answer, correct_answer):
        """判断答案是否正确"""
        if user_answer == '未作答':
            return False
        
        if question['type'] in ['single_choice', 'true_false']:
            return user_answer == correct_answer
        elif question['type'] == 'multiple_choice':
            if isinstance(user_answer, list) and isinstance(correct_answer, list):
                return set(user_answer) == set(correct_answer)
        
        # 主观题暂时无法自动判断
        return False
    
    def format_answer(self, answer):
        """格式化答案显示"""
        if isinstance(answer, list):
            return ', '.join(answer)
        elif isinstance(answer, str):
            return answer[:20] + "..." if len(answer) > 20 else answer
        else:
            return str(answer)
    
    def add_wrong_questions(self):
        """添加错题到错题本"""
        print("🔍 点击了添加错题到错题本按钮")

        if not self.record:
            print("❌ 没有考试记录")
            messagebox.showerror("错误", "没有考试记录数据！")
            return

        print(f"📋 考试记录信息:")
        print(f"  记录ID: {self.record.get('id', '未知')}")
        print(f"  考试ID: {self.record.get('exam_id', '未知')}")
        print(f"  题目数量: {len(self.record.get('questions', []))}")
        print(f"  答案数量: {len(self.record.get('answers', {}))}")

        try:
            print("🚀 开始调用批量添加方法...")
            added_count = self.wrong_question_manager.batch_add_wrong_questions_from_exam(self.record)
            print(f"✅ 批量添加完成，添加了 {added_count} 道错题")

            if added_count > 0:
                messagebox.showinfo("成功", f"已添加{added_count}道错题到错题本！")
            else:
                messagebox.showinfo("提示", "没有发现错题，或所有题目都答对了！")

        except Exception as e:
            print(f"❌ 添加错题失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"添加错题失败：{str(e)}")
    
    def show_explanation(self):
        """显示题目解析"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道题目！")
            return

        # 获取选中题目的索引
        item = self.tree.item(selection[0])
        question_num = int(item['values'][0]) - 1  # 题号从1开始，索引从0开始

        if 0 <= question_num < len(self.record['questions']):
            question = self.record['questions'][question_num]
            user_answer = self.record['answers'].get(str(question_num), '未作答')

            # 创建解析窗口
            ExplanationWindow(self.window, question, user_answer, question_num + 1)
        else:
            messagebox.showerror("错误", "无法获取题目信息！")

    def retake_exam(self):
        """重新考试"""
        if messagebox.askyesno("确认", "确定要重新开始这个考试吗？"):
            # 获取原始考试数据
            exam_data = self.exam_manager.get_exam_by_id(self.record['exam_id'])
            if exam_data:
                # 关闭当前窗口
                self.window.destroy()
                # 启动新的考试
                from src.ui.exam_window import ExamWindow
                ExamWindow(self.window.master, self.exam_manager, self.wrong_question_manager, exam_data)
            else:
                messagebox.showerror("错误", "无法获取考试数据！")

    def export_result(self):
        """导出考试结果"""
        try:
            from tkinter import filedialog
            import csv

            # 选择保存位置
            filename = filedialog.asksaveasfilename(
                title="导出考试结果",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)

                    # 写入标题
                    writer.writerow(['题号', '题目', '我的答案', '正确答案', '结果', '分数', '解析'])

                    # 写入数据
                    questions = self.record['questions']
                    answers = self.record['answers']

                    for i, question in enumerate(questions):
                        question_id = str(i)
                        user_answer = answers.get(question_id, '未作答')
                        correct_answer = question.get('correct_answer', '')
                        is_correct = self.is_answer_correct(question, user_answer, correct_answer)
                        result_text = "正确" if is_correct else "错误"
                        score = question.get('score', 1) if is_correct else 0
                        explanation = question.get('explanation', '暂无解析')

                        writer.writerow([
                            i + 1,
                            question['question'],
                            self.format_answer(user_answer),
                            self.format_answer(correct_answer),
                            result_text,
                            score,
                            explanation
                        ])

                messagebox.showinfo("成功", f"考试结果已导出到：\n{filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出失败：{str(e)}")


class ExplanationWindow:
    """题目解析窗口"""

    def __init__(self, parent, question, user_answer, question_num):
        self.question = question
        self.user_answer = user_answer
        self.question_num = question_num

        self.window = tk.Toplevel(parent)
        self.window.title(f"第{question_num}题 - 解析")
        self.window.geometry("700x500")
        self.window.transient(parent)
        self.window.grab_set()

        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(title_frame, text=f"第{self.question_num}题解析",
                 font=("Arial", 16, "bold")).pack()

        # 题目类型和分值
        type_name = self.get_question_type_name(self.question['type'])
        score = self.question.get('score', 1)
        ttk.Label(title_frame, text=f"{type_name} | {score}分",
                 font=("Arial", 10), foreground="gray").pack()

        # 题目内容
        question_frame = ttk.LabelFrame(main_frame, text="题目", padding=10)
        question_frame.pack(fill=tk.X, pady=(0, 10))

        question_text = tk.Text(question_frame, wrap=tk.WORD, height=4,
                               font=("Arial", 11), state=tk.DISABLED)
        question_text.pack(fill=tk.X)

        question_text.config(state=tk.NORMAL)
        question_text.insert(1.0, self.question['question'])
        question_text.config(state=tk.DISABLED)

        # 选项（如果是选择题）
        if self.question['type'] in ['single_choice', 'multiple_choice'] and 'options' in self.question:
            options_frame = ttk.LabelFrame(main_frame, text="选项", padding=10)
            options_frame.pack(fill=tk.X, pady=(0, 10))

            for i, option in enumerate(self.question['options']):
                option_label = chr(65 + i)  # A, B, C, D
                ttk.Label(options_frame, text=f"{option_label}. {option}",
                         font=("Arial", 10)).pack(anchor=tk.W, pady=2)

        # 答案对比
        answer_frame = ttk.LabelFrame(main_frame, text="答案对比", padding=10)
        answer_frame.pack(fill=tk.X, pady=(0, 10))

        # 我的答案
        my_answer_frame = ttk.Frame(answer_frame)
        my_answer_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(my_answer_frame, text="我的答案：", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        user_answer_text = self.format_answer(self.user_answer)
        is_correct = self.is_answer_correct()

        answer_color = "green" if is_correct else "red"
        answer_icon = "✓" if is_correct else "✗"

        answer_label = ttk.Label(my_answer_frame, text=f"{answer_icon} {user_answer_text}",
                                foreground=answer_color, font=("Arial", 10))
        answer_label.pack(side=tk.LEFT, padx=(5, 0))

        # 正确答案
        correct_answer_frame = ttk.Frame(answer_frame)
        correct_answer_frame.pack(fill=tk.X)

        ttk.Label(correct_answer_frame, text="正确答案：", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        correct_answer_text = self.format_answer(self.question.get('correct_answer', ''))
        ttk.Label(correct_answer_frame, text=correct_answer_text,
                 foreground="green", font=("Arial", 10)).pack(side=tk.LEFT, padx=(5, 0))

        # 解析内容
        explanation_frame = ttk.LabelFrame(main_frame, text="详细解析", padding=10)
        explanation_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        explanation_text = scrolledtext.ScrolledText(explanation_frame, wrap=tk.WORD,
                                                   font=("Arial", 10), height=8)
        explanation_text.pack(fill=tk.BOTH, expand=True)

        explanation = self.question.get('explanation', '暂无解析')
        explanation_text.insert(1.0, explanation)
        explanation_text.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        if not is_correct:
            ttk.Button(button_frame, text="❌ 添加到错题本",
                      command=self.add_to_wrong_questions).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)

    def get_question_type_name(self, question_type):
        """获取题目类型名称"""
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        return type_names.get(question_type, '未知题型')

    def format_answer(self, answer):
        """格式化答案显示"""
        if answer == '未作答' or not answer:
            return '未作答'

        if isinstance(answer, list):
            return ', '.join(answer)

        return str(answer)

    def is_answer_correct(self):
        """判断答案是否正确"""
        if self.user_answer == '未作答' or not self.user_answer:
            return False

        correct_answer = self.question.get('correct_answer', '')

        if self.question['type'] in ['single_choice', 'true_false']:
            return self.user_answer == correct_answer
        elif self.question['type'] == 'multiple_choice':
            if isinstance(self.user_answer, list) and isinstance(correct_answer, list):
                return set(self.user_answer) == set(correct_answer)
        elif self.question['type'] in ['short_answer', 'case_analysis']:
            # 简答题需要更复杂的判断，这里简化处理
            return self.user_answer.strip().lower() == correct_answer.strip().lower()

        return False

    def add_to_wrong_questions(self):
        """添加到错题本"""
        try:
            # 这里需要访问错题管理器，暂时显示提示
            messagebox.showinfo("提示", "该功能需要在考试结果窗口中使用'添加错题到错题本'按钮")
        except Exception as e:
            messagebox.showerror("错误", f"添加失败：{str(e)}")
