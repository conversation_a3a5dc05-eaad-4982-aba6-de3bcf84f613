#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试iOS界面
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """快速测试iOS界面"""
    try:
        print("🍎 快速测试iOS界面...")
        
        # 导入必要的模块
        from src.core.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.core.wrong_question_manager import WrongQuestionManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        exam_manager = ExamManager(db_manager)
        wrong_question_manager = WrongQuestionManager(db_manager)
        
        # 创建简单的测试数据
        test_exam_data = {
            'id': 1,
            'title': '🍎 iOS界面测试',
            'description': '测试iOS风格界面',
            'time_limit': 10,
            'questions': [
                {
                    'type': 'single_choice',
                    'question': '这个iOS风格界面看起来怎么样？',
                    'options': ['很棒！', '不错', '一般', '需要改进'],
                    'correct_answer': 'A',
                    'explanation': '希望您喜欢这个iOS风格的界面！',
                    'score': 1
                },
                {
                    'type': 'true_false',
                    'question': 'iOS风格界面比传统界面更美观。',
                    'correct_answer': '对',
                    'explanation': 'iOS风格确实更加现代化和美观',
                    'score': 1
                }
            ]
        }
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 导入并启动iOS界面
        from src.ui.ios_style_exam_window import iOSStyleExamWindow
        
        print("✅ 模块导入成功")
        print("🚀 启动iOS界面...")
        
        # 创建iOS界面
        ios_window = iOSStyleExamWindow(root, exam_manager, wrong_question_manager, test_exam_data)
        
        print("✅ iOS界面创建成功！")
        
        # 运行界面
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误对话框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", f"iOS界面测试失败：\n{str(e)}")
        root.destroy()

if __name__ == "__main__":
    quick_test()
