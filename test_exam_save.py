#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试考试保存和加载功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_exam_operations():
    """测试考试操作"""
    try:
        from src.utils.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.utils.config_manager import ConfigManager
        
        print("=== 考试保存和加载测试 ===")
        
        # 初始化
        config = ConfigManager()
        db = DatabaseManager(config)
        exam_manager = ExamManager(db)
        
        print("✓ 管理器初始化成功")
        
        # 创建测试题目
        test_questions = [
            {
                "type": "single_choice",
                "question": "Python是什么类型的语言？",
                "options": ["编译型", "解释型", "汇编型", "机器型"],
                "correct_answer": "B",
                "explanation": "Python是解释型语言",
                "score": 2
            },
            {
                "type": "true_false",
                "question": "Python支持面向对象编程。",
                "correct_answer": "对",
                "explanation": "Python确实支持面向对象编程",
                "score": 1
            },
            {
                "type": "short_answer",
                "question": "请简述Python的主要特点。",
                "correct_answer": "简洁易读、跨平台、丰富的库、动态类型等",
                "explanation": "Python具有多种优秀特性",
                "score": 3
            }
        ]
        
        print(f"准备保存考试，题目数量: {len(test_questions)}")
        
        # 测试创建考试
        try:
            exam_id = exam_manager.create_exam(
                title="Python基础测试",
                description="测试Python基础知识的考试",
                questions=test_questions,
                time_limit=45
            )
            
            print(f"✓ 考试创建成功，ID: {exam_id}")
            
        except Exception as e:
            print(f"✗ 考试创建失败: {str(e)}")
            return False
        
        # 测试获取考试
        try:
            saved_exam = exam_manager.get_exam_by_id(exam_id)
            if saved_exam:
                print(f"✓ 考试获取成功")
                print(f"  标题: {saved_exam['title']}")
                print(f"  描述: {saved_exam['description']}")
                print(f"  时间限制: {saved_exam['time_limit']}分钟")
                print(f"  题目数量: {len(saved_exam['questions'])}")
                
                # 验证题目内容
                for i, q in enumerate(saved_exam['questions']):
                    print(f"  题目{i+1}: {q['question'][:30]}...")
                    
            else:
                print("✗ 考试获取失败")
                return False
                
        except Exception as e:
            print(f"✗ 考试获取失败: {str(e)}")
            return False
        
        # 测试获取所有考试
        try:
            all_exams = exam_manager.get_all_exams()
            print(f"✓ 获取所有考试成功，总数: {len(all_exams)}")
            
            for exam in all_exams:
                print(f"  考试: ID={exam[0]}, 标题='{exam[1]}', 时间={exam[3]}分钟")
                
        except Exception as e:
            print(f"✗ 获取所有考试失败: {str(e)}")
            return False
        
        print("\n=== 所有测试通过 ===")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_exam_operations()
    
    if success:
        print("\n🎉 考试保存和加载功能正常！")
        print("现在可以在考试系统中正常生成和查看试卷了。")
    else:
        print("\n❌ 考试功能有问题，需要进一步调试。")
