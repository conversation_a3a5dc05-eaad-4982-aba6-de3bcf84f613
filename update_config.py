#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新配置文件，确保包含所有必要的配置项
"""

import os
import configparser

def update_config():
    """更新配置文件"""
    config_path = "config/config.ini"
    
    # 确保配置目录存在
    os.makedirs("config", exist_ok=True)
    
    # 读取现有配置
    config = configparser.ConfigParser()
    if os.path.exists(config_path):
        config.read(config_path, encoding='utf-8')
        print("✓ 读取现有配置文件")
    else:
        print("✓ 创建新的配置文件")
    
    # 确保API部分存在
    if not config.has_section('API'):
        config.add_section('API')
        print("✓ 添加API配置部分")
    
    # 添加中转API配置项（如果不存在）
    relay_configs = {
        'relay_api_key': 'your_relay_api_key_here',
        'relay_base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1',
        'relay_model': 'qwen-turbo'
    }
    
    for key, default_value in relay_configs.items():
        if not config.has_option('API', key):
            config.set('API', key, default_value)
            print(f"✓ 添加配置项: {key}")
        else:
            print(f"✓ 配置项已存在: {key}")
    
    # 确保其他必要的配置项存在
    other_configs = {
        'openai_api_key': 'your_openai_api_key_here',
        'openai_base_url': 'https://api.openai.com',
        'openai_model': 'gpt-3.5-turbo',
        'deepseek_api_key': 'your_deepseek_api_key_here',
        'deepseek_base_url': 'https://api.deepseek.com',
        'deepseek_model': 'deepseek-chat',
        'gemini_api_key': 'your_gemini_api_key_here',
        'gemini_model': 'gemini-pro',
        'doubao_api_key': 'your_doubao_api_key_here',
        'doubao_endpoint': 'your_doubao_endpoint_here',
        'doubao_model': 'doubao-seed-1-6-flash-250615'
    }
    
    for key, default_value in other_configs.items():
        if not config.has_option('API', key):
            config.set('API', key, default_value)
            print(f"✓ 添加配置项: {key}")
    
    # 确保其他部分存在
    if not config.has_section('DATABASE'):
        config.add_section('DATABASE')
        config.set('DATABASE', 'db_path', 'data/exam_system.db')
        print("✓ 添加DATABASE配置部分")
    
    if not config.has_section('EXAM'):
        config.add_section('EXAM')
        config.set('EXAM', 'default_exam_time', '60')
        config.set('EXAM', 'questions_per_exam', '20')
        config.set('EXAM', 'auto_save_interval', '30')
        print("✓ 添加EXAM配置部分")
    
    if not config.has_section('UI'):
        config.add_section('UI')
        config.set('UI', 'window_width', '1200')
        config.set('UI', 'window_height', '800')
        config.set('UI', 'font_size', '12')
        print("✓ 添加UI配置部分")
    
    # 保存配置文件
    with open(config_path, 'w', encoding='utf-8') as f:
        config.write(f)
    
    print(f"\n🎉 配置文件更新完成: {config_path}")
    print("现在可以正常启动程序了！")

if __name__ == "__main__":
    update_config()
