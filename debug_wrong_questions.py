#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试错题添加问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wrong_question_addition():
    """测试错题添加功能"""
    try:
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database import Database
        
        print("=== 调试错题添加功能 ===")
        
        db = Database()
        wq_manager = WrongQuestionManager(db)
        
        # 模拟考试记录数据
        mock_exam_record = {
            'id': 1,
            'exam_id': 1,
            'answers': {'0': 'A', '1': 'B', '2': '错'},  # 故意答错
            'score': 1.0,
            'total_score': 3.0,
            'questions': [
                {
                    'type': 'single_choice',
                    'question': '测试题目1：Python中哪个关键字用于定义函数？',
                    'options': ['function', 'def', 'func', 'define'],
                    'correct_answer': 'B',  # 正确答案是B，用户答A（错误）
                    'explanation': 'Python中使用def关键字来定义函数。',
                    'score': 1
                },
                {
                    'type': 'single_choice',
                    'question': '测试题目2：Python是什么类型的编程语言？',
                    'options': ['编译型语言', '解释型语言', '汇编语言', '机器语言'],
                    'correct_answer': 'B',  # 正确答案是B，用户也答B（正确）
                    'explanation': 'Python是一种解释型编程语言。',
                    'score': 1
                },
                {
                    'type': 'true_false',
                    'question': '测试题目3：Python是开源的编程语言。',
                    'correct_answer': '对',  # 正确答案是对，用户答错（错误）
                    'explanation': 'Python确实是开源的编程语言。',
                    'score': 1
                }
            ]
        }
        
        print("模拟考试记录:")
        print(f"  题目数量: {len(mock_exam_record['questions'])}")
        print(f"  答案数量: {len(mock_exam_record['answers'])}")
        print(f"  得分: {mock_exam_record['score']}/{mock_exam_record['total_score']}")
        
        # 详细分析每道题目
        print("\n题目答案分析:")
        questions = mock_exam_record['questions']
        answers = mock_exam_record['answers']
        
        for i, question in enumerate(questions):
            question_id = str(i)
            user_answer = answers.get(question_id, '')
            correct_answer = question.get('correct_answer', '')
            
            print(f"\n题目 {i+1}:")
            print(f"  题目: {question['question'][:50]}...")
            print(f"  类型: {question['type']}")
            print(f"  我的答案: '{user_answer}'")
            print(f"  正确答案: '{correct_answer}'")
            
            # 判断是否答错
            is_wrong = False
            if question['type'] in ['single_choice', 'true_false']:
                is_wrong = user_answer != correct_answer
                print(f"  比较结果: '{user_answer}' != '{correct_answer}' = {is_wrong}")
            elif question['type'] == 'multiple_choice':
                if isinstance(user_answer, list) and isinstance(correct_answer, list):
                    is_wrong = set(user_answer) != set(correct_answer)
                    print(f"  比较结果: {set(user_answer)} != {set(correct_answer)} = {is_wrong}")
                else:
                    is_wrong = True
                    print(f"  比较结果: 类型不匹配 = {is_wrong}")
            
            print(f"  是否错题: {is_wrong}")
        
        # 测试批量添加
        print(f"\n开始批量添加错题...")
        try:
            added_count = wq_manager.batch_add_wrong_questions_from_exam(mock_exam_record)
            print(f"✅ 批量添加成功，添加了 {added_count} 道错题")
        except Exception as e:
            print(f"❌ 批量添加失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 验证错题是否添加成功
        print(f"\n验证错题添加结果...")
        wrong_questions = wq_manager.get_all_wrong_questions()
        print(f"错题本中共有 {len(wrong_questions)} 道错题")
        
        if wrong_questions:
            print("最新错题:")
            for i, wq in enumerate(wrong_questions[:3], 1):  # 显示前3道
                print(f"  {i}. {wq[1][:50]}...")
                print(f"     正确答案: {wq[3]}, 我的答案: {wq[4]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_wrong_question_add():
    """测试单个错题添加"""
    try:
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database import Database
        
        print("\n=== 测试单个错题添加 ===")
        
        db = Database()
        wq_manager = WrongQuestionManager(db)
        
        # 直接添加一道错题
        print("直接添加错题...")
        question_id = wq_manager.add_wrong_question(
            question_text="直接添加的测试错题：Python的创始人是谁？",
            question_type="single_choice",
            correct_answer="Guido van Rossum",
            user_answer="Dennis Ritchie",
            explanation="Python是由Guido van Rossum创建的，不是Dennis Ritchie。"
        )
        
        print(f"✅ 单个错题添加成功，ID: {question_id}")
        
        # 验证添加结果
        added_question = wq_manager.get_wrong_question_by_id(question_id)
        if added_question:
            print("添加的错题信息:")
            print(f"  ID: {added_question[0]}")
            print(f"  题目: {added_question[1]}")
            print(f"  类型: {added_question[2]}")
            print(f"  正确答案: {added_question[3]}")
            print(f"  我的答案: {added_question[4]}")
            print(f"  解析: {added_question[5]}")
        else:
            print("❌ 无法获取添加的错题")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 单个错题添加测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from src.utils.database import Database
        
        print("\n=== 测试数据库连接 ===")
        
        db = Database()
        
        # 检查错题表是否存在
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name='wrong_questions'"
        result = db.execute_query(query)
        
        if result:
            print("✅ wrong_questions表存在")
            
            # 检查表结构
            query = "PRAGMA table_info(wrong_questions)"
            columns = db.execute_query(query)
            print("表结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            # 检查现有数据
            query = "SELECT COUNT(*) FROM wrong_questions"
            count_result = db.execute_query(query)
            count = count_result[0][0] if count_result else 0
            print(f"当前错题数量: {count}")
            
        else:
            print("❌ wrong_questions表不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始调试错题添加问题...\n")
    
    # 测试数据库连接
    db_ok = test_database_connection()
    
    # 测试单个错题添加
    single_ok = test_single_wrong_question_add()
    
    # 测试批量错题添加
    batch_ok = test_wrong_question_addition()
    
    print("\n=== 调试总结 ===")
    
    results = [
        ("数据库连接", db_ok),
        ("单个错题添加", single_ok),
        ("批量错题添加", batch_ok)
    ]
    
    all_passed = True
    for name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 错题添加功能正常！")
        print("\n可能的问题：")
        print("1. 考试结果窗口没有正确传递考试记录")
        print("2. 用户没有点击'添加错题到错题本'按钮")
        print("3. 答案格式不匹配导致判断错误")
        
        print("\n建议操作：")
        print("1. 完成考试后，在结果窗口点击'添加错题到错题本'")
        print("2. 检查是否有成功提示")
        print("3. 刷新错题本界面")
    else:
        print("\n⚠️ 发现问题，需要进一步修复")

if __name__ == "__main__":
    main()
