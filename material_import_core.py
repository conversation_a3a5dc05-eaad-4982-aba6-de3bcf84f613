#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
材料导入核心功能模块
可独立集成到其他系统中使用
"""

import os
import PyPDF2
from datetime import datetime
from typing import Optional, List, Dict, Any


class MaterialImporter:
    """材料导入器 - 核心功能类"""
    
    def __init__(self, db_connection=None):
        """
        初始化材料导入器
        
        Args:
            db_connection: 数据库连接对象，需要支持execute()方法
        """
        self.db = db_connection
    
    def import_text_file(self, file_path: str) -> Dict[str, Any]:
        """
        导入文本文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 包含导入结果的字典
                - success: bool, 是否成功
                - material_id: int, 材料ID（如果成功）
                - title: str, 材料标题
                - content: str, 材料内容
                - file_type: str, 文件类型
                - error: str, 错误信息（如果失败）
        """
        try:
            # 尝试UTF-8编码读取
            content = self._read_text_file_with_encoding(file_path, 'utf-8')
            if content is None:
                # 尝试GBK编码
                content = self._read_text_file_with_encoding(file_path, 'gbk')
                if content is None:
                    return {
                        'success': False,
                        'error': '无法读取文件：不支持的编码格式'
                    }
            
            title = os.path.basename(file_path)
            
            # 如果有数据库连接，保存到数据库
            material_id = None
            if self.db:
                material_id = self._save_to_database(title, content, 'text')
            
            return {
                'success': True,
                'material_id': material_id,
                'title': title,
                'content': content,
                'file_type': 'text',
                'file_size': len(content),
                'created_at': datetime.now()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'导入文本文件失败: {str(e)}'
            }
    
    def import_pdf_file(self, file_path: str) -> Dict[str, Any]:
        """
        导入PDF文件
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            dict: 包含导入结果的字典
        """
        try:
            content = ""
            page_count = 0
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                page_count = len(pdf_reader.pages)
                
                for page_num in range(page_count):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text.strip():  # 只添加非空页面
                        content += page_text + "\n"
            
            if not content.strip():
                return {
                    'success': False,
                    'error': 'PDF文件中未找到可提取的文本内容'
                }
            
            title = os.path.basename(file_path)
            
            # 如果有数据库连接，保存到数据库
            material_id = None
            if self.db:
                material_id = self._save_to_database(title, content, 'pdf')
            
            return {
                'success': True,
                'material_id': material_id,
                'title': title,
                'content': content,
                'file_type': 'pdf',
                'page_count': page_count,
                'file_size': len(content),
                'created_at': datetime.now()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'导入PDF文件失败: {str(e)}'
            }
    
    def import_from_text(self, title: str, content: str) -> Dict[str, Any]:
        """
        直接从文本内容导入材料
        
        Args:
            title: 材料标题
            content: 材料内容
            
        Returns:
            dict: 包含导入结果的字典
        """
        try:
            if not title.strip():
                return {
                    'success': False,
                    'error': '标题不能为空'
                }
            
            if not content.strip():
                return {
                    'success': False,
                    'error': '内容不能为空'
                }
            
            # 如果有数据库连接，保存到数据库
            material_id = None
            if self.db:
                material_id = self._save_to_database(title, content, 'manual')
            
            return {
                'success': True,
                'material_id': material_id,
                'title': title,
                'content': content,
                'file_type': 'manual',
                'file_size': len(content),
                'created_at': datetime.now()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'保存材料失败: {str(e)}'
            }
    
    def _read_text_file_with_encoding(self, file_path: str, encoding: str) -> Optional[str]:
        """
        使用指定编码读取文本文件
        
        Args:
            file_path: 文件路径
            encoding: 编码格式
            
        Returns:
            str: 文件内容，如果失败返回None
        """
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return file.read()
        except (UnicodeDecodeError, UnicodeError):
            return None
        except Exception:
            return None
    
    def _save_to_database(self, title: str, content: str, file_type: str) -> Optional[int]:
        """
        保存材料到数据库
        
        Args:
            title: 标题
            content: 内容
            file_type: 文件类型
            
        Returns:
            int: 材料ID，如果失败返回None
        """
        if not self.db:
            return None
        
        try:
            query = """
                INSERT INTO materials (title, content, file_type, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """
            now = datetime.now()
            params = (title, content, file_type, now, now)
            
            # 执行插入并返回ID
            cursor = self.db.execute(query, params)
            self.db.commit()
            return cursor.lastrowid
            
        except Exception as e:
            if hasattr(self.db, 'rollback'):
                self.db.rollback()
            raise Exception(f"保存材料到数据库失败: {str(e)}")


class MaterialManager:
    """材料管理器 - 提供完整的材料管理功能"""
    
    def __init__(self, db_connection=None):
        """
        初始化材料管理器
        
        Args:
            db_connection: 数据库连接对象
        """
        self.db = db_connection
        self.importer = MaterialImporter(db_connection)
    
    def get_all_materials(self) -> List[Dict[str, Any]]:
        """获取所有材料列表"""
        if not self.db:
            return []
        
        try:
            query = "SELECT id, title, file_type, created_at FROM materials ORDER BY created_at DESC"
            cursor = self.db.execute(query)
            rows = cursor.fetchall()
            
            materials = []
            for row in rows:
                materials.append({
                    'id': row[0],
                    'title': row[1],
                    'file_type': row[2],
                    'created_at': row[3]
                })
            
            return materials
            
        except Exception as e:
            raise Exception(f"获取材料列表失败: {str(e)}")
    
    def get_material_by_id(self, material_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取材料详情"""
        if not self.db:
            return None
        
        try:
            query = "SELECT * FROM materials WHERE id = ?"
            cursor = self.db.execute(query, (material_id,))
            row = cursor.fetchone()
            
            if row:
                return {
                    'id': row[0],
                    'title': row[1],
                    'content': row[2],
                    'file_type': row[3],
                    'created_at': row[4],
                    'updated_at': row[5]
                }
            
            return None
            
        except Exception as e:
            raise Exception(f"获取材料详情失败: {str(e)}")
    
    def search_materials(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索材料"""
        if not self.db or not keyword.strip():
            return self.get_all_materials()
        
        try:
            query = """
                SELECT id, title, file_type, created_at 
                FROM materials 
                WHERE title LIKE ? OR content LIKE ?
                ORDER BY created_at DESC
            """
            search_term = f"%{keyword}%"
            cursor = self.db.execute(query, (search_term, search_term))
            rows = cursor.fetchall()
            
            materials = []
            for row in rows:
                materials.append({
                    'id': row[0],
                    'title': row[1],
                    'file_type': row[2],
                    'created_at': row[3]
                })
            
            return materials
            
        except Exception as e:
            raise Exception(f"搜索材料失败: {str(e)}")
    
    def delete_material(self, material_id: int) -> bool:
        """删除材料"""
        if not self.db:
            return False
        
        try:
            query = "DELETE FROM materials WHERE id = ?"
            cursor = self.db.execute(query, (material_id,))
            self.db.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            if hasattr(self.db, 'rollback'):
                self.db.rollback()
            raise Exception(f"删除材料失败: {str(e)}")
    
    def update_material(self, material_id: int, title: str, content: str) -> bool:
        """更新材料"""
        if not self.db:
            return False
        
        try:
            query = """
                UPDATE materials 
                SET title = ?, content = ?, updated_at = ?
                WHERE id = ?
            """
            now = datetime.now()
            cursor = self.db.execute(query, (title, content, now, material_id))
            self.db.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            if hasattr(self.db, 'rollback'):
                self.db.rollback()
            raise Exception(f"更新材料失败: {str(e)}")
    
    def get_material_stats(self) -> Dict[str, Any]:
        """获取材料统计信息"""
        if not self.db:
            return {'total_count': 0, 'by_type': {}}
        
        try:
            stats = {}
            
            # 总材料数
            cursor = self.db.execute("SELECT COUNT(*) FROM materials")
            stats['total_count'] = cursor.fetchone()[0]
            
            # 按类型统计
            cursor = self.db.execute("SELECT file_type, COUNT(*) FROM materials GROUP BY file_type")
            stats['by_type'] = {row[0]: row[1] for row in cursor.fetchall()}
            
            return stats
            
        except Exception as e:
            raise Exception(f"获取统计信息失败: {str(e)}")
