#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化启动画面
提供专业的启动体验和加载进度显示
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import math

class SplashScreen:
    def __init__(self, parent=None):
        """初始化启动画面"""
        self.parent = parent
        self.splash = None
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar()
        self.animation_running = True
        
        # 创建启动窗口
        self.create_splash_window()
        
    def create_splash_window(self):
        """创建启动窗口"""
        self.splash = tk.Toplevel() if self.parent else tk.Tk()
        
        # 窗口设置
        self.splash.title("考试系统启动中...")
        self.splash.geometry("500x350")
        self.splash.resizable(False, False)
        self.splash.configure(bg='#1a1a1a')
        
        # 移除窗口装饰
        self.splash.overrideredirect(True)
        
        # 居中显示
        self.center_window()
        
        # 创建界面元素
        self.create_ui_elements()
        
        # 启动动画
        self.start_animations()
        
    def center_window(self):
        """窗口居中显示"""
        self.splash.update_idletasks()
        width = 500
        height = 350
        x = (self.splash.winfo_screenwidth() // 2) - (width // 2)
        y = (self.splash.winfo_screenheight() // 2) - (height // 2)
        self.splash.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_ui_elements(self):
        """创建UI元素"""
        # 主容器
        main_frame = tk.Frame(self.splash, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # Logo区域
        logo_frame = tk.Frame(main_frame, bg='#1a1a1a')
        logo_frame.pack(fill=tk.X, pady=(0, 30))
        
        # 系统图标（使用文字图标）
        icon_label = tk.Label(logo_frame, 
                             text="📚",
                             font=("Segoe UI Emoji", 48),
                             bg='#1a1a1a', fg='#007AFF')
        icon_label.pack()
        
        # 系统标题
        title_label = tk.Label(logo_frame,
                              text="智能考试系统",
                              font=("Microsoft YaHei", 24, "bold"),
                              bg='#1a1a1a', fg='#FFFFFF')
        title_label.pack(pady=(10, 0))
        
        # 版本信息
        version_label = tk.Label(logo_frame,
                                text="Kaoshi v2.0 - 现代化版本",
                                font=("Microsoft YaHei", 12),
                                bg='#1a1a1a', fg='#888888')
        version_label.pack(pady=(5, 0))
        
        # 进度区域
        progress_frame = tk.Frame(main_frame, bg='#1a1a1a')
        progress_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 状态文字
        self.status_label = tk.Label(progress_frame,
                                    textvariable=self.status_var,
                                    font=("Microsoft YaHei", 11),
                                    bg='#1a1a1a', fg='#CCCCCC')
        self.status_label.pack(pady=(0, 10))
        
        # 现代化进度条
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("Modern.Horizontal.TProgressbar",
                       background='#007AFF',
                       troughcolor='#333333',
                       borderwidth=0,
                       lightcolor='#007AFF',
                       darkcolor='#007AFF')
        
        self.progress_bar = ttk.Progressbar(progress_frame,
                                           variable=self.progress_var,
                                           maximum=100,
                                           style="Modern.Horizontal.TProgressbar",
                                           length=400)
        self.progress_bar.pack(pady=(0, 10))
        
        # 进度百分比
        self.percent_label = tk.Label(progress_frame,
                                     text="0%",
                                     font=("Microsoft YaHei", 10),
                                     bg='#1a1a1a', fg='#007AFF')
        self.percent_label.pack()
        
        # 底部信息
        footer_frame = tk.Frame(main_frame, bg='#1a1a1a')
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        footer_label = tk.Label(footer_frame,
                               text="正在加载系统组件，请稍候...",
                               font=("Microsoft YaHei", 9),
                               bg='#1a1a1a', fg='#666666')
        footer_label.pack()
        
    def start_animations(self):
        """启动动画效果"""
        # 启动进度条动画
        threading.Thread(target=self.animate_progress, daemon=True).start()
        
        # 启动图标动画
        threading.Thread(target=self.animate_icon, daemon=True).start()
        
    def animate_progress(self):
        """进度条动画"""
        steps = [
            (10, "初始化系统配置..."),
            (25, "加载数据库连接..."),
            (40, "初始化AI接口..."),
            (55, "加载用户界面组件..."),
            (70, "准备考试模块..."),
            (85, "加载错题本系统..."),
            (95, "完成系统初始化..."),
            (100, "启动完成！")
        ]
        
        for progress, status in steps:
            if not self.animation_running:
                break
                
            # 平滑进度更新
            current = self.progress_var.get()
            step_size = (progress - current) / 10
            
            for i in range(10):
                if not self.animation_running:
                    break
                current += step_size
                try:
                    self.splash.after(0, lambda p=current, s=status: self.update_progress(p, s))
                except:
                    # 如果主线程不在主循环中，直接更新
                    self.update_progress(current, status)
                time.sleep(0.05)
            
            time.sleep(0.3)  # 每个步骤间的停顿
            
    def animate_icon(self):
        """图标动画效果"""
        icons = ["📚", "📖", "📝", "📊", "🎯"]
        icon_index = 0
        
        while self.animation_running and self.progress_var.get() < 100:
            try:
                # 查找图标标签并更新
                for widget in self.splash.winfo_children():
                    if isinstance(widget, tk.Frame):
                        for child in widget.winfo_children():
                            if isinstance(child, tk.Frame):
                                for grandchild in child.winfo_children():
                                    if isinstance(grandchild, tk.Label) and grandchild.cget('font')[1] == 48:
                                        self.splash.after(0, lambda: grandchild.config(text=icons[icon_index]))
                                        break
                
                icon_index = (icon_index + 1) % len(icons)
                time.sleep(0.5)
            except:
                break
                
    def update_progress(self, progress, status):
        """更新进度"""
        try:
            self.progress_var.set(progress)
            self.status_var.set(status)
            self.percent_label.config(text=f"{int(progress)}%")
            
            # 进度条颜色变化
            if progress >= 100:
                style = ttk.Style()
                style.configure("Modern.Horizontal.TProgressbar", background='#30D158')
        except:
            pass
            
    def simulate_loading(self, callback=None):
        """模拟加载过程"""
        def loading_thread():
            try:
                # 等待动画完成
                while self.animation_running and self.progress_var.get() < 100:
                    time.sleep(0.1)
                    if not self.animation_running:
                        break

                if not self.animation_running:
                    return

                # 显示完成状态
                time.sleep(0.5)

                # 关闭启动画面
                self.animation_running = False
                if self.splash and callback:
                    self.splash.after(0, callback)
                elif self.splash:
                    self.splash.after(0, self.close)
            except Exception as e:
                print(f"启动画面线程错误: {e}")
                self.animation_running = False

        threading.Thread(target=loading_thread, daemon=True).start()
        
    def close(self):
        """关闭启动画面"""
        self.animation_running = False
        try:
            if self.splash:
                self.splash.destroy()
                self.splash = None
        except Exception as e:
            print(f"关闭启动画面错误: {e}")
            
    def show(self):
        """显示启动画面"""
        if self.splash:
            self.splash.deiconify()
            self.splash.lift()
            self.splash.focus_force()
            
def show_splash_screen(callback=None):
    """显示启动画面的便捷函数"""
    splash = SplashScreen()
    splash.simulate_loading(callback)
    return splash

if __name__ == "__main__":
    # 测试启动画面
    def on_complete():
        print("启动完成！")
        root.quit()
        
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    splash = show_splash_screen(on_complete)
    root.mainloop()
