#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能助手界面
提供聊天式的学习指导和答疑界面
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from datetime import datetime
import threading
from typing import Dict, List

class AssistantWindow:
    def __init__(self, parent, intelligent_assistant, theme_manager=None):
        """初始化智能助手窗口"""
        self.parent = parent
        self.assistant = intelligent_assistant
        self.theme_manager = theme_manager
        
        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("🤖 智能学习助手")
        self.window.geometry("800x600")
        self.window.transient(parent)
        
        # 对话历史
        self.conversation_display = []
        
        # 创建界面
        self.create_ui()
        
        # 显示欢迎消息
        self.show_welcome_message()
        
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题栏
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(title_frame, text="🤖 智能学习助手", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # 功能按钮
        self.create_function_buttons(title_frame)
        
        # 对话区域
        self.create_chat_area(main_frame)
        
        # 输入区域
        self.create_input_area(main_frame)
        
        # 快捷建议区域
        self.create_suggestions_area(main_frame)
        
    def create_function_buttons(self, parent):
        """创建功能按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(side=tk.RIGHT)
        
        # 清空对话按钮
        clear_btn = ttk.Button(button_frame, text="🗑️ 清空", 
                              command=self.clear_conversation, width=8)
        clear_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 导出对话按钮
        export_btn = ttk.Button(button_frame, text="📤 导出", 
                               command=self.export_conversation, width=8)
        export_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 学习分析按钮
        analysis_btn = ttk.Button(button_frame, text="📊 分析", 
                                 command=self.show_learning_analysis, width=8)
        analysis_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
    def create_chat_area(self, parent):
        """创建对话区域"""
        chat_frame = ttk.LabelFrame(parent, text="💬 对话记录", padding=5)
        chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建滚动文本区域
        self.chat_display = scrolledtext.ScrolledText(
            chat_frame,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 11),
            height=20,
            state=tk.DISABLED
        )
        self.chat_display.pack(fill=tk.BOTH, expand=True)
        
        # 配置文本标签样式
        self.chat_display.tag_configure("user", foreground="#2196F3", font=("Microsoft YaHei", 11, "bold"))
        self.chat_display.tag_configure("assistant", foreground="#4CAF50", font=("Microsoft YaHei", 11, "bold"))
        self.chat_display.tag_configure("timestamp", foreground="#757575", font=("Microsoft YaHei", 9))
        self.chat_display.tag_configure("suggestion", foreground="#FF9800", font=("Microsoft YaHei", 10))
        
    def create_input_area(self, parent):
        """创建输入区域"""
        input_frame = ttk.Frame(parent)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 输入标签
        ttk.Label(input_frame, text="💭 请输入你的问题:").pack(anchor=tk.W, pady=(0, 5))
        
        # 输入框和按钮的容器
        entry_frame = ttk.Frame(input_frame)
        entry_frame.pack(fill=tk.X)
        
        # 输入框
        self.input_entry = ttk.Entry(entry_frame, font=("Microsoft YaHei", 11))
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 绑定回车键
        self.input_entry.bind("<Return>", self.send_message)
        
        # 发送按钮
        send_btn = ttk.Button(entry_frame, text="📤 发送", 
                             command=self.send_message, width=10)
        send_btn.pack(side=tk.RIGHT)
        
        # 语音输入按钮（预留）
        voice_btn = ttk.Button(entry_frame, text="🎤", 
                              command=self.voice_input, width=5)
        voice_btn.pack(side=tk.RIGHT, padx=(0, 5))
        
    def create_suggestions_area(self, parent):
        """创建快捷建议区域"""
        suggestions_frame = ttk.LabelFrame(parent, text="💡 快捷建议", padding=5)
        suggestions_frame.pack(fill=tk.X)
        
        # 建议按钮容器
        self.suggestions_container = ttk.Frame(suggestions_frame)
        self.suggestions_container.pack(fill=tk.X)
        
        # 默认建议
        default_suggestions = [
            "查看我的学习进度",
            "给我一些学习建议",
            "分析我的薄弱环节",
            "制定学习计划"
        ]
        
        self.update_suggestions(default_suggestions)
        
    def update_suggestions(self, suggestions: List[str]):
        """更新建议按钮"""
        try:
            # 清除现有按钮
            for widget in self.suggestions_container.winfo_children():
                widget.destroy()
            
            # 创建新按钮
            for i, suggestion in enumerate(suggestions[:6]):  # 最多显示6个建议
                btn = ttk.Button(
                    self.suggestions_container,
                    text=suggestion,
                    command=lambda s=suggestion: self.send_suggestion(s),
                    width=len(suggestion) + 2
                )
                btn.grid(row=i//3, column=i%3, padx=5, pady=2, sticky="w")
                
            # 配置网格权重
            for i in range(3):
                self.suggestions_container.grid_columnconfigure(i, weight=1)
                
        except Exception as e:
            print(f"❌ 更新建议按钮失败: {e}")
            
    def show_welcome_message(self):
        """显示欢迎消息"""
        try:
            welcome_response = self.assistant.process_user_query("你好")
            self.display_message("assistant", welcome_response['content'])
            
            if welcome_response.get('suggestions'):
                self.update_suggestions(welcome_response['suggestions'])
                
        except Exception as e:
            print(f"❌ 显示欢迎消息失败: {e}")
            self.display_message("assistant", "你好！我是你的智能学习助手，有什么可以帮助你的吗？")
            
    def send_message(self, event=None):
        """发送消息"""
        try:
            message = self.input_entry.get().strip()
            if not message:
                return
            
            # 清空输入框
            self.input_entry.delete(0, tk.END)
            
            # 显示用户消息
            self.display_message("user", message)
            
            # 显示思考状态
            self.display_thinking()
            
            # 在后台处理回答
            threading.Thread(target=self.process_message, args=(message,), daemon=True).start()
            
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            
    def send_suggestion(self, suggestion: str):
        """发送建议消息"""
        try:
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, suggestion)
            self.send_message()
            
        except Exception as e:
            print(f"❌ 发送建议失败: {e}")
            
    def process_message(self, message: str):
        """处理消息（后台线程）"""
        try:
            # 调用智能助手处理
            response = self.assistant.process_user_query(message)
            
            # 在主线程中更新UI
            self.window.after(0, lambda: self.handle_response(response))
            
        except Exception as e:
            print(f"❌ 处理消息失败: {e}")
            error_response = {
                'content': '抱歉，我现在无法处理你的问题，请稍后再试。',
                'suggestions': ['重新尝试', '查看帮助']
            }
            self.window.after(0, lambda: self.handle_response(error_response))
            
    def handle_response(self, response: Dict):
        """处理助手回答"""
        try:
            # 移除思考状态
            self.remove_thinking()
            
            # 显示助手回答
            self.display_message("assistant", response.get('content', ''))
            
            # 更新建议
            if response.get('suggestions'):
                self.update_suggestions(response['suggestions'])
                
            # 如果有特殊类型的回答，进行特殊处理
            response_type = response.get('type', '')
            if response_type == 'analysis':
                self.handle_analysis_response(response)
            elif response_type == 'planning':
                self.handle_planning_response(response)
                
        except Exception as e:
            print(f"❌ 处理回答失败: {e}")
            
    def display_message(self, sender: str, content: str):
        """显示消息"""
        try:
            self.chat_display.config(state=tk.NORMAL)
            
            # 添加时间戳
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            if sender == "user":
                self.chat_display.insert(tk.END, f"[{timestamp}] 你: ", "timestamp")
                self.chat_display.insert(tk.END, f"{content}\n\n", "user")
            else:
                self.chat_display.insert(tk.END, f"[{timestamp}] 助手: ", "timestamp")
                self.chat_display.insert(tk.END, f"{content}\n\n", "assistant")
            
            # 滚动到底部
            self.chat_display.see(tk.END)
            self.chat_display.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"❌ 显示消息失败: {e}")
            
    def display_thinking(self):
        """显示思考状态"""
        try:
            self.chat_display.config(state=tk.NORMAL)
            self.chat_display.insert(tk.END, "🤔 助手正在思考...\n", "suggestion")
            self.chat_display.see(tk.END)
            self.chat_display.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"❌ 显示思考状态失败: {e}")
            
    def remove_thinking(self):
        """移除思考状态"""
        try:
            self.chat_display.config(state=tk.NORMAL)
            
            # 获取最后一行内容
            last_line = self.chat_display.get("end-2l", "end-1l")
            if "助手正在思考" in last_line:
                self.chat_display.delete("end-2l", "end-1l")
                
            self.chat_display.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"❌ 移除思考状态失败: {e}")
            
    def handle_analysis_response(self, response: Dict):
        """处理分析类回答"""
        try:
            # 可以在这里添加特殊的分析数据展示
            pass
            
        except Exception as e:
            print(f"❌ 处理分析回答失败: {e}")
            
    def handle_planning_response(self, response: Dict):
        """处理计划类回答"""
        try:
            # 可以在这里添加学习计划的特殊展示
            pass
            
        except Exception as e:
            print(f"❌ 处理计划回答失败: {e}")
            
    def voice_input(self):
        """语音输入（预留功能）"""
        try:
            # 这里可以集成语音识别功能
            self.display_message("assistant", "语音输入功能正在开发中，敬请期待！")
            
        except Exception as e:
            print(f"❌ 语音输入失败: {e}")
            
    def clear_conversation(self):
        """清空对话"""
        try:
            self.chat_display.config(state=tk.NORMAL)
            self.chat_display.delete(1.0, tk.END)
            self.chat_display.config(state=tk.DISABLED)
            
            # 重新显示欢迎消息
            self.show_welcome_message()
            
        except Exception as e:
            print(f"❌ 清空对话失败: {e}")
            
    def export_conversation(self):
        """导出对话记录"""
        try:
            from tkinter import filedialog
            
            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                title="导出对话记录",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if file_path:
                # 获取对话内容
                conversation_content = self.chat_display.get(1.0, tk.END)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"智能学习助手对话记录\n")
                    f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(conversation_content)
                
                self.display_message("assistant", f"对话记录已导出到: {file_path}")
                
        except Exception as e:
            print(f"❌ 导出对话失败: {e}")
            self.display_message("assistant", "导出对话记录失败，请稍后再试。")
            
    def show_learning_analysis(self):
        """显示学习分析"""
        try:
            # 自动发送学习分析请求
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, "分析我的学习情况")
            self.send_message()
            
        except Exception as e:
            print(f"❌ 显示学习分析失败: {e}")
            
    def apply_theme(self):
        """应用主题"""
        try:
            if self.theme_manager:
                # 应用主题到窗口组件
                self.theme_manager.apply_to_widget(self.window, "frame")
                
        except Exception as e:
            print(f"❌ 应用主题失败: {e}")
            
    def get_conversation_summary(self) -> Dict:
        """获取对话摘要"""
        try:
            return self.assistant.get_conversation_summary()
            
        except Exception as e:
            print(f"❌ 获取对话摘要失败: {e}")
            return {}
