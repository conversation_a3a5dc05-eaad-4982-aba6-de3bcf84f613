#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试豆包API修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_doubao():
    """测试增强的豆包客户端"""
    try:
        from src.api.enhanced_doubao_client import EnhancedDoubaoClient
        
        print("=== 增强豆包客户端测试 ===")
        
        # 创建客户端
        client = EnhancedDoubaoClient(
            api_key="2f62a42c-6ea7-4c2e-8b51-9d2e93f6e9c6",
            endpoint="https://ark.cn-beijing.volces.com/api/v3",
            model="doubao-seed-1-6-flash-250615"
        )
        
        print("✓ 增强豆包客户端创建成功")
        print(f"使用模型: {client.model}")
        
        # 获取可用模型
        print("\n获取可用模型...")
        models = client.get_available_models()
        print(f"✓ 获取到 {len(models)} 个模型:")
        for model in models:
            print(f"  - {model}")
        
        # 测试连接
        print("\n测试连接...")
        if client.test_connection():
            print("✓ 连接测试成功")
        else:
            print("✗ 连接测试失败")
            return False
        
        # 测试题目生成
        print("\n测试题目生成...")
        material = "Python是一种高级编程语言，具有简洁的语法和强大的功能。Python支持面向对象编程，也支持函数式编程。"
        
        try:
            questions = client.generate_questions(
                material=material,
                question_types=['single_choice', 'true_false'],
                num_questions=2
            )
            
            if questions:
                print(f"✓ 成功生成 {len(questions)} 道题目")
                for i, q in enumerate(questions, 1):
                    print(f"\n题目 {i}:")
                    print(f"  类型: {q.get('type', 'unknown')}")
                    print(f"  题目: {q.get('question', 'N/A')}")
                    print(f"  答案: {q.get('correct_answer', 'N/A')}")
                return True
            else:
                print("✗ 题目生成失败")
                return False
                
        except Exception as e:
            print(f"✗ 题目生成出错: {str(e)}")
            return False
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_universal_api_manager():
    """测试通用API管理器"""
    try:
        from src.api.universal_api_manager import UniversalAPIManager
        
        print("\n=== 通用API管理器测试 ===")
        
        # 创建管理器
        api_manager = UniversalAPIManager()
        print("✓ API管理器创建成功")
        
        # 更新豆包配置
        api_manager.update_provider(
            "doubao",
            api_key="2f62a42c-6ea7-4c2e-8b51-9d2e93f6e9c6",
            test_model="doubao-seed-1-6-flash-250615"
        )
        print("✓ 豆包配置更新成功")
        
        # 测试豆包连接
        print("测试豆包连接...")
        success = api_manager.test_provider_connection("doubao")
        
        provider = api_manager.providers["doubao"]
        print(f"状态: {provider.status}")
        print(f"模型数量: {len(provider.models)}")
        
        if success:
            print("✓ 豆包连接测试成功")
            if provider.models:
                print("可用模型:")
                for model in provider.models[:5]:  # 只显示前5个
                    print(f"  - {model}")
        else:
            print("✗ 豆包连接测试失败")
            print(f"错误: {provider.error_message}")
        
        return success
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试豆包API修复...\n")
    
    # 测试增强豆包客户端
    enhanced_success = test_enhanced_doubao()
    
    # 测试通用API管理器
    manager_success = test_universal_api_manager()
    
    print("\n=== 测试总结 ===")
    if enhanced_success:
        print("✅ 增强豆包客户端测试通过")
    else:
        print("❌ 增强豆包客户端测试失败")
    
    if manager_success:
        print("✅ 通用API管理器测试通过")
    else:
        print("❌ 通用API管理器测试失败")
    
    if enhanced_success and manager_success:
        print("\n🎉 所有测试通过！")
        print("现在可以正常使用豆包API了")
        print("模型名称已修正为: doubao-seed-1-6-flash-250615")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
