#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试iOS风格考试界面
"""

import tkinter as tk
from src.core.database_manager import DatabaseManager
from src.core.exam_manager import ExamManager
from src.core.wrong_question_manager import WrongQuestionManager
from src.ui.ios_style_exam_window import iOSStyleExamWindow

def test_ios_interface():
    """测试iOS风格界面"""
    print("🍎 测试iOS风格考试界面...")
    
    # 初始化数据库和管理器
    db_manager = DatabaseManager()
    exam_manager = ExamManager(db_manager)
    wrong_question_manager = WrongQuestionManager(db_manager)
    
    # 创建测试考试数据
    test_questions = [
        {
            "type": "single_choice",
            "question": "iOS系统是由哪家公司开发的？",
            "options": ["Google", "Apple", "Microsoft", "Samsung"],
            "correct_answer": "B",
            "explanation": "iOS是苹果公司开发的移动操作系统",
            "score": 2
        },
        {
            "type": "multiple_choice", 
            "question": "以下哪些是iOS的特点？（多选）",
            "options": ["流畅动画", "圆角设计", "扁平化图标", "毛玻璃效果"],
            "correct_answer": "ABCD",
            "explanation": "这些都是iOS的设计特点",
            "score": 3
        },
        {
            "type": "true_false",
            "question": "iOS界面设计注重用户体验和视觉美感。",
            "correct_answer": "对",
            "explanation": "iOS确实以优秀的用户体验和美观设计著称",
            "score": 1
        },
        {
            "type": "short_answer",
            "question": "请简述iOS设计语言的主要特点。",
            "correct_answer": "简洁、直观、流畅、美观",
            "explanation": "iOS设计语言强调简洁性和用户友好性",
            "score": 4
        },
        {
            "type": "single_choice",
            "question": "iOS中的圆角半径通常使用多少像素？",
            "options": ["4px", "8px", "12px", "16px"],
            "correct_answer": "C",
            "explanation": "iOS通常使用12px的圆角半径",
            "score": 2
        }
    ]
    
    # 创建测试考试
    try:
        exam_id = exam_manager.create_exam(
            title="🍎 iOS设计风格测试",
            description="测试iOS风格界面的美观考试",
            questions=test_questions,
            time_limit=15  # 15分钟
        )
        print(f"✅ 创建测试考试成功，ID: {exam_id}")
        
        # 获取考试数据
        exam_data = exam_manager.get_exam_by_id(exam_id)
        if not exam_data:
            print("❌ 无法获取考试数据")
            return
            
        print(f"📊 考试数据: {exam_data['title']}, {len(exam_data['questions'])}题")
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 启动iOS风格考试界面
        print("🚀 启动iOS风格考试界面...")
        ios_window = iOSStyleExamWindow(root, exam_manager, wrong_question_manager, exam_data)
        
        # 运行界面
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ios_interface()
