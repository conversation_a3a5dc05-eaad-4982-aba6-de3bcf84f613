#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试数据库保存功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_save():
    """直接测试保存功能"""
    try:
        from src.utils.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.utils.config_manager import ConfigManager
        
        print("=== 直接保存测试 ===")
        
        # 初始化
        config = ConfigManager()
        db = DatabaseManager(config)
        exam_manager = ExamManager(db)
        
        print("✓ 管理器初始化成功")
        
        # 检查数据库连接
        try:
            result = db.execute_query("SELECT COUNT(*) FROM exams")
            current_count = result[0][0] if result else 0
            print(f"✓ 数据库连接正常，当前考试数量: {current_count}")
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            return False
        
        # 创建测试题目
        test_questions = [
            {
                "type": "single_choice",
                "question": "直接保存测试：Python是什么？",
                "options": ["编程语言", "动物", "食物", "工具"],
                "correct_answer": "A",
                "explanation": "Python是一种编程语言",
                "score": 2
            }
        ]
        
        print(f"准备保存考试，题目数量: {len(test_questions)}")
        
        # 直接调用保存方法
        try:
            exam_id = exam_manager.create_exam(
                title="直接保存测试考试",
                description="用于测试直接保存功能的考试",
                questions=test_questions,
                time_limit=30
            )
            
            print(f"✓ 考试保存成功，ID: {exam_id}")
            
            # 验证保存结果
            saved_exam = exam_manager.get_exam_by_id(exam_id)
            if saved_exam:
                print("✓ 保存验证成功")
                print(f"  标题: {saved_exam['title']}")
                print(f"  题目数量: {len(saved_exam['questions'])}")
            else:
                print("✗ 保存验证失败")
                return False
            
            # 检查考试列表
            all_exams = exam_manager.get_all_exams()
            print(f"✓ 数据库中现在有 {len(all_exams)} 个考试")
            
            for exam in all_exams:
                print(f"  考试: ID={exam[0]}, 标题='{exam[1]}', 时间={exam[3]}分钟")
            
            return True
            
        except Exception as e:
            print(f"✗ 保存失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"✗ 初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_write():
    """测试数据库写入权限"""
    try:
        import sqlite3
        import os
        
        print("\n=== 数据库写入权限测试 ===")
        
        db_path = "data/exam_system.db"
        
        # 检查数据目录
        if not os.path.exists("data"):
            print("创建data目录...")
            os.makedirs("data")
        
        # 检查数据库文件权限
        if os.path.exists(db_path):
            print(f"✓ 数据库文件存在: {db_path}")
            print(f"  文件大小: {os.path.getsize(db_path)} 字节")
            print(f"  可读: {os.access(db_path, os.R_OK)}")
            print(f"  可写: {os.access(db_path, os.W_OK)}")
        else:
            print("数据库文件不存在，将创建新文件")
        
        # 测试直接数据库操作
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 测试写入
        test_time = "2024-01-01 12:00:00"
        cursor.execute(
            "INSERT INTO exams (title, description, time_limit, questions, created_at) VALUES (?, ?, ?, ?, ?)",
            ("数据库测试考试", "测试数据库写入", 60, '[]', test_time)
        )
        
        conn.commit()
        test_id = cursor.lastrowid
        print(f"✓ 直接数据库写入成功，ID: {test_id}")
        
        # 验证写入
        cursor.execute("SELECT * FROM exams WHERE id = ?", (test_id,))
        result = cursor.fetchone()
        if result:
            print(f"✓ 写入验证成功: {result[1]}")
        else:
            print("✗ 写入验证失败")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库写入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始数据库保存功能测试...\n")
    
    # 测试数据库写入权限
    db_test = test_database_write()
    
    if db_test:
        # 测试应用程序保存功能
        app_test = test_direct_save()
        
        if app_test:
            print("\n🎉 所有测试通过！")
            print("现在重新启动考试系统，应该能看到测试考试了。")
        else:
            print("\n❌ 应用程序保存功能有问题")
    else:
        print("\n❌ 数据库写入权限有问题")
