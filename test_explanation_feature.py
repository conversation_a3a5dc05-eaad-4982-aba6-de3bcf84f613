#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_explanation_window():
    """测试解析窗口"""
    try:
        import tkinter as tk
        from src.ui.exam_window import ExplanationWindow
        
        print("=== 测试解析窗口 ===")
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 测试题目数据
        test_question = {
            'type': 'single_choice',
            'question': '测试题目：Python是什么类型的编程语言？',
            'options': ['编译型语言', '解释型语言', '汇编语言', '机器语言'],
            'correct_answer': 'B',
            'explanation': 'Python是一种解释型编程语言，代码在运行时由解释器逐行执行，而不需要事先编译成机器码。这使得Python具有跨平台性和开发效率高的特点。',
            'score': 2
        }
        
        # 模拟用户答案
        user_answer = 'A'  # 错误答案
        
        print("✓ 创建解析窗口...")
        
        # 创建解析窗口
        explanation_window = ExplanationWindow(root, test_question, user_answer, 1)
        
        print("✓ 解析窗口创建成功")
        print("✓ 窗口应该显示：")
        print("  - 题目内容")
        print("  - 选项列表")
        print("  - 答案对比（我的答案 vs 正确答案）")
        print("  - 详细解析")
        print("  - 添加到错题本按钮（因为答错了）")
        
        # 不启动主循环，只是测试创建
        root.after(2000, root.quit)  # 2秒后退出
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ 解析窗口测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_exam_result_features():
    """测试考试结果功能"""
    try:
        from src.ui.exam_window import ExamResultWindow
        from src.core.exam_manager import ExamManager
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database import Database
        
        print("\n=== 测试考试结果功能 ===")
        
        # 初始化数据库和管理器
        db = Database()
        exam_manager = ExamManager(db)
        wrong_question_manager = WrongQuestionManager(db)
        
        print("✓ 管理器初始化成功")
        
        # 检查是否有考试记录
        records = exam_manager.get_all_exam_records()
        print(f"✓ 数据库中的考试记录数量: {len(records)}")
        
        if records:
            print("✓ 发现考试记录，可以测试解析功能")
            for i, record in enumerate(records[:3]):  # 只显示前3个
                print(f"  记录{i+1}: ID={record[0]}, 分数={record[3]}/{record[4]}")
        else:
            print("⚠ 没有考试记录，需要先完成一次考试才能测试解析功能")
        
        return True
        
    except Exception as e:
        print(f"✗ 考试结果功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_test_exam():
    """创建测试考试"""
    try:
        from src.core.exam_manager import ExamManager
        from src.utils.database import Database
        
        print("\n=== 创建测试考试 ===")
        
        db = Database()
        exam_manager = ExamManager(db)
        
        # 创建包含解析的测试题目
        test_questions = [
            {
                'type': 'single_choice',
                'question': 'Python中哪个关键字用于定义函数？',
                'options': ['function', 'def', 'func', 'define'],
                'correct_answer': 'B',
                'explanation': 'Python中使用def关键字来定义函数。语法格式为：def 函数名(参数):',
                'score': 2
            },
            {
                'type': 'true_false',
                'question': 'Python是一种编译型语言。',
                'correct_answer': '错',
                'explanation': 'Python是解释型语言，不是编译型语言。Python代码在运行时由解释器逐行执行。',
                'score': 1
            },
            {
                'type': 'multiple_choice',
                'question': 'Python中哪些是可变数据类型？',
                'options': ['list', 'tuple', 'dict', 'set'],
                'correct_answer': ['A', 'C', 'D'],
                'explanation': 'Python中list（列表）、dict（字典）、set（集合）是可变数据类型，而tuple（元组）是不可变数据类型。',
                'score': 3
            }
        ]
        
        exam_id = exam_manager.create_exam(
            title="解析功能测试考试",
            description="用于测试题目解析功能的考试",
            questions=test_questions,
            time_limit=10
        )
        
        print(f"✓ 测试考试创建成功，ID: {exam_id}")
        print("✓ 现在可以：")
        print("  1. 启动考试系统")
        print("  2. 选择'开始考试'")
        print("  3. 选择'解析功能测试考试'")
        print("  4. 完成考试后查看解析")
        
        return True
        
    except Exception as e:
        print(f"✗ 创建测试考试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试解析功能...\n")
    
    # 测试解析窗口
    window_success = test_explanation_window()
    
    # 测试考试结果功能
    result_success = test_exam_result_features()
    
    # 创建测试考试
    exam_success = create_test_exam()
    
    print("\n=== 测试总结 ===")
    if window_success:
        print("✅ 解析窗口测试通过")
    else:
        print("❌ 解析窗口测试失败")
    
    if result_success:
        print("✅ 考试结果功能测试通过")
    else:
        print("❌ 考试结果功能测试失败")
    
    if exam_success:
        print("✅ 测试考试创建成功")
    else:
        print("❌ 测试考试创建失败")
    
    if window_success and result_success and exam_success:
        print("\n🎉 解析功能修复完成！")
        print("\n现在您可以：")
        print("1. 完成考试后，在结果页面点击'📝 查看解析'")
        print("2. 双击任意题目查看详细解析")
        print("3. 查看答案对比和详细说明")
        print("4. 将错题添加到错题本")
        print("5. 导出考试结果")
        print("6. 重新考试")
    else:
        print("\n⚠️ 部分功能测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
