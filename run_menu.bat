@echo off
chcp 65001 >nul
title 考试系统启动菜单

:menu
cls
echo.
echo ========================================
echo 🎓 智能化考试系统 v2.0 启动菜单
echo ========================================
echo.
echo 请选择启动方式：
echo.
echo 1. 🚀 稳定版启动 (推荐，无启动画面)
echo 2. ⚡ 优化版启动 (包含启动画面)
echo 3. 📱 标准版启动 (基础功能)
echo 4. 🔒 安全模式启动 (处理异常)
echo 5. 🎨 现代UI演示 (灵动风格界面)
echo 6. 🧪 功能测试 (测试智能功能)
echo 7. ❌ 退出
echo.
set /p choice=请输入选项 (1-7): 

if "%choice%"=="1" goto stable
if "%choice%"=="2" goto optimized
if "%choice%"=="3" goto standard
if "%choice%"=="4" goto safe
if "%choice%"=="5" goto modern
if "%choice%"=="6" goto test
if "%choice%"=="7" goto exit

echo 无效选项，请重新选择...
timeout /t 2 >nul
goto menu

:stable
echo.
echo 🚀 启动稳定版本...
echo 特点：无启动画面，快速启动，避免线程问题
echo.
if exist main_stable.py (
    python main_stable.py
) else (
    echo ❌ main_stable.py 文件不存在
    echo 请检查文件是否完整
)
goto end

:optimized
echo.
echo ⚡ 启动优化版本...
echo 特点：包含启动画面和完整功能
echo.
if exist main_optimized.py (
    python main_optimized.py
) else (
    echo ❌ main_optimized.py 文件不存在
    echo 尝试使用标准版本...
    python main.py
)
goto end

:standard
echo.
echo 📱 启动标准版本...
echo 特点：基础功能，兼容性好
echo.
python main.py
goto end

:safe
echo.
echo 🔒 启动安全模式...
echo 特点：异常处理，信号捕获，安全退出
echo.
if exist run_safe.py (
    python run_safe.py
) else (
    echo ❌ run_safe.py 文件不存在
    echo 尝试使用稳定版本...
    if exist main_stable.py (
        python main_stable.py
    ) else (
        python main.py
    )
)
goto end

:modern
echo.
echo 🎨 启动现代UI演示...
echo 特点：灵动风格界面，PyQt6实现
echo.
if exist demo_modern_ui.py (
    python demo_modern_ui.py
) else (
    echo ❌ demo_modern_ui.py 文件不存在
    echo 请确保现代UI文件完整
)
goto end

:test
echo.
echo 🧪 启动功能测试...
echo 特点：测试智能化功能
echo.
if exist test_phase2_intelligence.py (
    python test_phase2_intelligence.py
) else (
    echo ❌ 测试文件不存在
    echo 跳过测试，启动主程序...
    python main.py
)
goto end

:exit
echo.
echo 👋 感谢使用智能化考试系统！
exit /b 0

:end
echo.
if errorlevel 1 (
    echo ❌ 程序运行出错
    echo.
    echo 🔧 故障排除建议：
    echo 1. 检查Python版本 (需要3.7+)
    echo 2. 安装依赖: pip install -r requirements.txt
    echo 3. 检查文件完整性
    echo 4. 尝试其他启动方式
    echo.
) else (
    echo ✅ 程序正常退出
)

echo.
set /p restart=是否返回菜单？(y/n): 
if /i "%restart%"=="y" goto menu
if /i "%restart%"=="yes" goto menu

echo 👋 再见！
pause
