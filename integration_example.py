#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
材料导入系统集成示例
演示如何在其他系统中集成材料导入功能
"""

import sqlite3
import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# 导入材料导入系统模块
from material_import_system.material_import_core import MaterialImporter, MaterialManager
from material_import_system.material_import_ui import MaterialImportDialog, MaterialListWidget
from material_import_system.database_setup import DatabaseSetup
from material_import_system.config import config


class IntegrationExample:
    """集成示例类 - 演示如何在现有系统中集成材料导入功能"""
    
    def __init__(self):
        """初始化集成示例"""
        self.db_connection = None
        self.material_manager = None
        self.setup_database()
        self.setup_ui()
    
    def setup_database(self):
        """设置数据库连接"""
        try:
            # 使用配置文件中的数据库设置
            db_path = config.get_database_url()
            
            # 创建数据库（如果不存在）
            db_setup = DatabaseSetup(db_path)
            self.db_connection = db_setup.create_database()
            
            # 创建材料管理器
            self.material_manager = MaterialManager(self.db_connection)
            
            print(f"数据库连接成功: {db_path}")
            
        except Exception as e:
            print(f"数据库连接失败: {e}")
            messagebox.showerror("错误", f"数据库连接失败: {e}")
    
    def setup_ui(self):
        """设置用户界面"""
        self.root = tk.Tk()
        self.root.title("材料导入系统集成示例")
        self.root.geometry("900x700")
        
        # 创建主菜单
        self.create_menu()
        
        # 创建主界面
        self.create_main_interface()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入材料", command=self.show_import_dialog)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="批量导入", command=self.batch_import)
        tools_menu.add_command(label="统计信息", command=self.show_statistics)
        tools_menu.add_command(label="数据库管理", command=self.database_management)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_main_interface(self):
        """创建主界面"""
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 材料管理标签页
        material_frame = ttk.Frame(notebook)
        notebook.add(material_frame, text="材料管理")
        
        # 在材料管理标签页中添加材料列表组件
        self.material_list = MaterialListWidget(material_frame, self.db_connection)
        
        # 示例标签页
        example_frame = ttk.Frame(notebook)
        notebook.add(example_frame, text="集成示例")
        
        self.create_example_interface(example_frame)
    
    def create_example_interface(self, parent):
        """创建示例界面"""
        # 标题
        title_label = ttk.Label(parent, text="材料导入系统集成示例", font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # 功能演示区域
        demo_frame = ttk.LabelFrame(parent, text="功能演示", padding="10")
        demo_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 核心功能按钮
        ttk.Button(demo_frame, text="导入文本文件", command=self.demo_import_text).pack(side=tk.LEFT, padx=5)
        ttk.Button(demo_frame, text="导入PDF文件", command=self.demo_import_pdf).pack(side=tk.LEFT, padx=5)
        ttk.Button(demo_frame, text="手动添加材料", command=self.demo_manual_input).pack(side=tk.LEFT, padx=5)
        
        # API演示区域
        api_frame = ttk.LabelFrame(parent, text="API调用示例", padding="10")
        api_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 代码示例文本框
        self.code_text = tk.Text(api_frame, height=20, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(api_frame, orient=tk.VERTICAL, command=self.code_text.yview)
        self.code_text.configure(yscrollcommand=scrollbar.set)
        
        self.code_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 插入示例代码
        self.insert_example_code()
    
    def insert_example_code(self):
        """插入示例代码"""
        example_code = '''
# 材料导入系统API使用示例

# 1. 基本导入功能
from material_import_core import MaterialImporter, MaterialManager
import sqlite3

# 创建数据库连接
conn = sqlite3.connect("materials.db")

# 创建导入器
importer = MaterialImporter(conn)

# 导入文本文件
result = importer.import_text_file("example.txt")
if result['success']:
    print(f"导入成功: {result['title']}")
else:
    print(f"导入失败: {result['error']}")

# 导入PDF文件
result = importer.import_pdf_file("document.pdf")
if result['success']:
    print(f"PDF导入成功，页数: {result['page_count']}")

# 手动添加材料
result = importer.import_from_text("标题", "内容")

# 2. 材料管理功能
manager = MaterialManager(conn)

# 获取所有材料
materials = manager.get_all_materials()
for material in materials:
    print(f"ID: {material['id']}, 标题: {material['title']}")

# 搜索材料
results = manager.search_materials("Python")

# 获取材料详情
material = manager.get_material_by_id(1)
if material:
    print(f"内容: {material['content'][:100]}...")

# 更新材料
manager.update_material(1, "新标题", "新内容")

# 删除材料
manager.delete_material(1)

# 获取统计信息
stats = manager.get_material_stats()
print(f"总材料数: {stats['total_count']}")

# 3. 在GUI中集成
import tkinter as tk
from material_import_ui import MaterialImportDialog, MaterialListWidget

root = tk.Tk()

# 显示导入对话框
def show_import():
    MaterialImportDialog(root, conn, callback=refresh_list)

# 创建材料列表组件
material_list = MaterialListWidget(root, conn)

# 4. 配置系统
from config import config

# 检查文件是否支持
if config.is_supported_file("test.pdf"):
    print("支持PDF文件")

# 获取文件类型
file_type = config.get_file_type("document.txt")
print(f"文件类型: {file_type}")

# 验证文件大小
if config.validate_file_size("large_file.pdf"):
    print("文件大小符合要求")

# 5. 错误处理
try:
    result = importer.import_text_file("nonexistent.txt")
    if not result['success']:
        print(f"导入失败: {result['error']}")
except Exception as e:
    print(f"发生异常: {e}")

conn.close()
'''
        self.code_text.insert(1.0, example_code)
        self.code_text.config(state=tk.DISABLED)
    
    def show_import_dialog(self):
        """显示导入对话框"""
        if self.db_connection:
            MaterialImportDialog(self.root, self.db_connection, self.refresh_material_list)
        else:
            messagebox.showerror("错误", "数据库连接未建立！")
    
    def refresh_material_list(self):
        """刷新材料列表"""
        if hasattr(self, 'material_list'):
            self.material_list.refresh_list()
    
    def demo_import_text(self):
        """演示文本文件导入"""
        from tkinter import filedialog
        
        file_path = filedialog.askopenfilename(
            title="选择文本文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path and self.material_manager:
            try:
                importer = MaterialImporter(self.db_connection)
                result = importer.import_text_file(file_path)
                
                if result['success']:
                    messagebox.showinfo("成功", f"文本文件导入成功！\n标题: {result['title']}\n大小: {result['file_size']} 字符")
                    self.refresh_material_list()
                else:
                    messagebox.showerror("错误", result['error'])
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {e}")
    
    def demo_import_pdf(self):
        """演示PDF文件导入"""
        from tkinter import filedialog
        
        file_path = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path and self.material_manager:
            try:
                importer = MaterialImporter(self.db_connection)
                result = importer.import_pdf_file(file_path)
                
                if result['success']:
                    messagebox.showinfo("成功", f"PDF文件导入成功！\n标题: {result['title']}\n页数: {result['page_count']}\n大小: {result['file_size']} 字符")
                    self.refresh_material_list()
                else:
                    messagebox.showerror("错误", result['error'])
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {e}")
    
    def demo_manual_input(self):
        """演示手动输入"""
        # 创建简单的输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("手动添加材料")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 标题输入
        ttk.Label(dialog, text="标题:").pack(pady=5)
        title_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=title_var, width=50).pack(pady=5)
        
        # 内容输入
        ttk.Label(dialog, text="内容:").pack(pady=5)
        content_text = tk.Text(dialog, width=50, height=10)
        content_text.pack(pady=5, padx=10, fill=tk.BOTH, expand=True)
        
        # 按钮
        def save_material():
            title = title_var.get().strip()
            content = content_text.get(1.0, tk.END).strip()
            
            if not title or not content:
                messagebox.showwarning("警告", "请输入标题和内容！")
                return
            
            try:
                importer = MaterialImporter(self.db_connection)
                result = importer.import_from_text(title, content)
                
                if result['success']:
                    messagebox.showinfo("成功", "材料添加成功！")
                    self.refresh_material_list()
                    dialog.destroy()
                else:
                    messagebox.showerror("错误", result['error'])
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
        
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="保存", command=save_material).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
    
    def batch_import(self):
        """批量导入功能"""
        from tkinter import filedialog
        
        file_paths = filedialog.askopenfilenames(
            title="选择要批量导入的文件",
            filetypes=[
                ("支持的文件", "*.txt;*.pdf"),
                ("文本文件", "*.txt"),
                ("PDF文件", "*.pdf"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_paths and self.material_manager:
            success_count = 0
            error_count = 0
            errors = []
            
            importer = MaterialImporter(self.db_connection)
            
            for file_path in file_paths:
                try:
                    if file_path.lower().endswith('.pdf'):
                        result = importer.import_pdf_file(file_path)
                    else:
                        result = importer.import_text_file(file_path)
                    
                    if result['success']:
                        success_count += 1
                    else:
                        error_count += 1
                        errors.append(f"{os.path.basename(file_path)}: {result['error']}")
                        
                except Exception as e:
                    error_count += 1
                    errors.append(f"{os.path.basename(file_path)}: {str(e)}")
            
            # 显示结果
            message = f"批量导入完成！\n成功: {success_count} 个文件\n失败: {error_count} 个文件"
            if errors:
                message += "\n\n错误详情:\n" + "\n".join(errors[:5])  # 只显示前5个错误
                if len(errors) > 5:
                    message += f"\n... 还有 {len(errors) - 5} 个错误"
            
            if error_count == 0:
                messagebox.showinfo("成功", message)
            else:
                messagebox.showwarning("部分成功", message)
            
            self.refresh_material_list()
    
    def show_statistics(self):
        """显示统计信息"""
        if self.material_manager:
            try:
                stats = self.material_manager.get_material_stats()
                
                message = f"材料统计信息:\n\n"
                message += f"总材料数: {stats['total_count']}\n\n"
                message += "按类型分布:\n"
                
                for file_type, count in stats['by_type'].items():
                    message += f"  {file_type}: {count} 个\n"
                
                messagebox.showinfo("统计信息", message)
                
            except Exception as e:
                messagebox.showerror("错误", f"获取统计信息失败: {e}")
    
    def database_management(self):
        """数据库管理"""
        # 这里可以添加数据库管理功能，如备份、恢复等
        messagebox.showinfo("数据库管理", "数据库管理功能开发中...")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
材料导入系统使用说明:

1. 导入材料:
   - 点击"文件"菜单 -> "导入材料"
   - 或在材料管理页面点击"导入材料"按钮
   - 支持文本文件(.txt)和PDF文件(.pdf)

2. 管理材料:
   - 在材料管理页面查看所有材料
   - 使用搜索框查找特定材料
   - 双击材料可查看详情和编辑
   - 选中材料后点击删除按钮可删除

3. 批量操作:
   - 使用"工具"菜单中的"批量导入"功能
   - 可同时选择多个文件进行导入

4. 统计信息:
   - 查看材料总数和类型分布
   - 了解系统使用情况

5. API集成:
   - 参考"集成示例"标签页中的代码
   - 可在其他系统中集成材料导入功能
        """
        
        # 创建帮助窗口
        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("500x400")
        help_window.transient(self.root)
        
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
材料导入系统 v1.0

一个功能完整的学习材料导入和管理系统

主要功能:
• 支持文本和PDF文件导入
• 材料搜索和管理
• 批量导入操作
• 统计信息查看
• API接口支持

技术栈:
• Python 3.7+
• tkinter (GUI)
• SQLite (数据库)
• PyPDF2 (PDF处理)

开发目的:
为其他系统提供可集成的材料导入功能模块
        """
        messagebox.showinfo("关于", about_text)
    
    def on_closing(self):
        """关闭程序时的处理"""
        if self.db_connection:
            self.db_connection.close()
        self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    print("启动材料导入系统集成示例...")
    
    # 检查依赖
    try:
        import PyPDF2
        print("✓ PyPDF2 已安装")
    except ImportError:
        print("✗ PyPDF2 未安装，请运行: pip install PyPDF2")
        return
    
    # 创建并运行应用
    app = IntegrationExample()
    app.run()


if __name__ == "__main__":
    main()
