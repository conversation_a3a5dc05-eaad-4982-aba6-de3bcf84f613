#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的系统
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试模块导入"""
    try:
        print("🔍 测试模块导入...")
        
        # 测试核心模块
        from src.core.database_manager import DatabaseManager
        print("✅ DatabaseManager 导入成功")
        
        from src.core.exam_manager import ExamManager
        print("✅ ExamManager 导入成功")
        
        from src.core.wrong_question_manager import WrongQuestionManager
        print("✅ WrongQuestionManager 导入成功")
        
        # 测试UI模块
        from src.ui.main_window import MainWindow
        print("✅ MainWindow 导入成功")
        
        from src.ui.advanced_wrong_questions_window import AdvancedWrongQuestionsWindow
        print("✅ AdvancedWrongQuestionsWindow 导入成功")
        
        from src.ui.enhanced_exam_history_window import EnhancedExamHistoryWindow
        print("✅ EnhancedExamHistoryWindow 导入成功")
        
        print("🎉 所有模块导入成功！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_wrong_questions_window():
    """测试错题本窗口初始化"""
    try:
        print("\n🔍 测试错题本窗口初始化...")
        
        from src.core.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.ui.advanced_wrong_questions_window import AdvancedWrongQuestionsWindow
        
        # 创建管理器实例
        db = DatabaseManager()
        exam_manager = ExamManager(db)
        wrong_question_manager = WrongQuestionManager(db)
        
        # 测试窗口初始化（不显示）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        window = AdvancedWrongQuestionsWindow(root, wrong_question_manager, exam_manager)
        print("✅ AdvancedWrongQuestionsWindow 初始化成功")
        
        # 测试方法是否存在
        if hasattr(window, 'switch_to_favorites_view'):
            print("✅ switch_to_favorites_view 方法存在")
        else:
            print("❌ switch_to_favorites_view 方法不存在")
            
        if hasattr(window, 'load_favorite_questions'):
            print("✅ load_favorite_questions 方法存在")
        else:
            print("❌ load_favorite_questions 方法不存在")
        
        root.destroy()
        print("🎉 错题本窗口测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 错题本窗口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window():
    """测试主窗口初始化"""
    try:
        print("\n🔍 测试主窗口初始化...")
        
        from src.ui.main_window import MainWindow
        
        # 测试主窗口初始化（不显示）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        app = MainWindow(root)
        print("✅ MainWindow 初始化成功")
        
        # 测试方法是否存在
        if hasattr(app, 'open_wrong_questions'):
            print("✅ open_wrong_questions 方法存在")
        else:
            print("❌ open_wrong_questions 方法不存在")
            
        if hasattr(app, 'open_favorites'):
            print("✅ open_favorites 方法存在")
        else:
            print("❌ open_favorites 方法不存在")
        
        root.destroy()
        print("🎉 主窗口测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 主窗口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始系统修复测试...\n")
    
    success_count = 0
    total_tests = 3
    
    # 测试模块导入
    if test_imports():
        success_count += 1
    
    # 测试错题本窗口
    if test_wrong_questions_window():
        success_count += 1
    
    # 测试主窗口
    if test_main_window():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！系统修复成功！")
        print("\n✅ 现在可以安全地启动主程序了：")
        print("   python main.py")
    else:
        print("❌ 部分测试失败，需要进一步修复")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()
