#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业API配置界面
类似于专业API管理工具的界面设计
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import requests
from typing import Dict, List, Optional

class ProfessionalAPIConfig:
    def __init__(self, parent, config_manager):
        self.parent = parent
        self.config = config_manager
        self.window = None
        
        # API配置数据
        self.api_configs = {
            'openai': {
                'name': 'OpenAI',
                'base_url': 'https://api.openai.com/v1',
                'models': ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o'],
                'icon': '🤖'
            },
            'relay': {
                'name': '中转API',
                'base_url': 'http://14.103.135.19:3000/v1',
                'models': ['gemini-2.5-flash', 'gpt-3.5-turbo', 'gpt-4'],
                'icon': '🔄'
            },
            'deepseek': {
                'name': 'DeepSeek',
                'base_url': 'https://api.deepseek.com/v1',
                'models': ['deepseek-chat', 'deepseek-coder'],
                'icon': '🧠'
            },
            'qwen': {
                'name': '通义千问',
                'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1',
                'models': ['qwen-turbo', 'qwen-plus', 'qwen-max'],
                'icon': '🌟'
            }
        }
        
        self.current_config = 'relay'  # 默认选择中转API
        
    def show(self):
        """显示专业API配置窗口"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("API连接配置")
        self.window.geometry("900x700")
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 设置深色主题样式
        self.setup_dark_theme()
        
        # 创建主界面
        self.create_main_interface()
        
        # 加载当前配置
        self.load_current_config()
        
    def setup_dark_theme(self):
        """设置深色主题"""
        style = ttk.Style()
        
        # 配置深色主题
        self.window.configure(bg='#2b2b2b')
        
        # 自定义样式
        style.configure('Dark.TFrame', background='#2b2b2b')
        style.configure('Dark.TLabel', background='#2b2b2b', foreground='#ffffff')
        style.configure('Dark.TButton', background='#404040', foreground='#ffffff')
        style.configure('Dark.TEntry', fieldbackground='#404040', foreground='#ffffff')
        style.configure('Dark.TCombobox', fieldbackground='#404040', foreground='#ffffff')
        
    def create_main_interface(self):
        """创建主界面"""
        # 左侧API选择面板
        self.create_left_panel()
        
        # 右侧配置面板
        self.create_right_panel()
        
        # 底部按钮面板
        self.create_bottom_panel()
        
    def create_left_panel(self):
        """创建左侧API选择面板"""
        left_frame = tk.Frame(self.window, bg='#1e1e1e', width=200)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(10, 5), pady=10)
        left_frame.pack_propagate(False)
        
        # 标题
        title_label = tk.Label(left_frame, text="API连接配置", 
                              font=('Microsoft YaHei', 14, 'bold'),
                              bg='#1e1e1e', fg='#ffffff')
        title_label.pack(pady=(10, 20))
        
        # API选择按钮
        self.api_buttons = {}
        for api_key, api_info in self.api_configs.items():
            btn_frame = tk.Frame(left_frame, bg='#1e1e1e')
            btn_frame.pack(fill=tk.X, pady=2)
            
            btn = tk.Button(btn_frame, 
                           text=f"{api_info['icon']} {api_info['name']}",
                           font=('Microsoft YaHei', 10),
                           bg='#404040', fg='#ffffff',
                           activebackground='#505050',
                           activeforeground='#ffffff',
                           border=0, relief='flat',
                           command=lambda k=api_key: self.select_api(k))
            btn.pack(fill=tk.X, ipady=8)
            
            self.api_buttons[api_key] = btn
            
        # 选中当前API
        self.select_api(self.current_config)
        
    def create_right_panel(self):
        """创建右侧配置面板"""
        right_frame = tk.Frame(self.window, bg='#2b2b2b')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 10), pady=10)
        
        # 滚动区域
        canvas = tk.Canvas(right_frame, bg='#2b2b2b', highlightthickness=0)
        scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#2b2b2b')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.config_frame = scrollable_frame
        
        # 创建配置表单
        self.create_config_form()
        
    def create_config_form(self):
        """创建配置表单"""
        # 清空现有内容
        for widget in self.config_frame.winfo_children():
            widget.destroy()
            
        # API名称显示
        api_info = self.api_configs[self.current_config]
        
        # 标题区域
        title_frame = tk.Frame(self.config_frame, bg='#2b2b2b')
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        icon_label = tk.Label(title_frame, text=api_info['icon'], 
                             font=('Microsoft YaHei', 24),
                             bg='#2b2b2b', fg='#ffffff')
        icon_label.pack(side=tk.LEFT)
        
        name_label = tk.Label(title_frame, text=api_info['name'], 
                             font=('Microsoft YaHei', 16, 'bold'),
                             bg='#2b2b2b', fg='#ffffff')
        name_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 连接状态指示器
        self.status_frame = tk.Frame(title_frame, bg='#2b2b2b')
        self.status_frame.pack(side=tk.RIGHT)
        
        self.status_indicator = tk.Label(self.status_frame, text="●", 
                                        font=('Microsoft YaHei', 12),
                                        bg='#2b2b2b', fg='#ff6b6b')
        self.status_indicator.pack(side=tk.LEFT)
        
        self.status_label = tk.Label(self.status_frame, text="未连接", 
                                    font=('Microsoft YaHei', 10),
                                    bg='#2b2b2b', fg='#ffffff')
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 配置表单
        self.create_form_fields()
        
        # 操作按钮
        self.create_action_buttons()
        
    def create_form_fields(self):
        """创建表单字段"""
        # API Key
        self.create_field("API Key", "api_key", is_password=True)
        
        # Base URL
        self.create_field("Base URL", "base_url")
        
        # 模型选择
        self.create_model_field()
        
        # 自定义模型
        self.create_custom_model_field()
        
    def create_field(self, label_text, field_name, is_password=False):
        """创建表单字段"""
        field_frame = tk.Frame(self.config_frame, bg='#2b2b2b')
        field_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 标签
        label = tk.Label(field_frame, text=label_text, 
                        font=('Microsoft YaHei', 10, 'bold'),
                        bg='#2b2b2b', fg='#ffffff')
        label.pack(anchor=tk.W)
        
        # 输入框
        if is_password:
            entry = tk.Entry(field_frame, font=('Consolas', 10),
                           bg='#404040', fg='#ffffff', 
                           insertbackground='#ffffff',
                           show='*', relief='flat', bd=5)
        else:
            entry = tk.Entry(field_frame, font=('Consolas', 10),
                           bg='#404040', fg='#ffffff', 
                           insertbackground='#ffffff',
                           relief='flat', bd=5)
        
        entry.pack(fill=tk.X, ipady=8, pady=(5, 0))
        
        # 保存引用
        setattr(self, f"{field_name}_var", tk.StringVar())
        entry.config(textvariable=getattr(self, f"{field_name}_var"))
        setattr(self, f"{field_name}_entry", entry)
        
    def create_model_field(self):
        """创建模型选择字段"""
        field_frame = tk.Frame(self.config_frame, bg='#2b2b2b')
        field_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 标签
        label = tk.Label(field_frame, text="可用模型", 
                        font=('Microsoft YaHei', 10, 'bold'),
                        bg='#2b2b2b', fg='#ffffff')
        label.pack(anchor=tk.W)
        
        # 下拉框
        self.model_var = tk.StringVar()
        model_combo = ttk.Combobox(field_frame, textvariable=self.model_var,
                                  font=('Consolas', 10), state='readonly')
        model_combo.pack(fill=tk.X, ipady=8, pady=(5, 0))
        
        # 设置模型列表
        api_info = self.api_configs[self.current_config]
        model_combo['values'] = api_info['models']
        if api_info['models']:
            model_combo.set(api_info['models'][0])
            
        self.model_combo = model_combo
        
    def create_custom_model_field(self):
        """创建自定义模型字段"""
        field_frame = tk.Frame(self.config_frame, bg='#2b2b2b')
        field_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 标签
        label = tk.Label(field_frame, text="输入模型名", 
                        font=('Microsoft YaHei', 10, 'bold'),
                        bg='#2b2b2b', fg='#ffffff')
        label.pack(anchor=tk.W)
        
        # 输入框和按钮的容器
        input_frame = tk.Frame(field_frame, bg='#2b2b2b')
        input_frame.pack(fill=tk.X, pady=(5, 0))
        
        # 输入框
        self.custom_model_var = tk.StringVar()
        custom_entry = tk.Entry(input_frame, textvariable=self.custom_model_var,
                               font=('Consolas', 10),
                               bg='#404040', fg='#ffffff', 
                               insertbackground='#ffffff',
                               relief='flat', bd=5)
        custom_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=8)
        
        # 添加按钮
        add_btn = tk.Button(input_frame, text="添加", 
                           font=('Microsoft YaHei', 9),
                           bg='#4CAF50', fg='#ffffff',
                           activebackground='#45a049',
                           border=0, relief='flat',
                           command=self.add_custom_model)
        add_btn.pack(side=tk.RIGHT, padx=(10, 0), ipady=8, ipadx=15)
        
    def create_action_buttons(self):
        """创建操作按钮"""
        action_frame = tk.Frame(self.config_frame, bg='#2b2b2b')
        action_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 按钮样式
        btn_style = {
            'font': ('Microsoft YaHei', 10),
            'border': 0,
            'relief': 'flat',
            'cursor': 'hand2'
        }
        
        # 连接测试按钮
        test_btn = tk.Button(action_frame, text="🔗 连接", 
                            bg='#2196F3', fg='#ffffff',
                            activebackground='#1976D2',
                            command=self.test_connection,
                            **btn_style)
        test_btn.pack(side=tk.LEFT, ipady=10, ipadx=20)
        
        # 获取参数按钮
        params_btn = tk.Button(action_frame, text="📋 附加参数", 
                              bg='#FF9800', fg='#ffffff',
                              activebackground='#F57C00',
                              command=self.show_params,
                              **btn_style)
        params_btn.pack(side=tk.LEFT, padx=(10, 0), ipady=10, ipadx=20)
        
        # 发送测试消息按钮
        send_btn = tk.Button(action_frame, text="📤 发送测试消息", 
                            bg='#4CAF50', fg='#ffffff',
                            activebackground='#388E3C',
                            command=self.send_test_message,
                            **btn_style)
        send_btn.pack(side=tk.LEFT, padx=(10, 0), ipady=10, ipadx=20)
        
    def create_bottom_panel(self):
        """创建底部按钮面板"""
        bottom_frame = tk.Frame(self.window, bg='#2b2b2b')
        bottom_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(0, 10))
        
        # 自动连接复选框
        self.auto_connect_var = tk.BooleanVar(value=True)
        auto_check = tk.Checkbutton(bottom_frame, text="自动连接到上次的服务器",
                                   variable=self.auto_connect_var,
                                   font=('Microsoft YaHei', 10),
                                   bg='#2b2b2b', fg='#ffffff',
                                   selectcolor='#404040',
                                   activebackground='#2b2b2b',
                                   activeforeground='#ffffff')
        auto_check.pack(side=tk.LEFT)
        
        # 右侧按钮
        btn_frame = tk.Frame(bottom_frame, bg='#2b2b2b')
        btn_frame.pack(side=tk.RIGHT)
        
        # 查看隐藏API密钥按钮
        view_key_btn = tk.Button(btn_frame, text="查看隐藏的API密钥",
                                font=('Microsoft YaHei', 9),
                                bg='#6c757d', fg='#ffffff',
                                activebackground='#5a6268',
                                border=0, relief='flat',
                                command=self.toggle_api_key_visibility)
        view_key_btn.pack(side=tk.RIGHT, padx=(10, 0), ipady=5, ipadx=15)
        
    def select_api(self, api_key):
        """选择API"""
        # 更新按钮样式
        for key, btn in self.api_buttons.items():
            if key == api_key:
                btn.config(bg='#0d7377', activebackground='#14a085')
            else:
                btn.config(bg='#404040', activebackground='#505050')
        
        self.current_config = api_key
        
        # 重新创建配置表单
        if hasattr(self, 'config_frame'):
            self.create_config_form()
            
    def load_current_config(self):
        """加载当前配置"""
        try:
            api_config = self.config.get_api_config()
            
            if self.current_config == 'relay':
                self.api_key_var.set(api_config.get('relay_api_key', ''))
                self.base_url_var.set(api_config.get('relay_base_url', ''))
                self.model_var.set(api_config.get('relay_model', ''))
            elif self.current_config == 'openai':
                self.api_key_var.set(api_config.get('openai_api_key', ''))
                self.base_url_var.set(api_config.get('openai_base_url', ''))
                self.model_var.set(api_config.get('openai_model', ''))
            # 可以添加更多API的配置加载
                
        except Exception as e:
            print(f"加载配置失败: {e}")
            
    def add_custom_model(self):
        """添加自定义模型"""
        model_name = self.custom_model_var.get().strip()
        if not model_name:
            messagebox.showwarning("警告", "请输入模型名称")
            return
            
        # 添加到下拉列表
        current_values = list(self.model_combo['values'])
        if model_name not in current_values:
            current_values.append(model_name)
            self.model_combo['values'] = current_values
            self.model_combo.set(model_name)
            self.custom_model_var.set('')
            messagebox.showinfo("成功", f"已添加模型: {model_name}")
        else:
            messagebox.showinfo("提示", "模型已存在")
            
    def test_connection(self):
        """测试连接"""
        self.update_status("连接中...", "#FFA500")
        
        try:
            # 这里调用实际的连接测试逻辑
            api_key = self.api_key_var.get().strip()
            base_url = self.base_url_var.get().strip()
            model = self.model_var.get().strip()
            
            if not all([api_key, base_url, model]):
                self.update_status("配置不完整", "#ff6b6b")
                messagebox.showwarning("警告", "请完整填写API Key、Base URL和模型")
                return
                
            # 模拟连接测试（这里应该调用实际的API测试）
            self.window.after(1000, lambda: self.update_status("已连接", "#4CAF50"))
            messagebox.showinfo("成功", "连接测试成功！")
            
        except Exception as e:
            self.update_status("连接失败", "#ff6b6b")
            messagebox.showerror("错误", f"连接测试失败: {str(e)}")
            
    def update_status(self, text, color):
        """更新连接状态"""
        self.status_indicator.config(fg=color)
        self.status_label.config(text=text)
        
    def show_params(self):
        """显示附加参数"""
        messagebox.showinfo("附加参数", "附加参数功能开发中...")
        
    def send_test_message(self):
        """发送测试消息"""
        messagebox.showinfo("测试消息", "发送测试消息功能开发中...")
        
    def toggle_api_key_visibility(self):
        """切换API Key可见性"""
        current_show = self.api_key_entry.cget('show')
        if current_show == '*':
            self.api_key_entry.config(show='')
        else:
            self.api_key_entry.config(show='*')
