#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复脚本 - 解决常见问题
"""

import sys
import subprocess
import os

def run_command(command):
    """运行命令"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_pyqt6():
    """安装PyQt6"""
    print("🎨 正在安装PyQt6...")
    success, stdout, stderr = run_command("pip install PyQt6")
    
    if success:
        print("✅ PyQt6 安装成功！")
        return True
    else:
        print("❌ PyQt6 安装失败")
        print(f"错误信息: {stderr}")
        
        # 尝试使用国内镜像
        print("🔄 尝试使用国内镜像...")
        success2, stdout2, stderr2 = run_command("pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyQt6")
        
        if success2:
            print("✅ 使用国内镜像安装PyQt6成功！")
            return True
        else:
            print("❌ 使用国内镜像也安装失败")
            return False

def check_and_fix():
    """检查并修复问题"""
    print("🔍 检查系统状态...")
    
    # 检查PyQt6
    try:
        import PyQt6
        print("✅ PyQt6 已安装")
        pyqt6_available = True
    except ImportError:
        print("❌ PyQt6 未安装")
        pyqt6_available = False
    
    # 检查其他依赖
    missing_packages = []
    
    packages_to_check = {
        'requests': '网络请求库',
        'numpy': '数值计算库',
        'matplotlib': '图表绘制库'
    }
    
    for package, description in packages_to_check.items():
        try:
            __import__(package)
            print(f"✅ {package}: {description}")
        except ImportError:
            print(f"❌ {package}: {description}")
            missing_packages.append(package)
    
    return pyqt6_available, missing_packages

def main():
    """主函数"""
    print("🚀 智能化考试系统 - 快速修复工具")
    print("=" * 50)
    
    # 检查问题
    pyqt6_available, missing_packages = check_and_fix()
    
    if pyqt6_available and not missing_packages:
        print("\n🎉 系统状态良好！")
        print("所有依赖都已安装，可以使用所有功能。")
        
        print("\n🚀 推荐启动方式:")
        print("• 现代UI: python demo_modern_ui.py")
        print("• 稳定版: python main_stable.py")
        print("• 启动菜单: run_menu.bat")
        
        input("\n按回车键退出...")
        return
    
    # 需要修复
    print("\n🔧 发现问题，开始修复...")
    
    if not pyqt6_available:
        print("\n📦 修复PyQt6问题...")
        choice = input("是否安装PyQt6以支持现代UI？(y/n): ").lower()
        
        if choice in ['y', 'yes', '是']:
            if install_pyqt6():
                pyqt6_available = True
            else:
                print("⚠️ PyQt6安装失败，现代UI功能将不可用")
        else:
            print("⚠️ 跳过PyQt6安装，现代UI功能将不可用")
    
    if missing_packages:
        print(f"\n📦 发现缺失的包: {', '.join(missing_packages)}")
        choice = input("是否安装这些包？(y/n): ").lower()
        
        if choice in ['y', 'yes', '是']:
            for package in missing_packages:
                print(f"安装 {package}...")
                success, _, _ = run_command(f"pip install {package}")
                if success:
                    print(f"✅ {package} 安装成功")
                else:
                    print(f"❌ {package} 安装失败")
    
    # 最终检查
    print("\n🔍 最终检查...")
    pyqt6_final, missing_final = check_and_fix()
    
    print("\n📊 修复结果:")
    if pyqt6_final:
        print("✅ PyQt6: 现代UI功能可用")
    else:
        print("❌ PyQt6: 现代UI功能不可用")
    
    if not missing_final:
        print("✅ 所有基础依赖都已安装")
    else:
        print(f"❌ 仍缺失: {', '.join(missing_final)}")
    
    # 推荐启动方式
    print("\n🚀 推荐启动方式:")
    
    if pyqt6_final:
        print("1. 🎨 现代UI (推荐): python demo_modern_ui.py")
        print("2. 🚀 稳定版: python main_stable.py")
        print("3. 📋 启动菜单: run_menu.bat")
    else:
        print("1. 🚀 稳定版 (推荐): python main_stable.py")
        print("2. 📋 启动菜单: run_menu.bat")
        print("3. 🔧 安全模式: python run_safe.py")
        print("\n💡 要体验现代UI，请先安装PyQt6:")
        print("   pip install PyQt6")
    
    print("\n🎯 快速启动命令:")
    if os.path.exists('main_stable.py'):
        print("   python main_stable.py")
    else:
        print("   python main.py")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
