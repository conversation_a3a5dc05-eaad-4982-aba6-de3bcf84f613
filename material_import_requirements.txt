# 材料导入系统依赖包
# Python 3.7+ 兼容

# 核心依赖 - 必需
PyPDF2>=3.0.0

# 可选依赖 - 根据需要安装

# 更强大的PDF处理
# PyMuPDF>=1.20.0
# pdfplumber>=0.7.0

# 支持更多文档格式
# python-docx>=0.8.11  # Word文档
# openpyxl>=3.0.0      # Excel文档
# python-pptx>=0.6.0   # PowerPoint文档

# 文本处理和NLP
# jieba>=0.42.1        # 中文分词
# nltk>=3.7            # 英文文本处理
# textstat>=0.7.0      # 文本统计

# GUI增强
# ttkthemes>=3.2.0     # 主题支持
# tkinter-tooltip>=2.0.0  # 工具提示

# 数据库增强
# sqlalchemy>=1.4.0    # ORM支持
# pymongo>=4.0.0       # MongoDB支持
# psycopg2>=2.9.0      # PostgreSQL支持

# 开发和测试
# pytest>=7.0.0
# pytest-cov>=4.0.0
# black>=22.0.0
# flake8>=5.0.0
