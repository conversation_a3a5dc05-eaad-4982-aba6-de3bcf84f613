# 中转API连接失败故障排除指南

## 🚨 您遇到的问题

根据截图显示，您的中转API配置为：
- **Base URL**: `http://14.103.135.19:3000/v1`
- **Model**: `gemini-2.5-flash`
- **错误**: "RELAY API连接测试失败！"

## 🔍 快速诊断步骤

### 1. 使用新的网络诊断工具

现在系统已经添加了专门的网络诊断功能：

1. 打开**系统设置** → **API设置** → **中转API**
2. 点击**"网络诊断"**按钮（新增功能）
3. 在弹出窗口中点击**"开始诊断"**
4. 查看详细的诊断报告

### 2. 手动检查基础连接

**检查服务器是否可访问**：
```bash
# 在命令行中测试
ping 14.103.135.19
telnet 14.103.135.19 3000
```

**使用curl测试API**：
```bash
curl -v http://14.103.135.19:3000/v1/models \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### 3. 常见问题排查

#### 问题1: 服务器未启动
**症状**: 连接超时或拒绝连接
**解决方案**:
- 确认中转服务正在运行
- 检查服务器端口3000是否开放
- 查看服务器防火墙设置

#### 问题2: API Key无效
**症状**: 401 Unauthorized错误
**解决方案**:
- 检查API Key是否正确
- 确认API Key是否有效期内
- 验证API Key权限

#### 问题3: 模型名称错误
**症状**: 400 Bad Request或模型不存在错误
**解决方案**:
- 确认模型名称拼写正确
- 检查该模型是否在中转服务中可用
- 尝试使用其他模型测试

#### 问题4: 网络连接问题
**症状**: 连接超时
**解决方案**:
- 检查本地网络连接
- 确认防火墙设置
- 尝试使用VPN或更换网络

### 4. 配置建议

#### 推荐的模型名称
根据您使用的中转服务，尝试这些常用模型：

**如果是OpenAI兼容**:
- `gpt-3.5-turbo`
- `gpt-4`
- `gpt-4-turbo`

**如果是通义千问**:
- `qwen-turbo`
- `qwen-plus`
- `qwen-max`

**如果是其他服务**:
- `claude-3-haiku`
- `gemini-pro`
- `deepseek-chat`

#### URL格式检查
确保Base URL格式正确：
- ✅ `http://14.103.135.19:3000/v1`
- ❌ `http://14.103.135.19:3000/v1/`（末尾不要斜杠）
- ❌ `http://14.103.135.19:3000`（缺少/v1）

### 5. 使用自定义模型功能

如果API连接正常但模型列表获取失败，您可以：

1. 手动添加模型名称：
   - 在"自定义模型"框中输入正确的模型名
   - 点击"添加到列表"

2. 使用预设模型：
   - 点击"管理模型"
   - 点击"添加预设模型"
   - 选择合适的模型进行测试

### 6. 调试信息查看

现在系统提供了详细的调试信息：
- 查看控制台输出
- 使用网络诊断工具
- 检查详细的错误消息

## 🛠️ 立即行动

1. **首先**: 点击"网络诊断"按钮，获取详细诊断报告
2. **然后**: 根据诊断结果修复发现的问题
3. **最后**: 重新测试连接

## 📞 需要帮助？

如果问题仍然存在，请提供：
1. 网络诊断的完整输出
2. 中转服务的类型和版本
3. 服务器端的错误日志
4. 网络环境信息

这样我可以提供更精确的解决方案！
