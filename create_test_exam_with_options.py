#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建带选项的测试考试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_exam():
    """创建测试考试"""
    try:
        from src.core.exam_manager import ExamManager
        from src.utils.database import Database
        
        print("=== 创建带选项的测试考试 ===")
        
        db = Database()
        exam_manager = ExamManager(db)
        
        # 创建包含完整选项的测试题目
        test_questions = [
            {
                'type': 'single_choice',
                'question': '测试题目1：Python中哪个关键字用于定义函数？',
                'options': ['function', 'def', 'func', 'define'],
                'correct_answer': 'B',
                'explanation': 'Python中使用def关键字来定义函数。',
                'score': 2
            },
            {
                'type': 'single_choice',
                'question': '测试题目2：Python是什么类型的编程语言？',
                'options': ['编译型语言', '解释型语言', '汇编语言', '机器语言'],
                'correct_answer': 'B',
                'explanation': 'Python是一种解释型编程语言。',
                'score': 2
            },
            {
                'type': 'multiple_choice',
                'question': '测试题目3：Python的主要特点包括哪些？',
                'options': ['简洁明了的语法', '强大的标准库', '跨平台兼容性', '静态类型系统'],
                'correct_answer': ['A', 'B', 'C'],
                'explanation': 'Python具有简洁语法、强大标准库和跨平台兼容性，但它是动态类型系统。',
                'score': 3
            },
            {
                'type': 'true_false',
                'question': '测试题目4：Python是开源的编程语言。',
                'correct_answer': '对',
                'explanation': 'Python确实是开源的编程语言。',
                'score': 1
            },
            {
                'type': 'short_answer',
                'question': '测试题目5：请简述Python的主要用途。',
                'correct_answer': 'Python主要用于Web开发、数据科学、人工智能、自动化脚本等领域。',
                'explanation': 'Python应用广泛，包括Web开发、数据分析、机器学习等多个领域。',
                'score': 3
            }
        ]
        
        print(f"准备创建考试，包含 {len(test_questions)} 道题目")
        
        # 验证题目格式
        for i, q in enumerate(test_questions, 1):
            print(f"题目 {i}: {q['type']} - {q['question'][:30]}...")
            if q['type'] in ['single_choice', 'multiple_choice']:
                options = q.get('options', [])
                print(f"  选项数量: {len(options)}")
                if options:
                    print(f"  选项: {options}")
                else:
                    print("  ❌ 缺少选项！")
            print(f"  答案: {q['correct_answer']}")
            print()
        
        # 创建考试
        exam_id = exam_manager.create_exam(
            title="选项修复测试考试",
            description="用于测试题目选项显示修复的考试",
            questions=test_questions,
            time_limit=30
        )
        
        print(f"✅ 测试考试创建成功，ID: {exam_id}")
        
        # 验证考试是否保存正确
        saved_exam = exam_manager.get_exam_by_id(exam_id)
        if saved_exam:
            print(f"✅ 考试保存验证成功")
            print(f"  标题: {saved_exam['title']}")
            print(f"  题目数量: {len(saved_exam['questions'])}")
            
            # 检查保存的题目选项
            for i, q in enumerate(saved_exam['questions'], 1):
                if q['type'] in ['single_choice', 'multiple_choice']:
                    options = q.get('options', [])
                    print(f"  题目 {i} 选项: {len(options)} 个")
                    if not options:
                        print(f"    ❌ 题目 {i} 保存后仍缺少选项")
        else:
            print("❌ 考试保存验证失败")
        
        return exam_id
        
    except Exception as e:
        print(f"❌ 创建测试考试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_exam_display():
    """测试考试显示"""
    try:
        print("\n=== 测试考试显示逻辑 ===")
        
        # 模拟题目数据
        test_question = {
            'type': 'single_choice',
            'question': '这是一道测试题目，用于验证选项显示',
            'options': ['选项A', '选项B', '选项C', '选项D'],
            'correct_answer': 'A',
            'explanation': '这是测试解析',
            'score': 1
        }
        
        print("测试题目数据:")
        print(f"  类型: {test_question['type']}")
        print(f"  题目: {test_question['question']}")
        print(f"  选项: {test_question.get('options', '无选项')}")
        print(f"  答案: {test_question['correct_answer']}")
        
        # 模拟选项处理逻辑
        options = test_question.get('options', [])
        if not options:
            print("⚠️ 题目缺少选项，添加默认选项")
            options = ["选项A", "选项B", "选项C", "选项D"]
            test_question['options'] = options
        
        print(f"处理后选项: {options}")
        
        # 模拟界面显示
        print("\n模拟界面显示:")
        for i, option in enumerate(options):
            print(f"  {chr(65+i)}. {option}")
        
        return True
        
    except Exception as e:
        print(f"❌ 考试显示测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始创建测试考试...\n")
    
    # 创建测试考试
    exam_id = create_test_exam()
    
    # 测试显示逻辑
    display_ok = test_exam_display()
    
    print("\n=== 测试总结 ===")
    
    if exam_id:
        print("✅ 测试考试创建成功")
        print(f"   考试ID: {exam_id}")
    else:
        print("❌ 测试考试创建失败")
    
    if display_ok:
        print("✅ 显示逻辑测试通过")
    else:
        print("❌ 显示逻辑测试失败")
    
    if exam_id and display_ok:
        print("\n🎉 选项修复完成！")
        print("\n现在您可以：")
        print("1. 启动考试系统")
        print("2. 选择'开始考试'")
        print("3. 选择'选项修复测试考试'")
        print("4. 验证题目选项是否正常显示")
        
        print("\n修复说明：")
        print("• 如果AI生成的题目缺少选项，系统会自动添加默认选项")
        print("• 选择题现在保证至少有4个选项")
        print("• 添加了详细的调试信息")
        print("• 改进了题目验证逻辑")
    else:
        print("\n⚠️ 部分功能仍有问题，请检查错误信息")

if __name__ == "__main__":
    main()
