#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版舒适考试窗口
确保基本功能正常工作
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from datetime import datetime
import json

class SimpleComfortableExamWindow:
    def __init__(self, parent, exam_manager, wrong_question_manager, exam_data):
        """初始化简化版舒适考试窗口"""
        self.parent = parent
        self.exam_manager = exam_manager
        self.wrong_question_manager = wrong_question_manager
        self.exam_data = exam_data
        
        # 调试信息
        print(f"🎯 初始化简化版舒适考试界面")
        print(f"📝 考试标题: {exam_data.get('title', '未知')}")
        print(f"📊 题目数量: {len(exam_data.get('questions', []))}")
        
        # 验证数据
        if not exam_data or not exam_data.get('questions'):
            messagebox.showerror("错误", "考试数据无效！")
            return
            
        self.window = tk.Toplevel(parent)
        self.window.title(f"🎯 舒适版考试 - {exam_data['title']}")
        self.window.geometry("1200x800")
        self.window.transient(parent)
        self.window.grab_set()
        
        # 考试状态
        self.current_question = 0
        self.answers = {}
        self.start_time = datetime.now()
        self.time_left = exam_data.get('time_limit', 60) * 60
        self.is_exam_finished = False
        self.timer_thread = None
        self.marked_questions = set()
        self.answer_widgets = {}
        
        # 防止关闭窗口
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 设置UI
        self.setup_ui()
        
        # 加载第一题
        self.load_question()
        
        # 启动计时器
        self.start_timer()
        
        # 设置快捷键
        self.setup_shortcuts()
        
        print("✅ 简化版舒适考试界面初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        # 顶部信息栏
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 考试标题
        title_label = ttk.Label(info_frame, text=f"📝 {self.exam_data['title']}", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # 时间显示
        self.time_label = ttk.Label(info_frame, text="⏰ 计时中...", 
                                   font=("Microsoft YaHei", 14, "bold"), 
                                   foreground="red")
        self.time_label.pack(side=tk.RIGHT)
        
        # 进度信息
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.progress_label = ttk.Label(progress_frame, text="", 
                                       font=("Microsoft YaHei", 12))
        self.progress_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(20, 0))
        
        # 主内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 左侧：题目区域
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))
        
        # 题目标题
        self.question_title_label = ttk.Label(left_frame, text="", 
                                             font=("Microsoft YaHei", 14, "bold"))
        self.question_title_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 题目内容
        question_frame = ttk.LabelFrame(left_frame, text="题目内容", padding=15)
        question_frame.pack(fill=tk.BOTH, expand=True)
        
        # 题目文本
        self.question_text = scrolledtext.ScrolledText(
            question_frame, wrap=tk.WORD, height=10, 
            font=("Microsoft YaHei", 12), state=tk.DISABLED)
        self.question_text.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 答案区域
        answer_label = ttk.Label(question_frame, text="请选择答案：", 
                                font=("Microsoft YaHei", 12, "bold"))
        answer_label.pack(anchor=tk.W, pady=(0, 10))
        
        self.answer_frame = ttk.Frame(question_frame)
        self.answer_frame.pack(fill=tk.BOTH, expand=True)
        
        # 右侧：导航区域
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(15, 0))
        
        # 导航按钮
        nav_frame = ttk.LabelFrame(right_frame, text="导航", padding=15)
        nav_frame.pack(fill=tk.X, pady=(0, 15))

        # TTK按钮不支持font参数，只设置width
        button_style = {'width': 12}

        self.prev_btn = ttk.Button(nav_frame, text="⬅️ 上一题",
                                  command=self.prev_question, **button_style)
        self.prev_btn.pack(fill=tk.X, pady=(0, 8))

        self.next_btn = ttk.Button(nav_frame, text="下一题 ➡️",
                                  command=self.next_question, **button_style)
        self.next_btn.pack(fill=tk.X, pady=(0, 8))
        
        # 题目状态
        status_frame = ttk.LabelFrame(right_frame, text="题目状态", padding=15)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 创建题目状态按钮
        self.create_status_buttons(status_frame)
        
        # 操作按钮
        action_frame = ttk.LabelFrame(right_frame, text="操作", padding=15)
        action_frame.pack(fill=tk.X)

        ttk.Button(action_frame, text="🔖 标记",
                  command=self.mark_question, **button_style).pack(fill=tk.X, pady=(0, 8))

        ttk.Button(action_frame, text="🗑️ 清除",
                  command=self.clear_answer, **button_style).pack(fill=tk.X)
        
        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X)

        # TTK按钮不支持font参数，只设置width
        big_button_style = {'width': 15}

        ttk.Button(bottom_frame, text="💾 保存答案",
                  command=self.save_current_answer, **big_button_style).pack(side=tk.LEFT)

        self.submit_btn = ttk.Button(bottom_frame, text="✅ 提交试卷",
                                    command=self.submit_exam, **big_button_style)
        self.submit_btn.pack(side=tk.RIGHT)
        
    def create_status_buttons(self, parent):
        """创建题目状态按钮"""
        # 滚动框架
        canvas = tk.Canvas(parent, height=200)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 创建按钮网格
        self.status_buttons = []
        total_questions = len(self.exam_data['questions'])
        
        for i in range(total_questions):
            row = i // 5
            col = i % 5
            
            btn = tk.Button(scrollable_frame, text=str(i+1), width=4, height=2,
                           font=("Microsoft YaHei", 10),
                           command=lambda idx=i: self.jump_to_question(idx))
            btn.grid(row=row, column=col, padx=2, pady=2)
            self.status_buttons.append(btn)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def load_question(self):
        """加载当前题目"""
        print(f"📖 加载题目 {self.current_question + 1}")
        
        questions = self.exam_data.get('questions', [])
        if self.current_question >= len(questions):
            print("❌ 题目索引超出范围")
            return
            
        question = questions[self.current_question]
        
        # 更新标题
        total = len(questions)
        answered = len(self.answers)
        title_text = f"第 {self.current_question + 1} 题 / 共 {total} 题"
        self.question_title_label.config(text=title_text)
        
        # 更新进度
        progress_text = f"已答题：{answered} / {total}"
        self.progress_label.config(text=progress_text)
        self.progress_bar['value'] = (answered / total) * 100
        
        # 显示题目内容
        self.question_text.config(state=tk.NORMAL)
        self.question_text.delete(1.0, tk.END)
        
        question_content = f"【{self.get_question_type_name(question['type'])}】{question.get('score', 1)}分\n\n"
        question_content += question.get('question', '无题目内容')
        
        self.question_text.insert(1.0, question_content)
        self.question_text.config(state=tk.DISABLED)
        
        # 清空并创建答案控件
        for widget in self.answer_frame.winfo_children():
            widget.destroy()
            
        self.create_answer_widgets(question)
        self.restore_answer()
        self.update_status_buttons()
        self.update_navigation_buttons()
        
        print(f"✅ 题目 {self.current_question + 1} 加载完成")
        
    def create_answer_widgets(self, question):
        """创建答案控件"""
        question_id = str(self.current_question)
        
        if question['type'] == 'single_choice':
            # 单选题
            self.answer_widgets[question_id] = tk.StringVar()
            options = question.get('options', ['选项A', '选项B', '选项C', '选项D'])
            
            for i, option in enumerate(options):
                rb = ttk.Radiobutton(self.answer_frame, 
                                   text=f"{chr(65+i)}. {option}",
                                   variable=self.answer_widgets[question_id], 
                                   value=chr(65+i))
                rb.pack(anchor=tk.W, pady=3)
                
        elif question['type'] == 'true_false':
            # 判断题
            self.answer_widgets[question_id] = tk.StringVar()
            
            ttk.Radiobutton(self.answer_frame, text="✅ 正确", 
                          variable=self.answer_widgets[question_id], 
                          value="对").pack(anchor=tk.W, pady=3)
            ttk.Radiobutton(self.answer_frame, text="❌ 错误", 
                          variable=self.answer_widgets[question_id], 
                          value="错").pack(anchor=tk.W, pady=3)
                          
        elif question['type'] == 'multiple_choice':
            # 多选题
            self.answer_widgets[question_id] = {}
            options = question.get('options', ['选项A', '选项B', '选项C', '选项D'])
            
            for i, option in enumerate(options):
                var = tk.BooleanVar()
                self.answer_widgets[question_id][chr(65+i)] = var
                
                cb = ttk.Checkbutton(self.answer_frame, 
                                   text=f"{chr(65+i)}. {option}", 
                                   variable=var)
                cb.pack(anchor=tk.W, pady=3)
                
        elif question['type'] in ['short_answer', 'case_analysis']:
            # 简答题
            height = 8 if question['type'] == 'case_analysis' else 5
            self.answer_widgets[question_id] = scrolledtext.ScrolledText(
                self.answer_frame, wrap=tk.WORD, height=height,
                font=("Microsoft YaHei", 11))
            self.answer_widgets[question_id].pack(fill=tk.BOTH, expand=True)
            
    def get_question_type_name(self, question_type):
        """获取题目类型名称"""
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题', 
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        return type_names.get(question_type, '未知题型')
        
    def save_current_answer(self):
        """保存当前答案"""
        question_id = str(self.current_question)
        question = self.exam_data['questions'][self.current_question]
        
        if question_id not in self.answer_widgets:
            return
            
        if question['type'] in ['single_choice', 'true_false']:
            answer = self.answer_widgets[question_id].get()
            if answer:
                self.answers[question_id] = answer
            elif question_id in self.answers:
                del self.answers[question_id]
                
        elif question['type'] == 'multiple_choice':
            selected = []
            for option, var in self.answer_widgets[question_id].items():
                if var.get():
                    selected.append(option)
            if selected:
                self.answers[question_id] = selected
            elif question_id in self.answers:
                del self.answers[question_id]
                
        elif question['type'] in ['short_answer', 'case_analysis']:
            answer = self.answer_widgets[question_id].get(1.0, tk.END).strip()
            if answer:
                self.answers[question_id] = answer
            elif question_id in self.answers:
                del self.answers[question_id]
                
        self.update_status_buttons()
        self.load_question()  # 刷新进度显示
        
    def restore_answer(self):
        """恢复答案"""
        question_id = str(self.current_question)
        if question_id not in self.answers or question_id not in self.answer_widgets:
            return
            
        answer = self.answers[question_id]
        question = self.exam_data['questions'][self.current_question]
        
        if question['type'] in ['single_choice', 'true_false']:
            self.answer_widgets[question_id].set(answer)
        elif question['type'] == 'multiple_choice':
            if isinstance(answer, list):
                for option in answer:
                    if option in self.answer_widgets[question_id]:
                        self.answer_widgets[question_id][option].set(True)
        elif question['type'] in ['short_answer', 'case_analysis']:
            self.answer_widgets[question_id].delete(1.0, tk.END)
            self.answer_widgets[question_id].insert(1.0, answer)
            
    def update_status_buttons(self):
        """更新状态按钮"""
        for i, btn in enumerate(self.status_buttons):
            if i == self.current_question:
                btn.config(bg='#ff6b6b', fg='white')  # 当前题目
            elif str(i) in self.answers:
                btn.config(bg='#51cf66', fg='white')  # 已答题
            else:
                btn.config(bg='#f8f9fa', fg='black')  # 未答题
                
    def update_navigation_buttons(self):
        """更新导航按钮"""
        self.prev_btn.config(state='normal' if self.current_question > 0 else 'disabled')
        self.next_btn.config(state='normal' if self.current_question < len(self.exam_data['questions']) - 1 else 'disabled')
        
    def prev_question(self):
        """上一题"""
        if self.current_question > 0:
            self.save_current_answer()
            self.current_question -= 1
            self.load_question()
            
    def next_question(self):
        """下一题"""
        if self.current_question < len(self.exam_data['questions']) - 1:
            self.save_current_answer()
            self.current_question += 1
            self.load_question()
            
    def jump_to_question(self, index):
        """跳转到题目"""
        if 0 <= index < len(self.exam_data['questions']):
            self.save_current_answer()
            self.current_question = index
            self.load_question()
            
    def mark_question(self):
        """标记题目"""
        messagebox.showinfo("提示", f"已标记第{self.current_question + 1}题")
        
    def clear_answer(self):
        """清除答案"""
        question_id = str(self.current_question)
        question = self.exam_data['questions'][self.current_question]
        
        if question['type'] in ['single_choice', 'true_false']:
            if question_id in self.answer_widgets:
                self.answer_widgets[question_id].set("")
        elif question['type'] == 'multiple_choice':
            if question_id in self.answer_widgets:
                for var in self.answer_widgets[question_id].values():
                    var.set(False)
        elif question['type'] in ['short_answer', 'case_analysis']:
            if question_id in self.answer_widgets:
                self.answer_widgets[question_id].delete(1.0, tk.END)
                
        if question_id in self.answers:
            del self.answers[question_id]
            
        self.update_status_buttons()
        self.load_question()
        
    def setup_shortcuts(self):
        """设置快捷键"""
        self.window.bind('<Left>', lambda e: self.prev_question())
        self.window.bind('<Right>', lambda e: self.next_question())
        self.window.bind('<Control-s>', lambda e: self.save_current_answer())
        self.window.bind('<Control-Return>', lambda e: self.submit_exam())
        
        # 数字键快速选择
        for i in range(1, 5):
            self.window.bind(f'<Key-{i}>', lambda e, idx=i-1: self.quick_select(idx))
            
        self.window.focus_set()
        
    def quick_select(self, option_index):
        """快速选择选项"""
        question = self.exam_data['questions'][self.current_question]
        question_id = str(self.current_question)
        
        if question['type'] == 'single_choice' and question_id in self.answer_widgets:
            options = question.get('options', [])
            if option_index < len(options):
                option_value = chr(65 + option_index)
                self.answer_widgets[question_id].set(option_value)
                
    def start_timer(self):
        """启动计时器"""
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()
        
    def timer_worker(self):
        """计时器工作线程"""
        while self.time_left > 0 and not self.is_exam_finished:
            minutes = self.time_left // 60
            seconds = self.time_left % 60
            time_text = f"⏰ 剩余时间：{minutes:02d}:{seconds:02d}"
            
            self.window.after(0, lambda: self.time_label.config(text=time_text))
            
            self.time_left -= 1
            threading.Event().wait(1)
            
        if not self.is_exam_finished:
            self.window.after(0, self.time_up)
            
    def time_up(self):
        """时间到"""
        messagebox.showwarning("时间到", "考试时间已到，将自动提交试卷！")
        self.submit_exam()
        
    def submit_exam(self):
        """提交试卷"""
        self.save_current_answer()
        
        answered = len(self.answers)
        total = len(self.exam_data['questions'])
        
        if answered < total:
            if not messagebox.askyesno("确认提交", 
                                     f"您还有{total - answered}道题目未作答，确定要提交吗？"):
                return
                
        self.is_exam_finished = True
        
        # 计算分数
        score, total_score = self.exam_manager.calculate_score(
            self.exam_data['questions'], self.answers)
        
        end_time = datetime.now()
        
        try:
            # 保存考试记录
            record_id = self.exam_manager.save_exam_record(
                exam_id=self.exam_data['id'],
                answers=self.answers,
                score=score,
                total_score=total_score,
                start_time=self.start_time,
                end_time=end_time
            )
            
            # 自动添加错题
            try:
                record = self.exam_manager.get_exam_record_by_id(record_id)
                if record:
                    added_count = self.wrong_question_manager.batch_add_wrong_questions_from_exam(record)
                    print(f"✅ 自动添加了 {added_count} 道错题")
            except Exception as e:
                print(f"❌ 自动添加错题失败: {e}")
                
            # 显示结果
            self.show_result(score, total_score, record_id)
            
        except Exception as e:
            messagebox.showerror("错误", f"保存考试记录失败：{str(e)}")
            
    def show_result(self, score, total_score, record_id):
        """显示结果"""
        duration = datetime.now() - self.start_time
        percentage = (score / total_score) * 100 if total_score > 0 else 0
        
        result_text = f"""
🎉 考试完成！

得分：{score:.1f} / {total_score:.1f} ({percentage:.1f}%)
用时：{str(duration).split('.')[0]}
记录ID：{record_id}

感谢使用舒适版考试界面！
"""
        
        messagebox.showinfo("考试结果", result_text)
        self.window.destroy()
        
    def on_closing(self):
        """关闭窗口"""
        if not self.is_exam_finished:
            if messagebox.askyesno("确认退出", "考试尚未完成，确定要退出吗？"):
                self.is_exam_finished = True
                self.window.destroy()
        else:
            self.window.destroy()
