#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试提交修复
"""

from src.core.database_manager import DatabaseManager
from src.core.exam_manager import ExamManager
from datetime import datetime

def test_submit_fix():
    """测试提交修复"""
    print("🔧 测试考试提交修复...")
    
    # 初始化
    db_manager = DatabaseManager()
    exam_manager = ExamManager(db_manager)
    
    # 创建测试考试
    test_questions = [
        {
            "type": "single_choice",
            "question": "测试题目：1+1等于多少？",
            "options": ["1", "2", "3", "4"],
            "correct_answer": "B",
            "explanation": "1+1=2",
            "score": 1
        }
    ]
    
    try:
        # 创建考试
        exam_id = exam_manager.create_exam(
            title="提交测试考试",
            description="用于测试提交功能修复",
            questions=test_questions,
            time_limit=5
        )
        print(f"✅ 创建考试成功，ID: {exam_id}")
        
        # 模拟考试记录
        test_answers = {"0": "B"}  # 正确答案
        start_time = datetime.now()
        end_time = datetime.now()
        
        # 测试保存考试记录（使用正确的参数）
        record_id = exam_manager.save_exam_record(
            exam_id=exam_id,
            answers=test_answers,
            score=1.0,
            total_score=1.0,
            start_time=start_time,
            end_time=end_time
        )
        
        print(f"✅ 保存考试记录成功，ID: {record_id}")
        
        # 验证记录
        record = exam_manager.get_exam_record_by_id(record_id)
        if record:
            print("✅ 考试记录验证成功")
            print(f"  考试ID: {record['exam_id']}")
            print(f"  分数: {record['score']}/{record['total_score']}")
            print(f"  答案: {record['answers']}")
        else:
            print("❌ 考试记录验证失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_submit_fix()
