@echo off
chcp 65001 >nul
title 一键修复工具

echo.
echo ========================================
echo 🚀 智能化考试系统 - 一键修复工具
echo ========================================
echo.
echo 🎯 这个工具将自动：
echo • 检查Python版本
echo • 安装缺失的依赖包
echo • 修复常见问题
echo • 启动系统
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python版本检查通过
python --version

echo.
echo 🔧 开始自动修复...
echo.

REM 安装基础依赖
echo 📦 安装基础依赖...
pip install requests >nul 2>&1
if errorlevel 1 (
    echo ⚠️ requests安装失败，尝试使用镜像...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ requests >nul 2>&1
)

REM 安装PyQt6（现代UI需要）
echo 🎨 安装现代UI依赖...
pip install PyQt6 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ PyQt6安装失败，尝试使用镜像...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyQt6 >nul 2>&1
    if errorlevel 1 (
        echo ❌ PyQt6安装失败，现代UI功能将不可用
        set PYQT6_OK=false
    ) else (
        echo ✅ PyQt6安装成功（使用镜像）
        set PYQT6_OK=true
    )
) else (
    echo ✅ PyQt6安装成功
    set PYQT6_OK=true
)

REM 安装智能功能依赖（可选）
echo 🧠 安装智能功能依赖...
pip install numpy matplotlib pandas >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 智能功能依赖安装失败，尝试使用镜像...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ numpy matplotlib pandas >nul 2>&1
    if errorlevel 1 (
        echo ❌ 智能功能依赖安装失败，部分功能可能受限
    ) else (
        echo ✅ 智能功能依赖安装成功（使用镜像）
    )
) else (
    echo ✅ 智能功能依赖安装成功
)

echo.
echo ✅ 自动修复完成！
echo.

REM 检查最终状态
echo 🔍 检查系统状态...
python -c "import requests; print('✅ requests: 网络请求功能正常')" 2>nul || echo "❌ requests: 网络请求功能异常"
python -c "import PyQt6; print('✅ PyQt6: 现代UI功能正常')" 2>nul || echo "❌ PyQt6: 现代UI功能异常"
python -c "import numpy; print('✅ numpy: 数值计算功能正常')" 2>nul || echo "⚠️ numpy: 数值计算功能受限"
python -c "import matplotlib; print('✅ matplotlib: 图表功能正常')" 2>nul || echo "⚠️ matplotlib: 图表功能受限"

echo.
echo 🚀 推荐启动方式：
echo.

if "%PYQT6_OK%"=="true" (
    echo 1. 🎨 现代UI演示 (推荐)
    echo    python demo_modern_ui.py
    echo.
)

echo 2. 🚀 稳定版启动 (推荐)
echo    python main_stable.py
echo.
echo 3. 📋 启动菜单
echo    run_menu.bat
echo.

set /p choice=现在启动系统吗？(y/n): 
if /i "%choice%"=="y" goto start
if /i "%choice%"=="yes" goto start
goto end

:start
echo.
echo 🚀 启动系统...

if "%PYQT6_OK%"=="true" (
    echo 使用现代UI启动...
    python demo_modern_ui.py
) else if exist main_stable.py (
    echo 使用稳定版启动...
    python main_stable.py
) else (
    echo 使用标准版启动...
    python main.py
)

:end
echo.
echo 🎉 修复完成！
echo.
echo 💡 如果还有问题，可以尝试：
echo • python quick_fix.py (快速修复)
echo • python diagnose_and_fix.py (详细诊断)
echo • run_menu.bat (启动菜单)
echo.
pause
