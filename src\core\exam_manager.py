#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试管理器
负责考试的创建、管理和记录
"""

import json
from datetime import datetime
from typing import List, Dict, Any

class ExamManager:
    def __init__(self, db_manager):
        """初始化考试管理器"""
        self.db = db_manager
    
    def create_exam(self, title: str, description: str, questions: List[Dict], time_limit: int = 60):
        """创建考试"""
        try:
            # 验证输入
            if not questions:
                raise ValueError("题目列表不能为空")

            print(f"正在创建考试: {title}, 题目数量: {len(questions)}")

            questions_json = json.dumps(questions, ensure_ascii=False)

            query = """
                INSERT INTO exams (title, description, time_limit, questions, created_at)
                VALUES (?, ?, ?, ?, ?)
            """
            params = (title, description, time_limit, questions_json, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

            exam_id = self.db.execute_update(query, params)
            print(f"考试创建成功，ID: {exam_id}")

            return exam_id

        except Exception as e:
            print(f"创建考试失败: {str(e)}")
            raise Exception(f"创建考试失败: {str(e)}")
    
    def get_exam_by_id(self, exam_id: int):
        """根据ID获取考试"""
        query = "SELECT * FROM exams WHERE id = ?"
        result = self.db.execute_query(query, (exam_id,))
        
        if result:
            exam = result[0]
            # 解析JSON格式的题目
            questions = json.loads(exam[3])
            return {
                'id': exam[0],
                'title': exam[1],
                'description': exam[2],
                'questions': questions,
                'time_limit': exam[4],
                'created_at': exam[5]
            }
        return None
    
    def get_all_exams(self):
        """获取所有考试"""
        query = "SELECT id, title, description, time_limit, created_at FROM exams ORDER BY created_at DESC"
        return self.db.execute_query(query)
    
    def delete_exam(self, exam_id: int):
        """删除考试"""
        # 先删除相关的考试记录
        self.db.execute_update("DELETE FROM exam_records WHERE exam_id = ?", (exam_id,))
        # 再删除考试
        self.db.execute_update("DELETE FROM exams WHERE id = ?", (exam_id,))
    
    def save_exam_record(self, exam_id: int, answers: Dict, score: float, total_score: float, 
                        start_time: datetime, end_time: datetime):
        """保存考试记录"""
        try:
            answers_json = json.dumps(answers, ensure_ascii=False)
            
            query = """
                INSERT INTO exam_records (exam_id, answers, score, total_score, start_time, end_time, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (exam_id, answers_json, score, total_score, start_time, end_time, datetime.now())
            
            record_id = self.db.execute_update(query, params)
            return record_id
            
        except Exception as e:
            raise Exception(f"保存考试记录失败: {str(e)}")
    
    def get_exam_records(self, exam_id: int = None):
        """获取考试记录"""
        if exam_id:
            query = """
                SELECT er.*, e.title as exam_title
                FROM exam_records er
                JOIN exams e ON er.exam_id = e.id
                WHERE er.exam_id = ?
                ORDER BY er.created_at DESC
            """
            return self.db.execute_query(query, (exam_id,))
        else:
            query = """
                SELECT er.*, e.title as exam_title
                FROM exam_records er
                JOIN exams e ON er.exam_id = e.id
                ORDER BY er.created_at DESC
            """
            return self.db.execute_query(query)
    
    def get_exam_record_by_id(self, record_id: int):
        """根据ID获取考试记录"""
        query = """
            SELECT er.*, e.title as exam_title, e.questions
            FROM exam_records er
            JOIN exams e ON er.exam_id = e.id
            WHERE er.id = ?
        """
        result = self.db.execute_query(query, (record_id,))
        
        if result:
            record = result[0]
            return {
                'id': record[0],
                'exam_id': record[1],
                'answers': json.loads(record[2]),
                'score': record[3],
                'total_score': record[4],
                'start_time': record[5],
                'end_time': record[6],
                'created_at': record[7],
                'exam_title': record[8],
                'questions': json.loads(record[9])
            }
        return None
    
    def get_exam_statistics(self):
        """获取考试统计信息"""
        stats = {}
        
        # 总考试数
        query = "SELECT COUNT(*) FROM exams"
        result = self.db.execute_query(query)
        stats['total_exams'] = result[0][0] if result else 0
        
        # 总考试记录数
        query = "SELECT COUNT(*) FROM exam_records"
        result = self.db.execute_query(query)
        stats['total_records'] = result[0][0] if result else 0
        
        # 平均分
        query = "SELECT AVG(score), AVG(total_score) FROM exam_records"
        result = self.db.execute_query(query)
        if result and result[0][0] is not None:
            avg_score = result[0][0]
            avg_total = result[0][1]
            stats['average_percentage'] = (avg_score / avg_total * 100) if avg_total > 0 else 0
        else:
            stats['average_percentage'] = 0
        
        # 最高分
        query = "SELECT MAX(score), MAX(total_score) FROM exam_records"
        result = self.db.execute_query(query)
        if result and result[0][0] is not None:
            max_score = result[0][0]
            max_total = result[0][1]
            stats['highest_percentage'] = (max_score / max_total * 100) if max_total > 0 else 0
        else:
            stats['highest_percentage'] = 0
        
        return stats
    
    def calculate_score(self, questions: List[Dict], answers: Dict):
        """计算考试分数"""
        total_score = 0
        correct_score = 0
        
        for i, question in enumerate(questions):
            question_score = question.get('score', 1)  # 默认每题1分
            total_score += question_score
            
            question_id = str(i)
            user_answer = answers.get(question_id, '')
            correct_answer = question.get('correct_answer', '')
            
            # 根据题目类型判断答案是否正确
            if question['type'] in ['single_choice', 'true_false']:
                if user_answer == correct_answer:
                    correct_score += question_score
            elif question['type'] == 'multiple_choice':
                # 多选题需要完全匹配
                if isinstance(user_answer, list) and isinstance(correct_answer, list):
                    if set(user_answer) == set(correct_answer):
                        correct_score += question_score
            elif question['type'] in ['short_answer', 'case_analysis']:
                # 简答题和案例分析题需要人工评分，这里暂时给0分
                # 实际应用中可以集成AI评分或人工评分功能
                pass
        
        return correct_score, total_score
