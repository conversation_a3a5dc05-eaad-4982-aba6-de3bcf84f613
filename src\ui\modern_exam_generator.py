#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化AI考试生成系统 - 灵动风格UI
使用PyQt6实现毛玻璃效果和现代化界面
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFrame, QScrollArea,
                            QSpinBox, QSlider, QComboBox, QTextEdit, QGroupBox,
                            QGridLayout, QSizePolicy)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, pyqtSignal, QTimer, QPoint
from PyQt6.QtGui import (QPainter, QLinearGradient, QColor, QPen, QBrush,
                        QFont, QPalette, QPixmap, QIcon, QPainterPath, QRadialGradient)
from PyQt6.QtWidgets import QGraphicsBlurEffect, QGraphicsDropShadowEffect

class RippleButton(QPushButton):
    """带涟漪效果的按钮"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.ripple_radius = 0
        self.ripple_center = QPoint()
        self.ripple_animation = QPropertyAnimation(self, b"ripple_radius")
        self.ripple_animation.setDuration(600)
        self.ripple_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.ripple_animation.finished.connect(self.reset_ripple)

    def get_ripple_radius(self):
        return self.ripple_radius

    def set_ripple_radius(self, radius):
        self.ripple_radius = radius
        self.update()

    ripple_radius = pyqtSignal(int)

    def mousePressEvent(self, event):
        """鼠标按下事件 - 触发涟漪效果"""
        super().mousePressEvent(event)

        self.ripple_center = event.position().toPoint()
        max_radius = max(self.width(), self.height())

        self.ripple_animation.setStartValue(0)
        self.ripple_animation.setEndValue(max_radius)
        self.ripple_animation.start()

    def paintEvent(self, event):
        """绘制事件 - 绘制涟漪效果"""
        super().paintEvent(event)

        if self.ripple_radius > 0:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 创建涟漪渐变
            gradient = QRadialGradient(self.ripple_center, self.ripple_radius)
            gradient.setColorAt(0, QColor(255, 255, 255, 80))
            gradient.setColorAt(0.8, QColor(255, 255, 255, 40))
            gradient.setColorAt(1, QColor(255, 255, 255, 0))

            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawEllipse(self.ripple_center, self.ripple_radius, self.ripple_radius)

    def reset_ripple(self):
        """重置涟漪效果"""
        self.ripple_radius = 0
        self.update()

class ModernExamGenerator(QMainWindow):
    """现代化AI考试生成系统主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧠 AI考试生成系统 - 灵动版")
        self.setGeometry(100, 100, 1200, 800)

        # 设置窗口属性 - 移除透明背景以避免模糊问题
        # self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        # self.setWindowFlags(Qt.WindowType.FramelessWindowHint)

        # 初始化UI
        self.init_ui()
        self.setup_styles()

        # 设置实时更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_preview)
        self.update_timer.setSingleShot(True)  # 单次触发，避免频繁更新
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建主容器
        self.main_container = QFrame()
        self.main_container.setObjectName("mainContainer")
        main_layout.addWidget(self.main_container)

        # 移除毛玻璃效果以避免模糊问题
        # blur_effect = QGraphicsBlurEffect()
        # blur_effect.setBlurRadius(15)
        # self.main_container.setGraphicsEffect(blur_effect)
        
        # 主容器布局
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # 创建顶部导航栏
        self.create_navigation_bar(container_layout)
        
        # 创建内容区域
        self.create_content_area(container_layout)
        
    def create_navigation_bar(self, parent_layout):
        """创建灵动岛风格导航栏"""
        nav_bar = QFrame()
        nav_bar.setObjectName("navBar")
        nav_bar.setFixedHeight(50)
        parent_layout.addWidget(nav_bar)
        
        nav_layout = QHBoxLayout(nav_bar)
        nav_layout.setContentsMargins(20, 0, 20, 0)
        
        # 标题
        title_label = QLabel("🧠 AI考试生成系统")
        title_label.setObjectName("titleLabel")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        nav_layout.addWidget(title_label)
        
        nav_layout.addStretch()
        
        # 导航按钮
        nav_buttons = [
            ("🏠 首页", self.go_home),
            ("📊 统计", self.show_stats),
            ("⚙️ 设置", self.show_settings),
            ("❌", self.close)
        ]
        
        for text, callback in nav_buttons:
            btn = QPushButton(text)
            btn.setObjectName("navButton")
            btn.clicked.connect(callback)
            btn.setFixedSize(80, 30) if text != "❌" else btn.setFixedSize(30, 30)
            nav_layout.addWidget(btn)
            
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        content_widget = QWidget()
        parent_layout.addWidget(content_widget)
        
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # 左侧参数设置区
        self.create_parameter_panel(content_layout)
        
        # 右侧预览区
        self.create_preview_panel(content_layout)
        
    def create_parameter_panel(self, parent_layout):
        """创建参数设置面板"""
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFixedWidth(400)
        scroll_area.setObjectName("scrollArea")
        parent_layout.addWidget(scroll_area)
        
        # 参数面板
        param_widget = QWidget()
        scroll_area.setWidget(param_widget)
        
        param_layout = QVBoxLayout(param_widget)
        param_layout.setSpacing(20)
        
        # 基础设置卡片
        self.create_basic_settings_card(param_layout)
        
        # 题型设置卡片
        self.create_question_type_card(param_layout)
        
        # 难度设置卡片
        self.create_difficulty_card(param_layout)
        
        # 生成按钮
        self.create_generate_button(param_layout)
        
        param_layout.addStretch()
        
    def create_basic_settings_card(self, parent_layout):
        """创建基础设置卡片"""
        card = self.create_card("📝 基础设置")
        parent_layout.addWidget(card)
        
        layout = QVBoxLayout()
        card.setLayout(layout)
        
        # 考试名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("考试名称:"))
        self.exam_name_input = QComboBox()
        self.exam_name_input.setEditable(True)
        self.exam_name_input.addItems(["数学期末考试", "英语模拟考试", "物理单元测试"])
        self.exam_name_input.setObjectName("modernInput")
        name_layout.addWidget(self.exam_name_input)
        layout.addLayout(name_layout)
        
        # 题目总数
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("题目总数:"))
        self.question_count = QSpinBox()
        self.question_count.setRange(5, 100)
        self.question_count.setValue(20)
        self.question_count.setObjectName("modernSpinBox")
        self.question_count.valueChanged.connect(self.delayed_update_preview)
        count_layout.addWidget(self.question_count)
        layout.addLayout(count_layout)
        
        # 考试时长
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("考试时长:"))
        self.exam_duration = QSpinBox()
        self.exam_duration.setRange(30, 300)
        self.exam_duration.setValue(90)
        self.exam_duration.setSuffix(" 分钟")
        self.exam_duration.setObjectName("modernSpinBox")
        time_layout.addWidget(self.exam_duration)
        layout.addLayout(time_layout)
        
    def create_question_type_card(self, parent_layout):
        """创建题型设置卡片"""
        card = self.create_card("🎯 题型分布")
        parent_layout.addWidget(card)
        
        layout = QVBoxLayout()
        card.setLayout(layout)
        
        # 题型滑块
        question_types = [
            ("单选题", 10, "#219be4"),
            ("多选题", 5, "#7338ab"),
            ("判断题", 3, "#ff6b6b"),
            ("填空题", 2, "#4ecdc4")
        ]
        
        self.type_sliders = {}
        for type_name, default_value, color in question_types:
            type_layout = QVBoxLayout()
            
            # 标题和数值
            header_layout = QHBoxLayout()
            label = QLabel(type_name)
            label.setStyleSheet(f"color: {color}; font-weight: bold;")
            header_layout.addWidget(label)
            
            value_label = QLabel(str(default_value))
            value_label.setObjectName("valueLabel")
            header_layout.addWidget(value_label)
            header_layout.addStretch()
            
            type_layout.addLayout(header_layout)
            
            # 滑块
            slider = QSlider(Qt.Orientation.Horizontal)
            slider.setRange(0, 20)
            slider.setValue(default_value)
            slider.setObjectName("modernSlider")
            slider.valueChanged.connect(lambda v, lbl=value_label: lbl.setText(str(v)))
            slider.valueChanged.connect(self.delayed_update_preview)
            
            self.type_sliders[type_name] = slider
            type_layout.addWidget(slider)
            
            layout.addLayout(type_layout)
            
    def create_difficulty_card(self, parent_layout):
        """创建难度设置卡片"""
        card = self.create_card("⚡ 难度分布")
        parent_layout.addWidget(card)
        
        layout = QVBoxLayout()
        card.setLayout(layout)
        
        # 难度滑块
        difficulties = [
            ("简单", 30, "#4CAF50"),
            ("中等", 50, "#FF9800"),
            ("困难", 20, "#F44336")
        ]
        
        self.difficulty_sliders = {}
        for diff_name, default_value, color in difficulties:
            diff_layout = QVBoxLayout()
            
            # 标题和百分比
            header_layout = QHBoxLayout()
            label = QLabel(diff_name)
            label.setStyleSheet(f"color: {color}; font-weight: bold;")
            header_layout.addWidget(label)
            
            percent_label = QLabel(f"{default_value}%")
            percent_label.setObjectName("percentLabel")
            header_layout.addWidget(percent_label)
            header_layout.addStretch()
            
            diff_layout.addLayout(header_layout)
            
            # 滑块
            slider = QSlider(Qt.Orientation.Horizontal)
            slider.setRange(0, 100)
            slider.setValue(default_value)
            slider.setObjectName("modernSlider")
            slider.valueChanged.connect(lambda v, lbl=percent_label: lbl.setText(f"{v}%"))
            slider.valueChanged.connect(self.delayed_update_preview)
            
            self.difficulty_sliders[diff_name] = slider
            diff_layout.addWidget(slider)
            
            layout.addLayout(diff_layout)
            
    def create_generate_button(self, parent_layout):
        """创建生成按钮"""
        button_layout = QVBoxLayout()
        
        # 主生成按钮（带涟漪效果）
        self.generate_btn = RippleButton("🚀 生成智能试卷")
        self.generate_btn.setObjectName("primaryButton")
        self.generate_btn.setFixedHeight(50)
        self.generate_btn.clicked.connect(self.generate_exam)
        button_layout.addWidget(self.generate_btn)
        
        # 辅助按钮（带涟漪效果）
        secondary_layout = QHBoxLayout()

        preview_btn = RippleButton("👁️ 预览")
        preview_btn.setObjectName("secondaryButton")
        preview_btn.clicked.connect(self.preview_exam)
        secondary_layout.addWidget(preview_btn)

        save_btn = RippleButton("💾 保存")
        save_btn.setObjectName("secondaryButton")
        save_btn.clicked.connect(self.save_exam)
        secondary_layout.addWidget(save_btn)
        
        button_layout.addLayout(secondary_layout)
        parent_layout.addLayout(button_layout)
        
    def create_preview_panel(self, parent_layout):
        """创建预览面板"""
        preview_container = QFrame()
        preview_container.setObjectName("previewContainer")
        parent_layout.addWidget(preview_container)
        
        layout = QVBoxLayout(preview_container)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 预览标题
        title = QLabel("📋 试卷预览")
        title.setObjectName("previewTitle")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 预览内容
        self.preview_content = QTextEdit()
        self.preview_content.setObjectName("previewContent")
        self.preview_content.setReadOnly(True)
        layout.addWidget(self.preview_content)
        
        # 初始化预览
        self.update_preview()
        
    def create_card(self, title):
        """创建圆角卡片"""
        card = QFrame()
        card.setObjectName("card")
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(12)
        shadow.setColor(QColor(0, 0, 0, 20))
        shadow.setOffset(0, 4)
        card.setGraphicsEffect(shadow)
        
        return card

    def delayed_update_preview(self):
        """延迟更新预览（避免频繁更新）"""
        self.update_timer.stop()
        self.update_timer.start(300)  # 300ms后更新

    def update_preview(self):
        """更新预览内容"""
        try:
            exam_name = self.exam_name_input.currentText()
            total_questions = self.question_count.value()
            duration = self.exam_duration.value()
            
            # 获取题型分布
            type_distribution = {}
            for type_name, slider in self.type_sliders.items():
                type_distribution[type_name] = slider.value()
            
            # 获取难度分布
            difficulty_distribution = {}
            for diff_name, slider in self.difficulty_sliders.items():
                difficulty_distribution[diff_name] = slider.value()
            
            # 生成预览内容
            preview_text = f"""
<div style="font-family: 'Microsoft YaHei'; line-height: 1.6;">
<h2 style="color: #219be4; text-align: center; margin-bottom: 20px;">
📋 {exam_name}
</h2>

<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
<p><strong>📊 考试信息</strong></p>
<p>• 题目总数：{total_questions} 题</p>
<p>• 考试时长：{duration} 分钟</p>
<p>• 平均每题：{duration/total_questions:.1f} 分钟</p>
</div>

<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
<p><strong>🎯 题型分布</strong></p>
"""
            
            for type_name, count in type_distribution.items():
                if count > 0:
                    percentage = (count / total_questions) * 100
                    preview_text += f"<p>• {type_name}：{count} 题 ({percentage:.1f}%)</p>"
            
            preview_text += """
</div>

<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
<p><strong>⚡ 难度分布</strong></p>
"""
            
            for diff_name, percentage in difficulty_distribution.items():
                if percentage > 0:
                    preview_text += f"<p>• {diff_name}：{percentage}%</p>"
            
            preview_text += """
</div>

<div style="background: #e3f2fd; padding: 15px; border-radius: 8px;">
<p><strong>💡 智能建议</strong></p>
<p>• 建议在考试前进行模拟练习</p>
<p>• 注意时间分配，避免在难题上花费过多时间</p>
<p>• 先完成简单题目，再攻克难题</p>
</div>
</div>
"""
            
            self.preview_content.setHtml(preview_text)
            
        except Exception as e:
            print(f"更新预览失败: {e}")
            
    def setup_styles(self):
        """设置样式表"""
        style = """
        QMainWindow {
            background-color: #f0f2f5;
        }

        #mainContainer {
            background-color: #2196F3;
            border-radius: 12px;
            border: 1px solid #ddd;
        }
        
        #navBar {
            background-color: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid #ddd;
            border-radius: 12px 12px 0 0;
        }

        #titleLabel {
            color: #2196F3;
            font-weight: bold;
        }

        #navButton {
            background-color: #2196F3;
            border: 1px solid #1976D2;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            padding: 5px 10px;
        }

        #navButton:hover {
            background-color: #1976D2;
            border-color: #1565C0;
        }
        
        #scrollArea {
            background: transparent;
            border: none;
        }
        
        #card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 15px;
            margin: 5px;
        }
        
        #modernInput, #modernSpinBox {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(99, 90, 217, 0.2);
            border-radius: 8px;
            padding: 8px;
            font-size: 14px;
        }
        
        #modernInput:focus, #modernSpinBox:focus {
            border-color: #635ad9;
            background: white;
        }
        
        #modernSlider::groove:horizontal {
            border: 1px solid rgba(99, 90, 217, 0.2);
            height: 6px;
            background: rgba(99, 90, 217, 0.1);
            border-radius: 3px;
        }
        
        #modernSlider::handle:horizontal {
            background-color: #2196F3;
            border: 2px solid white;
            width: 18px;
            margin: -7px 0;
            border-radius: 9px;
        }

        #modernSlider::handle:horizontal:hover {
            background-color: #1976D2;
        }

        #primaryButton {
            background-color: #2196F3;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            padding: 15px;
        }

        #primaryButton:hover {
            background-color: #1976D2;
        }

        #primaryButton:pressed {
            background-color: #1565C0;
        }
        
        #secondaryButton {
            background-color: white;
            border: 2px solid #2196F3;
            border-radius: 6px;
            color: #2196F3;
            font-weight: bold;
            padding: 10px 20px;
        }

        #secondaryButton:hover {
            background-color: #E3F2FD;
            border-color: #1976D2;
        }
        
        #previewContainer {
            background: #F5F5F7;
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        #previewTitle {
            color: #333;
            margin-bottom: 10px;
        }
        
        #previewContent {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            font-family: 'Microsoft YaHei';
            font-size: 14px;
        }
        
        QLabel {
            color: #333;
            font-family: 'Microsoft YaHei';
        }
        
        #valueLabel, #percentLabel {
            color: #666;
            font-weight: bold;
            min-width: 40px;
        }
        """
        
        self.setStyleSheet(style)
        
    # 事件处理方法
    def go_home(self):
        """返回首页"""
        print("返回首页")
        
    def show_stats(self):
        """显示统计"""
        print("显示统计")
        
    def show_settings(self):
        """显示设置"""
        print("显示设置")
        
    def generate_exam(self):
        """生成考试"""
        print("生成考试")
        # 这里可以集成之前的智能出题系统
        
    def preview_exam(self):
        """预览考试"""
        print("预览考试")
        
    def save_exam(self):
        """保存考试"""
        print("保存考试")

def main():
    """主函数"""
    # 设置高DPI支持 - 兼容不同PyQt6版本
    try:
        # 新版本PyQt6的高DPI设置
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    except AttributeError:
        pass

    try:
        # 尝试设置高DPI属性（如果存在）
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        pass

    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyle('Fusion')

    # 创建主窗口
    window = ModernExamGenerator()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
