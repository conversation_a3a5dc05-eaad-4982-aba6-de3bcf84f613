#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
用于创建材料导入系统所需的数据库表
"""

import sqlite3
import os
from datetime import datetime


class DatabaseSetup:
    """数据库设置类"""
    
    def __init__(self, db_path: str = "materials.db"):
        """
        初始化数据库设置
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
    
    def create_database(self) -> sqlite3.Connection:
        """
        创建数据库和表结构
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        # 确保数据库目录存在
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
        
        # 连接数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建材料表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS materials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                file_type TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_materials_title ON materials(title)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_materials_file_type ON materials(file_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_materials_created_at ON materials(created_at)')
        
        # 提交更改
        conn.commit()
        
        print(f"数据库创建成功: {self.db_path}")
        return conn
    
    def insert_sample_data(self, conn: sqlite3.Connection):
        """
        插入示例数据
        
        Args:
            conn: 数据库连接对象
        """
        cursor = conn.cursor()
        
        sample_materials = [
            {
                'title': 'Python基础教程',
                'content': '''Python是一种高级编程语言，具有简洁易读的语法。
                
主要特点：
1. 简洁明了的语法
2. 强大的标准库
3. 跨平台兼容性
4. 面向对象编程支持
5. 动态类型系统

基本数据类型：
- 数字类型：int, float, complex
- 字符串类型：str
- 布尔类型：bool
- 列表类型：list
- 元组类型：tuple
- 字典类型：dict
- 集合类型：set

控制结构：
- 条件语句：if, elif, else
- 循环语句：for, while
- 异常处理：try, except, finally''',
                'file_type': 'text'
            },
            {
                'title': '机器学习概述',
                'content': '''机器学习是人工智能的一个分支，通过算法让计算机从数据中学习模式。

主要类型：
1. 监督学习：使用标记数据训练模型
2. 无监督学习：从无标记数据中发现模式
3. 强化学习：通过与环境交互学习最优策略

常用算法：
- 线性回归：用于预测连续值
- 逻辑回归：用于分类问题
- 决策树：基于特征进行决策
- 支持向量机：寻找最优分类边界
- 神经网络：模拟人脑神经元结构

模型评估：
- 准确率：正确预测的比例
- 精确率：预测为正例中真正为正例的比例
- 召回率：真正为正例中被预测为正例的比例
- F1分数：精确率和召回率的调和平均''',
                'file_type': 'text'
            },
            {
                'title': '数据结构基础',
                'content': '''数据结构是计算机科学中组织和存储数据的方式。

基本数据结构：

1. 数组（Array）
   - 连续内存存储
   - 随机访问，时间复杂度O(1)
   - 插入删除复杂度O(n)

2. 链表（Linked List）
   - 非连续内存存储
   - 顺序访问，时间复杂度O(n)
   - 插入删除复杂度O(1)

3. 栈（Stack）
   - 后进先出（LIFO）
   - 主要操作：push, pop, top
   - 应用：函数调用、表达式求值

4. 队列（Queue）
   - 先进先出（FIFO）
   - 主要操作：enqueue, dequeue
   - 应用：任务调度、广度优先搜索

5. 树（Tree）
   - 层次结构
   - 二叉树、平衡树、搜索树
   - 应用：文件系统、数据库索引

6. 图（Graph）
   - 节点和边的集合
   - 有向图、无向图
   - 应用：社交网络、路径规划''',
                'file_type': 'manual'
            }
        ]
        
        for material in sample_materials:
            cursor.execute('''
                INSERT INTO materials (title, content, file_type, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                material['title'],
                material['content'],
                material['file_type'],
                datetime.now(),
                datetime.now()
            ))
        
        conn.commit()
        print(f"插入了 {len(sample_materials)} 条示例数据")
    
    def check_database(self, conn: sqlite3.Connection):
        """
        检查数据库状态
        
        Args:
            conn: 数据库连接对象
        """
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='materials'")
        if cursor.fetchone():
            print("✓ materials表存在")
        else:
            print("✗ materials表不存在")
            return
        
        # 检查数据数量
        cursor.execute("SELECT COUNT(*) FROM materials")
        count = cursor.fetchone()[0]
        print(f"✓ 材料总数: {count}")
        
        # 检查文件类型分布
        cursor.execute("SELECT file_type, COUNT(*) FROM materials GROUP BY file_type")
        type_stats = cursor.fetchall()
        print("✓ 文件类型分布:")
        for file_type, type_count in type_stats:
            print(f"  - {file_type}: {type_count}")
        
        # 显示最近的材料
        cursor.execute("SELECT id, title, file_type, created_at FROM materials ORDER BY created_at DESC LIMIT 3")
        recent_materials = cursor.fetchall()
        print("✓ 最近的材料:")
        for material in recent_materials:
            print(f"  - ID:{material[0]} {material[1]} ({material[2]})")


def main():
    """主函数 - 演示数据库设置"""
    print("=== 材料导入系统数据库设置 ===")
    
    # 创建数据库设置实例
    db_setup = DatabaseSetup("data/materials.db")
    
    # 创建数据库
    conn = db_setup.create_database()
    
    # 检查是否需要插入示例数据
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM materials")
    count = cursor.fetchone()[0]
    
    if count == 0:
        print("\n插入示例数据...")
        db_setup.insert_sample_data(conn)
    else:
        print(f"\n数据库已包含 {count} 条记录，跳过示例数据插入")
    
    # 检查数据库状态
    print("\n=== 数据库状态检查 ===")
    db_setup.check_database(conn)
    
    # 关闭连接
    conn.close()
    print("\n数据库设置完成！")


if __name__ == "__main__":
    main()
