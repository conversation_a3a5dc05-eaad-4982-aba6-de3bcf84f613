#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速插入测试考试
"""

import sqlite3
import json
import os
from datetime import datetime

def quick_insert_exam():
    """快速插入测试考试"""
    
    # 确保数据目录存在
    if not os.path.exists("data"):
        os.makedirs("data")
        print("创建data目录")
    
    db_path = "data/exam_system.db"
    
    try:
        print("=== 快速插入测试考试 ===")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 确保表存在
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exams (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                time_limit INTEGER DEFAULT 60,
                questions TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建测试题目
        test_questions = [
            {
                "type": "single_choice",
                "question": "Python是什么类型的编程语言？",
                "options": ["编译型语言", "解释型语言", "汇编语言", "机器语言"],
                "correct_answer": "B",
                "explanation": "Python是解释型语言，代码在运行时被解释器逐行执行。",
                "score": 2
            },
            {
                "type": "true_false",
                "question": "Python支持面向对象编程。",
                "correct_answer": "对",
                "explanation": "Python完全支持面向对象编程，包括类、继承、多态等特性。",
                "score": 1
            },
            {
                "type": "short_answer",
                "question": "请列举Python的两个主要特点。",
                "correct_answer": "简洁易读、跨平台",
                "explanation": "Python的主要特点包括语法简洁易读、跨平台兼容等。",
                "score": 3
            }
        ]
        
        questions_json = json.dumps(test_questions, ensure_ascii=False)
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 插入考试
        cursor.execute(
            "INSERT INTO exams (title, description, time_limit, questions, created_at) VALUES (?, ?, ?, ?, ?)",
            ("Python基础测试", "Python编程基础知识测试", 45, questions_json, current_time)
        )
        
        exam_id = cursor.lastrowid
        print(f"✓ 考试插入成功，ID: {exam_id}")
        
        # 再插入一个简单的考试
        simple_questions = [
            {
                "type": "single_choice",
                "question": "1+1等于多少？",
                "options": ["1", "2", "3", "4"],
                "correct_answer": "B",
                "explanation": "1+1=2",
                "score": 1
            }
        ]
        
        simple_json = json.dumps(simple_questions, ensure_ascii=False)
        
        cursor.execute(
            "INSERT INTO exams (title, description, time_limit, questions, created_at) VALUES (?, ?, ?, ?, ?)",
            ("简单数学测试", "简单的数学题目", 10, simple_json, current_time)
        )
        
        exam_id_2 = cursor.lastrowid
        print(f"✓ 第二个考试插入成功，ID: {exam_id_2}")
        
        # 提交更改
        conn.commit()
        
        # 验证插入结果
        cursor.execute("SELECT id, title, description, time_limit FROM exams")
        all_exams = cursor.fetchall()
        
        print(f"\n✓ 数据库中现在有 {len(all_exams)} 个考试:")
        for exam in all_exams:
            print(f"  ID: {exam[0]}, 标题: '{exam[1]}', 时间: {exam[3]}分钟")
        
        conn.close()
        
        print("\n🎉 测试考试插入成功！")
        print("现在重新启动考试系统，点击'开始考试'应该能看到这些考试了。")
        
        return True
        
    except Exception as e:
        print(f"❌ 插入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_insert_exam()
