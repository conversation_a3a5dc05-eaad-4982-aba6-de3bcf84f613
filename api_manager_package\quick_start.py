#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动API管理器
"""

import os
import sys

def main():
    print("🚀 快速启动API管理器")
    print("=" * 40)
    
    # 添加当前目录到路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    try:
        print("📦 导入模块...")
        from ui.api_manager_gui import APIManagerGUI
        
        print("🎨 启动GUI界面...")
        app = APIManagerGUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("\n🔧 解决方案:")
        print("1. 确保在正确的目录中运行")
        print("2. 安装依赖: pip install requests")
        print("3. 检查文件完整性")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
