#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试错题添加功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_wrong_question_add():
    """直接测试错题添加"""
    try:
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database_manager import DatabaseManager
        
        print("=== 直接测试错题添加 ===")
        
        db = DatabaseManager()
        db.init_database()  # 确保表已创建
        wq_manager = WrongQuestionManager(db)
        
        # 清空现有错题（用于测试）
        print("清空现有错题...")
        db.execute_update("DELETE FROM wrong_questions")
        
        # 直接添加一道错题
        print("添加测试错题...")
        question_id = wq_manager.add_wrong_question(
            question_text="测试错题：Python中哪个关键字用于定义函数？",
            question_type="single_choice",
            correct_answer="B",
            user_answer="A",
            explanation="Python中使用def关键字来定义函数。"
        )
        print(f"✅ 错题添加成功，ID: {question_id}")
        
        # 验证添加结果
        wrong_questions = wq_manager.get_all_wrong_questions()
        print(f"错题本中共有 {len(wrong_questions)} 道错题")
        
        if wrong_questions:
            wq = wrong_questions[0]
            print("错题信息:")
            print(f"  ID: {wq[0]}")
            print(f"  题目: {wq[1]}")
            print(f"  类型: {wq[2]}")
            print(f"  正确答案: {wq[3]}")
            print(f"  我的答案: {wq[4]}")
            print(f"  解析: {wq[5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接添加测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_add_simulation():
    """模拟批量添加测试"""
    try:
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.utils.database_manager import DatabaseManager

        print("\n=== 模拟批量添加测试 ===")

        db = DatabaseManager()
        db.init_database()
        wq_manager = WrongQuestionManager(db)
        
        # 模拟考试记录
        mock_record = {
            'id': 999,
            'exam_id': 1,
            'answers': {'0': 'A', '1': 'B', '2': '错'},  # 故意答错第1题和第3题
            'score': 1.0,
            'total_score': 3.0,
            'questions': [
                {
                    'type': 'single_choice',
                    'question': '模拟题目1：Python中哪个关键字用于定义函数？',
                    'options': ['function', 'def', 'func', 'define'],
                    'correct_answer': 'B',  # 用户答A，错误
                    'explanation': 'Python中使用def关键字来定义函数。',
                    'score': 1
                },
                {
                    'type': 'single_choice',
                    'question': '模拟题目2：Python是什么类型的编程语言？',
                    'options': ['编译型语言', '解释型语言', '汇编语言', '机器语言'],
                    'correct_answer': 'B',  # 用户答B，正确
                    'explanation': 'Python是一种解释型编程语言。',
                    'score': 1
                },
                {
                    'type': 'true_false',
                    'question': '模拟题目3：Python是开源的编程语言。',
                    'correct_answer': '对',  # 用户答错，错误
                    'explanation': 'Python确实是开源的编程语言。',
                    'score': 1
                }
            ]
        }
        
        print("开始批量添加...")
        added_count = wq_manager.batch_add_wrong_questions_from_exam(mock_record)
        print(f"批量添加完成，添加了 {added_count} 道错题")
        
        # 验证结果
        all_wrong_questions = wq_manager.get_all_wrong_questions()
        print(f"错题本中现在共有 {len(all_wrong_questions)} 道错题")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量添加测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_table():
    """测试数据库表"""
    try:
        from src.utils.database_manager import DatabaseManager

        print("\n=== 测试数据库表 ===")

        db = DatabaseManager()
        db.init_database()
        
        # 检查表是否存在
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name='wrong_questions'"
        result = db.execute_query(query)
        
        if result:
            print("✅ wrong_questions表存在")
            
            # 检查表结构
            query = "PRAGMA table_info(wrong_questions)"
            columns = db.execute_query(query)
            print("表结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            # 检查数据
            query = "SELECT COUNT(*) FROM wrong_questions"
            count_result = db.execute_query(query)
            count = count_result[0][0] if count_result else 0
            print(f"当前记录数: {count}")
            
            if count > 0:
                query = "SELECT * FROM wrong_questions LIMIT 3"
                records = db.execute_query(query)
                print("前3条记录:")
                for record in records:
                    print(f"  ID:{record[0]} - {record[1][:30]}...")
            
        else:
            print("❌ wrong_questions表不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库表测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_exam_and_record():
    """创建测试考试和记录"""
    try:
        from src.core.exam_manager import ExamManager
        from src.utils.database_manager import DatabaseManager
        from datetime import datetime

        print("\n=== 创建测试考试和记录 ===")

        db = DatabaseManager()
        db.init_database()
        exam_manager = ExamManager(db)
        
        # 创建测试考试
        test_questions = [
            {
                'type': 'single_choice',
                'question': '完整测试题目1：Python中哪个关键字用于定义函数？',
                'options': ['function', 'def', 'func', 'define'],
                'correct_answer': 'B',
                'explanation': 'Python中使用def关键字来定义函数。',
                'score': 1
            },
            {
                'type': 'true_false',
                'question': '完整测试题目2：Python是开源的编程语言。',
                'correct_answer': '对',
                'explanation': 'Python确实是开源的编程语言。',
                'score': 1
            }
        ]
        
        exam_id = exam_manager.create_exam(
            title="错题测试专用考试",
            description="用于测试错题功能的考试",
            questions=test_questions,
            time_limit=10
        )
        print(f"✅ 测试考试创建成功，ID: {exam_id}")
        
        # 创建考试记录（模拟答错）
        test_answers = {'0': 'A', '1': '错'}  # 第1题答错，第2题答错
        
        record_id = exam_manager.save_exam_record(
            exam_id=exam_id,
            answers=test_answers,
            score=0.0,
            total_score=2.0,
            start_time=datetime.now(),
            end_time=datetime.now()
        )
        print(f"✅ 考试记录创建成功，ID: {record_id}")
        
        # 获取考试记录
        record = exam_manager.get_exam_record_by_id(record_id)
        if record:
            print("考试记录信息:")
            print(f"  记录ID: {record['id']}")
            print(f"  考试ID: {record['exam_id']}")
            print(f"  答案: {record['answers']}")
            print(f"  题目数量: {len(record['questions'])}")
            
            # 现在测试错题添加
            from src.core.wrong_question_manager import WrongQuestionManager
            wq_manager = WrongQuestionManager(db)
            
            print("\n使用真实考试记录测试错题添加...")
            added_count = wq_manager.batch_add_wrong_questions_from_exam(record)
            print(f"✅ 使用真实记录添加了 {added_count} 道错题")
            
        return exam_id, record_id
        
    except Exception as e:
        print(f"❌ 创建测试考试和记录失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主函数"""
    print("开始直接测试错题功能...\n")
    
    # 测试数据库表
    db_ok = test_database_table()
    
    # 直接添加错题测试
    direct_ok = test_direct_wrong_question_add()
    
    # 批量添加模拟测试
    batch_ok = test_batch_add_simulation()
    
    # 创建完整的考试和记录测试
    exam_id, record_id = create_test_exam_and_record()
    
    print("\n=== 测试总结 ===")
    
    results = [
        ("数据库表", db_ok),
        ("直接添加", direct_ok),
        ("批量添加", batch_ok),
        ("完整流程", exam_id is not None)
    ]
    
    all_passed = True
    for name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 错题功能测试完成！")
        if exam_id and record_id:
            print(f"\n已创建测试数据:")
            print(f"  考试ID: {exam_id}")
            print(f"  记录ID: {record_id}")
            print(f"\n现在您可以:")
            print(f"1. 启动程序")
            print(f"2. 选择'开始考试' → '错题测试专用考试'")
            print(f"3. 或者查看错题本，应该能看到测试错题")
    else:
        print(f"\n⚠️ 发现问题，需要进一步调试")

if __name__ == "__main__":
    main()
