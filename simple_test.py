#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试模块导入"""
    try:
        from src.api.doubao_client import DoubaoClient
        print("✓ DoubaoClient 导入成功")
        
        from src.api.openai_client import OpenAIClient  
        print("✓ OpenAIClient 导入成功")
        
        from src.api.ai_manager import AIManager
        print("✓ AIManager 导入成功")
        
        from src.utils.config_manager import ConfigManager
        print("✓ ConfigManager 导入成功")
        
        # 测试配置管理器
        config = ConfigManager()
        api_config = config.get_api_config()
        print("✓ 配置读取成功")
        print(f"  OpenAI模型: {api_config.get('openai_model', 'N/A')}")
        print(f"  Doubao模型: {api_config.get('doubao_model', 'N/A')}")
        
        # 测试AI管理器
        ai_manager = AIManager(config)
        clients = ai_manager.get_available_clients()
        print(f"✓ 可用AI客户端: {clients}")
        
        # 测试模型获取
        for client_name in clients:
            models = ai_manager.get_available_models(client_name)
            print(f"  {client_name} 可用模型: {models[:3]}...")  # 只显示前3个
        
        print("\n=== 所有测试通过 ===")
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_import()
