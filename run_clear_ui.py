#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清晰版AI考试生成系统启动脚本
专门解决高DPI显示器模糊问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_high_dpi():
    """设置高DPI支持"""
    # 在导入PyQt6之前设置环境变量
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
    os.environ['QT_SCALE_FACTOR'] = '1'
    os.environ['QT_SCREEN_SCALE_FACTORS'] = '1'
    
    # 设置Qt应用程序属性
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    
    # 启用高DPI缩放
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_packages = {
        'PyQt6': 'PyQt6界面框架',
    }
    
    missing_packages = []
    
    for package, description in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package}: {description}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: {description} - 未安装")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖项检查完成！")
    return True

def main():
    """主函数"""
    print("🎨 启动清晰版AI考试生成系统...")
    print("=" * 50)
    
    # 设置高DPI支持
    setup_high_dpi()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print("\n🚀 启动清晰界面...")
    
    try:
        from src.ui.clear_exam_generator import main as run_clear_ui
        run_clear_ui()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n可能的解决方案:")
        print("1. 确保已安装 PyQt6: pip install PyQt6")
        print("2. 检查文件路径是否正确")
        print("3. 查看详细错误信息")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
