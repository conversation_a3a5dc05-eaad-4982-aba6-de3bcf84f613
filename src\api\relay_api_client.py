#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中转API客户端
支持通义千问、智谱AI、Kimi等中转API服务
"""

import requests
import json
import time
from typing import List, Dict, Any
from src.api.base_ai_client import BaseAIClient

class RelayAPIClient(BaseAIClient):
    """中转API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1", 
                 model: str = "qwen-turbo"):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            print(f"🔍 测试中转API连接...")
            print(f"Base URL: {self.base_url}")
            print(f"Model: {self.model}")
            print(f"API Key: {self.api_key[:10]}...{self.api_key[-4:] if len(self.api_key) > 14 else '***'}")

            # 首先测试基础连接
            print(f"📡 测试基础连接到: {self.base_url}")

            # 尝试访问根路径或健康检查
            try:
                base_response = requests.get(self.base_url.rstrip('/v1'), timeout=10)
                print(f"基础连接状态: {base_response.status_code}")
            except Exception as e:
                print(f"⚠️ 基础连接测试: {e}")

            # 发送测试请求
            url = f"{self.base_url}/chat/completions"
            print(f"🚀 发送测试请求到: {url}")

            data = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": "Hello, this is a connection test."}
                ],
                "max_tokens": 10,
                "temperature": 0.1
            }

            print(f"📤 请求数据: {data}")
            print(f"📤 请求头: Authorization: Bearer {self.api_key[:10]}...")

            response = requests.post(url, headers=self.headers, json=data, timeout=30)

            print(f"📥 响应状态码: {response.status_code}")
            print(f"📥 响应头: {dict(response.headers)}")

            if response.status_code == 200:
                result = response.json()
                print(f"📥 响应内容: {str(result)[:300]}...")

                if 'choices' in result and len(result['choices']) > 0:
                    print("✅ 中转API连接测试成功")
                    return True
                else:
                    print("❌ 中转API响应格式异常")
                    print(f"完整响应: {result}")
                    return False
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")

                try:
                    error_detail = response.json()
                    print(f"错误详情: {error_detail}")
                    if 'error' in error_detail:
                        error_msg = error_detail['error'].get('message', '未知错误')
                        print(f"错误消息: {error_msg}")
                except:
                    print(f"无法解析错误响应: {response.text[:500]}")

                return False

        except requests.exceptions.Timeout:
            print("❌ 中转API连接超时 (30秒)")
            print("💡 建议检查: 1) 网络连接 2) 服务器是否正常运行")
            return False
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 中转API连接失败: {e}")
            print("💡 建议检查: 1) URL是否正确 2) 服务器是否可访问 3) 防火墙设置")
            return False
        except Exception as e:
            print(f"❌ 中转API测试出错: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            return False
    
    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        print(f"🔍 正在从API获取模型列表...")
        print(f"API URL: {self.base_url}")
        print(f"请求地址: {self.base_url}/models")

        try:
            url = f"{self.base_url}/models"
            print(f"发送请求到: {url}")
            print(f"请求头: Authorization: Bearer {self.api_key[:10]}...")

            response = requests.get(url, headers=self.headers, timeout=30)
            print(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"响应内容: {str(result)[:200]}...")

                    if 'data' in result and isinstance(result['data'], list):
                        models = []
                        for model in result['data']:
                            if isinstance(model, dict) and 'id' in model:
                                models.append(model['id'])
                            elif isinstance(model, str):
                                models.append(model)

                        if models:
                            print(f"✅ 成功获取到 {len(models)} 个真实模型:")
                            for i, model in enumerate(models[:10]):  # 只显示前10个
                                print(f"  {i+1}. {model}")
                            if len(models) > 10:
                                print(f"  ... 还有 {len(models) - 10} 个模型")
                            return models
                        else:
                            print("⚠️ 模型列表为空")

                    # 尝试其他可能的响应格式
                    elif 'models' in result:
                        models = result['models']
                        if isinstance(models, list):
                            print(f"✅ 从'models'字段获取到 {len(models)} 个模型")
                            return models

                    elif isinstance(result, list):
                        # 直接是模型列表
                        models = [model['id'] if isinstance(model, dict) else str(model) for model in result]
                        print(f"✅ 直接获取到 {len(models)} 个模型")
                        return models

                    else:
                        print(f"⚠️ 未知的响应格式: {list(result.keys()) if isinstance(result, dict) else type(result)}")

                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始响应: {response.text[:500]}")

            else:
                print(f"❌ API请求失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"错误响应: {response.text[:200]}")

        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
        except Exception as e:
            print(f"❌ 获取模型列表出错: {e}")

        print("⚠️ 无法从API获取模型列表，返回预设模型")
        return self._get_default_models()

    def get_models_from_api(self) -> List[str]:
        """专门用于从API获取模型列表的方法"""
        print(f"🚀 开始从API获取真实模型列表...")

        # 针对不同的API服务使用不同的策略
        if "dashscope.aliyuncs.com" in self.base_url:
            return self._get_qwen_models()
        elif "open.bigmodel.cn" in self.base_url:
            return self._get_zhipu_models()
        elif "api.moonshot.cn" in self.base_url:
            return self._get_kimi_models()
        else:
            return self.get_available_models()

    def _get_qwen_models(self) -> List[str]:
        """获取通义千问模型列表"""
        print("🔍 正在获取通义千问模型列表...")

        # 通义千问的模型列表API可能需要特殊处理
        try:
            # 尝试标准的/models端点
            url = f"{self.base_url}/models"
            response = requests.get(url, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                print(f"通义千问API响应: {str(result)[:300]}...")

                if 'data' in result:
                    models = [model['id'] for model in result['data'] if 'id' in model]
                    if models:
                        print(f"✅ 从通义千问API获取到 {len(models)} 个模型")
                        return models

            # 如果标准端点失败，返回已知的通义千问模型
            print("⚠️ 无法从API获取，返回已知的通义千问模型")
            return [
                'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext',
                'qwen-7b-chat', 'qwen-14b-chat', 'qwen-72b-chat',
                'qwen1.5-0.5b-chat', 'qwen1.5-1.8b-chat', 'qwen1.5-4b-chat',
                'qwen1.5-7b-chat', 'qwen1.5-14b-chat', 'qwen1.5-32b-chat', 'qwen1.5-72b-chat',
                'qwen2-0.5b-instruct', 'qwen2-1.5b-instruct', 'qwen2-7b-instruct', 'qwen2-57b-a14b-instruct', 'qwen2-72b-instruct'
            ]

        except Exception as e:
            print(f"❌ 获取通义千问模型失败: {e}")
            return ['qwen-turbo', 'qwen-plus', 'qwen-max']

    def _get_zhipu_models(self) -> List[str]:
        """获取智谱AI模型列表"""
        print("🔍 正在获取智谱AI模型列表...")

        try:
            url = f"{self.base_url}/models"
            response = requests.get(url, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if 'data' in result:
                    models = [model['id'] for model in result['data'] if 'id' in model]
                    if models:
                        print(f"✅ 从智谱AI获取到 {len(models)} 个模型")
                        return models

            print("⚠️ 无法从API获取，返回已知的智谱AI模型")
            return ['glm-4', 'glm-4v', 'glm-3-turbo', 'chatglm3-6b', 'chatglm2-6b']

        except Exception as e:
            print(f"❌ 获取智谱AI模型失败: {e}")
            return ['glm-4', 'glm-4v', 'glm-3-turbo']

    def _get_kimi_models(self) -> List[str]:
        """获取Kimi模型列表"""
        print("🔍 正在获取Kimi模型列表...")

        try:
            url = f"{self.base_url}/models"
            response = requests.get(url, headers=self.headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if 'data' in result:
                    models = [model['id'] for model in result['data'] if 'id' in model]
                    if models:
                        print(f"✅ 从Kimi API获取到 {len(models)} 个模型")
                        return models

            print("⚠️ 无法从API获取，返回已知的Kimi模型")
            return ['moonshot-v1-8k', 'moonshot-v1-32k', 'moonshot-v1-128k']

        except Exception as e:
            print(f"❌ 获取Kimi模型失败: {e}")
            return ['moonshot-v1-8k', 'moonshot-v1-32k', 'moonshot-v1-128k']

    def _get_default_models(self) -> List[str]:
        """获取默认模型列表"""
        return [
            # 通义千问
            'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext',
            # 智谱AI
            'glm-4', 'glm-4v', 'glm-3-turbo',
            # Kimi
            'moonshot-v1-8k', 'moonshot-v1-32k', 'moonshot-v1-128k',
            # 通用
            'gpt-4', 'gpt-3.5-turbo'
        ]
    
    def generate_questions(self, material: str, question_types: List[str], 
                          num_questions: int = 20) -> List[Dict[str, Any]]:
        """使用中转API生成题目"""
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                print(f"🚀 使用中转API生成题目 (尝试 {attempt + 1}/{max_retries})")
                print(f"模型: {self.model}")
                print(f"题目数量: {num_questions}")
                
                # 创建提示词
                prompt = self.create_prompt_for_questions(material, question_types, num_questions)
                
                # 发送请求
                url = f"{self.base_url}/chat/completions"
                data = {
                    "model": self.model,
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 4000
                }
                
                response = requests.post(url, headers=self.headers, json=data, timeout=60)
                
                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content']
                        print(f"✓ 中转API调用成功，返回内容长度: {len(content)}")
                        
                        # 解析题目
                        questions = self.parse_questions_response(content)
                        validated_questions = self.validate_questions(questions)
                        
                        print(f"✓ 成功生成 {len(validated_questions)} 道题目")
                        return validated_questions
                    else:
                        raise Exception("API响应格式异常：缺少choices字段")
                else:
                    error_msg = f"API请求失败: {response.status_code}"
                    try:
                        error_detail = response.json()
                        if 'error' in error_detail:
                            error_msg += f" - {error_detail['error'].get('message', '')}"
                    except:
                        error_msg += f" - {response.text[:200]}"
                    
                    if attempt < max_retries - 1:
                        print(f"中转API请求失败，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                        continue
                    else:
                        raise Exception(error_msg)
                        
            except requests.exceptions.Timeout as e:
                if attempt < max_retries - 1:
                    print(f"中转API连接超时，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"中转API连接超时，请检查网络连接: {str(e)}")
            except requests.exceptions.ConnectionError as e:
                if attempt < max_retries - 1:
                    print(f"中转API连接错误，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"中转API连接失败，请检查网络连接: {str(e)}")
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"中转API请求出错，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"中转API生成题目失败: {str(e)}")
        
        return []
    
    def evaluate_answer(self, question: str, answer: str, correct_answer: str) -> Dict[str, Any]:
        """评估答案"""
        try:
            prompt = f"""
请评估以下答案的正确性：

题目：{question}
标准答案：{correct_answer}
学生答案：{answer}

请给出评分（0-10分）和详细反馈，返回JSON格式：
{{"score": 分数, "max_score": 10, "feedback": "详细反馈", "suggestions": "改进建议"}}
"""
            
            url = f"{self.base_url}/chat/completions"
            data = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 500
            }
            
            response = requests.post(url, headers=self.headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    
                    # 尝试解析JSON
                    try:
                        evaluation = json.loads(content)
                        return evaluation
                    except:
                        # 如果解析失败，返回默认评估
                        return {
                            "score": 5,
                            "max_score": 10,
                            "feedback": content,
                            "suggestions": "请参考标准答案进行改进"
                        }
                
        except Exception as e:
            return {
                "score": 0,
                "max_score": 10,
                "is_correct": False,
                "feedback": f"评估失败: {str(e)}",
                "suggestions": "请检查网络连接或API配置"
            }
