-- 修复错题表结构
-- 在SQLite中执行

-- 检查当前表结构
.schema wrong_questions

-- 添加新字段（如果不存在）
ALTER TABLE wrong_questions ADD COLUMN exam_id INTEGER;
ALTER TABLE wrong_questions ADD COLUMN exam_title TEXT;
ALTER TABLE wrong_questions ADD COLUMN exam_record_id INTEGER;

-- 查看修复后的表结构
.schema wrong_questions

-- 添加一些测试错题
INSERT INTO wrong_questions (question_text, question_type, correct_answer, user_answer, explanation, exam_title, created_at) VALUES
('测试错题1：Python中哪个关键字用于定义函数？', 'single_choice', 'def', 'function', 'Python中使用def关键字来定义函数。', 'Python基础测试', datetime('now')),
('测试错题2：Python是编译型语言吗？', 'true_false', '否', '是', 'Python是解释型语言，不是编译型语言。', 'Python基础测试', datetime('now')),
('测试错题3：列表和元组的区别是什么？', 'short_answer', '列表可变，元组不可变', '不知道', '列表是可变的，元组是不可变的数据结构。', 'Python数据结构', datetime('now'));

-- 查看添加结果
SELECT COUNT(*) as '错题总数' FROM wrong_questions;
SELECT id, question_text, exam_title, created_at FROM wrong_questions ORDER BY created_at DESC LIMIT 5;
