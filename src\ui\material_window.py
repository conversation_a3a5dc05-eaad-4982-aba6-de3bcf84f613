#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
材料管理窗口
提供材料导入、查看、编辑、删除等功能的用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
from src.core.material_manager import MaterialManager

class MaterialWindow:
    def __init__(self, parent, db_manager):
        """初始化材料管理窗口"""
        self.parent = parent
        self.db = db_manager
        self.material_manager = MaterialManager(db_manager)
        
        self.window = tk.Toplevel(parent)
        self.window.title("材料管理")
        self.window.geometry("900x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.refresh_material_list()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar, text="导入文本", command=self.import_text).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="导入PDF", command=self.import_pdf).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="刷新", command=self.refresh_material_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除", command=self.delete_material).pack(side=tk.LEFT, padx=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(toolbar)
        search_frame.pack(side=tk.RIGHT)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 5))
        search_entry.bind('<Return>', self.search_materials)
        ttk.Button(search_frame, text="搜索", command=self.search_materials).pack(side=tk.LEFT)
        
        # 分割面板
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧材料列表
        left_frame = ttk.LabelFrame(paned, text="材料列表", padding=5)
        paned.add(left_frame, weight=1)
        
        # 材料列表
        columns = ('ID', '标题', '类型', '创建时间')
        self.tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.tree.heading(col, text=col)
            if col == 'ID':
                self.tree.column(col, width=50)
            elif col == '类型':
                self.tree.column(col, width=80)
            elif col == '创建时间':
                self.tree.column(col, width=150)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_material_select)
        
        # 右侧内容预览
        right_frame = ttk.LabelFrame(paned, text="内容预览", padding=5)
        paned.add(right_frame, weight=2)
        
        # 内容显示区域
        self.content_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, state=tk.DISABLED)
        self.content_text.pack(fill=tk.BOTH, expand=True)
        
        # 底部按钮
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="编辑", command=self.edit_material).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出", command=self.export_material).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def import_text(self):
        """导入文本文件"""
        file_path = filedialog.askopenfilename(
            title="选择文本文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                self.material_manager.import_text_file(file_path)
                messagebox.showinfo("成功", "文本文件导入成功！")
                self.refresh_material_list()
            except Exception as e:
                messagebox.showerror("错误", str(e))
    
    def import_pdf(self):
        """导入PDF文件"""
        file_path = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                self.material_manager.import_pdf_file(file_path)
                messagebox.showinfo("成功", "PDF文件导入成功！")
                self.refresh_material_list()
            except Exception as e:
                messagebox.showerror("错误", str(e))
    
    def refresh_material_list(self):
        """刷新材料列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 获取材料列表
        materials = self.material_manager.get_all_materials()
        
        for material in materials:
            self.tree.insert('', tk.END, values=material)
    
    def search_materials(self, event=None):
        """搜索材料"""
        keyword = self.search_var.get().strip()
        
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if keyword:
            materials = self.material_manager.search_materials(keyword)
        else:
            materials = self.material_manager.get_all_materials()
        
        for material in materials:
            self.tree.insert('', tk.END, values=material)
    
    def on_material_select(self, event):
        """材料选择事件"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            material_id = item['values'][0]
            
            # 获取材料内容
            material = self.material_manager.get_material_by_id(material_id)
            if material:
                self.content_text.config(state=tk.NORMAL)
                self.content_text.delete(1.0, tk.END)
                self.content_text.insert(1.0, material[2])  # content字段
                self.content_text.config(state=tk.DISABLED)
    
    def delete_material(self):
        """删除材料"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的材料！")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的材料吗？"):
            item = self.tree.item(selection[0])
            material_id = item['values'][0]
            
            try:
                self.material_manager.delete_material(material_id)
                messagebox.showinfo("成功", "材料删除成功！")
                self.refresh_material_list()
                
                # 清空内容预览
                self.content_text.config(state=tk.NORMAL)
                self.content_text.delete(1.0, tk.END)
                self.content_text.config(state=tk.DISABLED)
                
            except Exception as e:
                messagebox.showerror("错误", f"删除材料失败: {str(e)}")
    
    def edit_material(self):
        """编辑材料"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要编辑的材料！")
            return
        
        item = self.tree.item(selection[0])
        material_id = item['values'][0]
        material = self.material_manager.get_material_by_id(material_id)
        
        if material:
            EditMaterialDialog(self.window, self.material_manager, material, self.refresh_material_list)
    
    def export_material(self):
        """导出材料"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要导出的材料！")
            return
        
        item = self.tree.item(selection[0])
        material_id = item['values'][0]
        material = self.material_manager.get_material_by_id(material_id)
        
        if material:
            file_path = filedialog.asksaveasfilename(
                title="保存材料",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if file_path:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(material[2])  # content字段
                    messagebox.showinfo("成功", "材料导出成功！")
                except Exception as e:
                    messagebox.showerror("错误", f"导出失败: {str(e)}")


class EditMaterialDialog:
    def __init__(self, parent, material_manager, material, refresh_callback):
        """编辑材料对话框"""
        self.material_manager = material_manager
        self.material = material
        self.refresh_callback = refresh_callback
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("编辑材料")
        self.dialog.geometry("600x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题输入
        ttk.Label(main_frame, text="标题:").pack(anchor=tk.W)
        self.title_var = tk.StringVar(value=self.material[1])
        title_entry = ttk.Entry(main_frame, textvariable=self.title_var, width=50)
        title_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 内容编辑
        ttk.Label(main_frame, text="内容:").pack(anchor=tk.W)
        self.content_text = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD)
        self.content_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.content_text.insert(1.0, self.material[2])
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="保存", command=self.save_material).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)
    
    def save_material(self):
        """保存材料"""
        title = self.title_var.get().strip()
        content = self.content_text.get(1.0, tk.END).strip()
        
        if not title:
            messagebox.showwarning("警告", "请输入标题！")
            return
        
        if not content:
            messagebox.showwarning("警告", "请输入内容！")
            return
        
        try:
            self.material_manager.update_material(self.material[0], title, content)
            messagebox.showinfo("成功", "材料更新成功！")
            self.refresh_callback()
            self.dialog.destroy()
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")
