# 自定义模型功能使用说明

## 🎯 功能概述

现在您可以在考试系统中自定义添加任何模型名称，无需依赖API自动获取。这样您就可以根据官网文档手动添加最新的模型。

## 🚀 使用方法

### 1. 添加单个自定义模型

1. 打开**系统设置** → **API设置** → **中转API**
2. 在"自定义模型"输入框中输入模型名称
3. 点击**"添加到列表"**按钮
4. 模型会自动添加到下拉列表并设为当前选择
5. 设置会自动保存

**示例模型名称**：
- `qwen-max-longcontext`
- `glm-4v`
- `moonshot-v1-128k`
- `claude-3-opus`

### 2. 批量添加预设模型

1. 点击**"管理模型"**按钮
2. 在弹出的管理窗口中点击**"添加预设模型"**
3. 系统会自动添加常用的热门模型：
   - **通义千问系列**：qwen-turbo, qwen-plus, qwen-max, qwen2-72b-instruct 等
   - **智谱AI系列**：glm-4, glm-4v, glm-3-turbo 等
   - **Kimi系列**：moonshot-v1-8k, moonshot-v1-32k, moonshot-v1-128k
   - **Claude系列**：claude-3-haiku, claude-3-sonnet, claude-3-opus
   - **GPT系列**：gpt-3.5-turbo, gpt-4, gpt-4-turbo, gpt-4o
   - **其他模型**：deepseek-chat, yi-34b-chat 等

### 3. 管理现有模型

1. 点击**"管理模型"**按钮
2. 在列表中选择要删除的模型
3. 点击**"删除选中"**按钮
4. 确认删除

### 4. 重置为默认

1. 点击**"重置为默认"**按钮
2. 确认操作
3. 所有自定义模型将被清除，恢复为系统默认模型

## 📝 常用模型名称参考

### 通义千问 (Qwen)
```
qwen-turbo
qwen-plus  
qwen-max
qwen-max-longcontext
qwen2-72b-instruct
qwen2-7b-instruct
qwen2-1.5b-instruct
qwen1.5-72b-chat
qwen1.5-32b-chat
qwen1.5-14b-chat
qwen1.5-7b-chat
```

### 智谱AI (GLM)
```
glm-4
glm-4v
glm-3-turbo
chatglm3-6b
chatglm2-6b
```

### Kimi (Moonshot)
```
moonshot-v1-8k
moonshot-v1-32k
moonshot-v1-128k
```

### Claude
```
claude-3-haiku
claude-3-sonnet
claude-3-opus
claude-3-5-sonnet
```

### GPT系列
```
gpt-3.5-turbo
gpt-4
gpt-4-turbo
gpt-4o
gpt-4o-mini
```

### 其他热门模型
```
deepseek-chat
deepseek-coder
yi-34b-chat
yi-6b-chat
baichuan2-13b-chat
chatglm-6b
```

## 💡 使用建议

1. **查看官方文档**：添加模型前，请查看对应API服务商的官方文档，确认模型名称的准确性

2. **测试连接**：添加模型后，建议点击"测试连接"按钮验证配置是否正确

3. **保存配置**：自定义模型会自动保存到配置文件，下次启动时会自动加载

4. **模型命名**：请使用官方准确的模型名称，避免拼写错误

## 🔧 技术说明

- 自定义模型保存在 `config/config.json` 文件中
- 支持任意数量的自定义模型
- 模型列表按添加顺序排列
- 删除模型不会影响其他配置

## ❓ 常见问题

**Q: 添加的模型无法使用怎么办？**
A: 请检查：
1. 模型名称是否正确（区分大小写）
2. API Key是否有该模型的使用权限
3. Base URL是否正确配置

**Q: 如何知道某个API支持哪些模型？**
A: 建议查看对应服务商的官方文档或API文档

**Q: 可以添加多少个自定义模型？**
A: 理论上没有限制，但建议保持在合理数量以便管理

## 🎉 开始使用

现在就打开系统设置，尝试添加您需要的模型吧！
