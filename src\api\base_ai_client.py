#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI客户端基类
定义AI API的通用接口
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseAIClient(ABC):
    """AI客户端基类"""
    
    def __init__(self, api_key: str, **kwargs):
        """初始化AI客户端"""
        self.api_key = api_key
        self.config = kwargs
    
    @abstractmethod
    def generate_questions(self, material: str, question_types: List[str], 
                          num_questions: int = 20) -> List[Dict[str, Any]]:
        """
        生成题目
        
        Args:
            material: 学习材料内容
            question_types: 题目类型列表 ['single_choice', 'true_false', 'short_answer', 'case_analysis']
            num_questions: 题目数量
            
        Returns:
            题目列表，每个题目包含：
            {
                'type': 题目类型,
                'question': 题目内容,
                'options': 选项列表（选择题用）,
                'correct_answer': 正确答案,
                'explanation': 解析,
                'score': 分值
            }
        """
        pass
    
    @abstractmethod
    def evaluate_answer(self, question: str, answer: str, correct_answer: str) -> Dict[str, Any]:
        """
        评估答案（主要用于主观题）
        
        Args:
            question: 题目内容
            answer: 学生答案
            correct_answer: 参考答案
            
        Returns:
            评估结果：
            {
                'score': 得分,
                'max_score': 满分,
                'feedback': 反馈意见
            }
        """
        pass
    
    def create_prompt_for_questions(self, material: str, question_types: List[str], 
                                   num_questions: int) -> str:
        """创建生成题目的提示词"""
        type_descriptions = {
            'single_choice': '单选题（4个选项，只有一个正确答案）',
            'multiple_choice': '多选题（4个选项，可能有多个正确答案）',
            'true_false': '判断题（对或错）',
            'short_answer': '简答题（需要简短回答）',
            'case_analysis': '案例分析题（需要详细分析）'
        }
        
        types_text = "、".join([type_descriptions.get(t, t) for t in question_types])
        
        prompt = f"""
请根据以下学习材料生成{num_questions}道题目，题目类型包括：{types_text}

学习材料：
{material}

要求：
1. 题目要紧密结合材料内容
2. 难度适中，既不过于简单也不过于困难
3. 每道题目都要有详细的解析
4. 选择题的选项要有一定的迷惑性
5. 题目分布要均匀，各种类型都要有

请按照以下JSON格式返回：
[
    {{
        "type": "题目类型",
        "question": "题目内容",
        "options": ["选项A", "选项B", "选项C", "选项D"],  // 仅选择题需要
        "correct_answer": "正确答案",
        "explanation": "详细解析",
        "score": 分值
    }}
]

注意：
- single_choice和multiple_choice需要options字段
- true_false的correct_answer应该是"对"或"错"
- short_answer和case_analysis不需要options字段
- 每题默认分值为1分，案例分析题可以是5分
"""
        return prompt
    
    def parse_questions_response(self, response: str) -> List[Dict[str, Any]]:
        """解析AI返回的题目数据"""
        import json
        import re

        print(f"🔍 开始解析AI响应，长度: {len(response)}")
        print(f"响应前100字符: {response[:100]}...")

        try:
            # 清理响应内容
            cleaned_response = response.strip()

            # 尝试直接解析JSON
            if cleaned_response.startswith('['):
                print("📝 尝试直接解析JSON数组")
                result = json.loads(cleaned_response)
                print(f"✅ 直接解析成功: {len(result)} 道题目")
                return result

            # 尝试提取JSON数组
            json_match = re.search(r'\[.*\]', cleaned_response, re.DOTALL)
            if json_match:
                print("📝 尝试提取JSON数组")
                json_str = json_match.group()
                result = json.loads(json_str)
                print(f"✅ 提取解析成功: {len(result)} 道题目")
                return result

            # 尝试提取单个JSON对象
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                print("📝 尝试提取单个JSON对象")
                json_str = json_match.group()
                result = json.loads(json_str)
                print("✅ 单对象解析成功，转换为数组")
                return [result]

            # 如果无法解析，尝试简单的文本解析
            print("⚠️ JSON解析失败，尝试文本解析")
            return self._parse_text_response(cleaned_response)

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            return self._parse_text_response(response)
        except Exception as e:
            print(f"❌ 解析异常: {e}")
            return []

    def _parse_text_response(self, response: str) -> List[Dict[str, Any]]:
        """从文本响应中解析题目"""
        print("📝 尝试文本解析模式")

        # 简单的文本解析逻辑
        questions = []
        lines = response.split('\n')

        current_question = {}
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测题目开始
            if '题目' in line or '问题' in line:
                if current_question:
                    questions.append(current_question)
                current_question = {
                    'type': 'single_choice',
                    'question': line,
                    'options': [],
                    'correct_answer': 'A',
                    'explanation': '暂无解析',
                    'score': 1
                }
            elif line.startswith(('A.', 'B.', 'C.', 'D.', 'A、', 'B、', 'C、', 'D、')):
                if current_question:
                    option_text = line[2:].strip()
                    current_question['options'].append(option_text)

        if current_question:
            questions.append(current_question)

        print(f"📝 文本解析结果: {len(questions)} 道题目")
        return questions
    
    def validate_questions(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证和修正题目数据"""
        valid_questions = []

        print(f"🔍 开始验证 {len(questions)} 道题目...")

        for i, q in enumerate(questions, 1):
            print(f"验证题目 {i}: {q.get('question', '无题目')[:30]}...")

            # 检查必需字段
            required_fields = ['type', 'question', 'correct_answer']
            missing_fields = [field for field in required_fields if field not in q]

            if missing_fields:
                print(f"❌ 题目 {i} 缺少必需字段: {missing_fields}")
                continue

            # 设置默认值
            if 'explanation' not in q:
                q['explanation'] = '暂无解析'
                print(f"📝 题目 {i} 添加默认解析")

            if 'score' not in q:
                q['score'] = 5 if q['type'] == 'case_analysis' else 1
                print(f"📝 题目 {i} 添加默认分值: {q['score']}")

            # 验证选择题的选项
            if q['type'] in ['single_choice', 'multiple_choice']:
                options = q.get('options', [])
                print(f"🔍 题目 {i} 选项检查: {options}")

                if not options:
                    print(f"❌ 题目 {i} 选择题缺少选项，尝试生成默认选项")
                    # 为选择题生成默认选项
                    q['options'] = ["选项A", "选项B", "选项C", "选项D"]
                    print(f"📝 题目 {i} 添加默认选项")
                elif not isinstance(options, list):
                    print(f"❌ 题目 {i} 选项格式错误: {type(options)}")
                    q['options'] = ["选项A", "选项B", "选项C", "选项D"]
                    print(f"📝 题目 {i} 修正选项格式")
                elif len(options) < 2:
                    print(f"❌ 题目 {i} 选项不足: {len(options)} < 2")
                    # 补充选项到4个
                    while len(q['options']) < 4:
                        q['options'].append(f"选项{chr(65 + len(q['options']))}")
                    print(f"📝 题目 {i} 补充选项到 {len(q['options'])} 个")
                else:
                    print(f"✅ 题目 {i} 选项正常: {len(options)} 个")

            print(f"✅ 题目 {i} 验证通过")
            valid_questions.append(q)

        print(f"🎉 验证完成: {len(valid_questions)}/{len(questions)} 道题目有效")
        return valid_questions
