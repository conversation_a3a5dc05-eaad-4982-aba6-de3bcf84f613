#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建API管理器包的脚本
运行此脚本将在当前目录创建完整的 api_manager_package 文件夹
"""

import os
import json

def create_directory_structure():
    """创建目录结构"""
    base_dir = "api_manager_package"
    
    directories = [
        base_dir,
        f"{base_dir}/core",
        f"{base_dir}/ui", 
        f"{base_dir}/examples",
        f"{base_dir}/config",
        f"{base_dir}/tests"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 创建目录: {directory}")

def create_files():
    """创建所有文件"""
    
    files_content = {
        # 根目录文件
        "api_manager_package/__init__.py": '''"""
通用API管理系统
从Kaoshi考试系统提取的完整API管理功能
"""

from .core.universal_api_manager import UniversalAPIManager, APIProvider

__version__ = "1.0.0"
__all__ = ['UniversalAPIManager', 'APIProvider']
''',

        "api_manager_package/README.md": '''# 🚀 通用API管理系统

从Kaoshi考试系统提取的完整API管理功能，支持多种AI服务提供商。

## ✨ 功能特性

- 🔌 **多API支持**: OpenAI、DeepSeek、Gemini、豆包、通义千问等
- 🔄 **自动检测**: 自动获取可用模型列表  
- 🧪 **连接测试**: 实时测试API连接状态
- 💾 **配置管理**: 支持配置保存和加载
- 🎨 **GUI界面**: 专业的可视化管理界面
- 🔧 **易于扩展**: 模块化设计，易于添加新的API

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests
```

### 2. 基础使用
```python
from api_manager_package.core.universal_api_manager import UniversalAPIManager

# 创建API管理器
api_manager = UniversalAPIManager()

# 添加自定义API
api_manager.add_custom_provider(
    name="my_api",
    display_name="我的API",
    base_url="https://api.example.com/v1",
    api_key="your_api_key",
    test_model="gpt-3.5-turbo"
)

# 测试连接
success = api_manager.test_provider_connection("my_api")
print(f"连接测试: {'成功' if success else '失败'}")
```

### 3. GUI使用
```python
python api_manager_package/quick_start.py
```
''',

        "api_manager_package/requirements.txt": '''# 通用API管理系统依赖

# 核心依赖
requests>=2.25.0

# 可选依赖 (GUI)
# tkinter  # Python内置，无需安装
''',

        "api_manager_package/quick_start.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动API管理器
"""

import os
import sys

def main():
    print("🚀 快速启动API管理器")
    print("=" * 40)
    
    # 添加当前目录到路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    try:
        print("📦 导入模块...")
        from ui.api_manager_gui import APIManagerGUI
        
        print("🎨 启动GUI界面...")
        app = APIManagerGUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("\\n🔧 解决方案:")
        print("1. 确保在正确的目录中运行")
        print("2. 安装依赖: pip install requests")
        print("3. 检查文件完整性")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
''',
    }
    
    # 创建核心管理器文件（简化版）
    files_content["api_manager_package/core/__init__.py"] = '''from .universal_api_manager import UniversalAPIManager, APIProvider

__all__ = ['UniversalAPIManager', 'APIProvider']
'''

    files_content["api_manager_package/core/universal_api_manager.py"] = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用API管理器 - 从Kaoshi考试系统提取
支持多种API提供商和自动模型检测
"""

import requests
import json
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class APIProvider:
    """API提供商配置"""
    name: str
    display_name: str
    base_url: str
    api_key: str
    api_type: str  # openai, claude, gemini, custom
    headers: Dict[str, str]
    models: List[str]
    test_model: str  # 用于测试的默认模型
    active: bool = True
    last_tested: Optional[str] = None
    status: str = "未测试"  # 未测试, 正常, 异常
    error_message: str = ""

class UniversalAPIManager:
    """通用API管理器"""
    
    def __init__(self):
        self.providers: Dict[str, APIProvider] = {}
        self.load_default_providers()
    
    def load_default_providers(self):
        """加载默认API提供商"""
        
        # OpenAI官方
        self.add_provider(APIProvider(
            name="openai_official",
            display_name="OpenAI官方",
            base_url="https://api.openai.com/v1",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=[],
            test_model="gpt-3.5-turbo"
        ))
        
        # DeepSeek
        self.add_provider(APIProvider(
            name="deepseek",
            display_name="DeepSeek",
            base_url="https://api.deepseek.com/v1",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=[],
            test_model="deepseek-chat"
        ))
        
        # 豆包API
        self.add_provider(APIProvider(
            name="doubao",
            display_name="豆包API",
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=["doubao-seed-1-6-flash-250615"],
            test_model="doubao-seed-1-6-flash-250615"
        ))
    
    def add_provider(self, provider: APIProvider):
        """添加API提供商"""
        self.providers[provider.name] = provider
    
    def add_custom_provider(self, name: str, display_name: str, base_url: str, 
                           api_key: str, api_type: str = "openai", test_model: str = ""):
        """添加自定义API提供商"""
        
        headers = {"Content-Type": "application/json"}
        if api_key and api_type == "openai":
            headers["Authorization"] = f"Bearer {api_key}"
        
        provider = APIProvider(
            name=name,
            display_name=display_name,
            base_url=base_url.rstrip('/'),
            api_key=api_key,
            api_type=api_type,
            headers=headers,
            models=[],
            test_model=test_model or "gpt-3.5-turbo"
        )
        
        self.add_provider(provider)
        return provider
    
    def test_provider_connection(self, name: str) -> bool:
        """测试API提供商连接"""
        if name not in self.providers:
            return False
        
        provider = self.providers[name]
        
        try:
            return self._test_openai_connection(provider)
        except Exception as e:
            provider.status = "异常"
            provider.error_message = str(e)
            provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return False
    
    def _test_openai_connection(self, provider: APIProvider) -> bool:
        """测试OpenAI兼容API连接"""
        
        headers = provider.headers.copy()
        if provider.api_key:
            headers["Authorization"] = f"Bearer {provider.api_key}"
        
        # 测试聊天接口
        chat_url = f"{provider.base_url}/chat/completions"
        
        test_data = {
            "model": provider.test_model,
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        try:
            response = requests.post(chat_url, headers=headers, json=test_data, timeout=30)
            
            if response.status_code == 200:
                provider.status = "正常"
                provider.error_message = ""
                provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                return True
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                provider.status = "异常"
                provider.error_message = error_msg
                provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                return False
                
        except Exception as e:
            provider.status = "异常"
            provider.error_message = str(e)
            provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return False
    
    def save_config(self, config_path: str):
        """保存配置到文件"""
        config_data = {}
        for name, provider in self.providers.items():
            config_data[name] = {
                'display_name': provider.display_name,
                'base_url': provider.base_url,
                'api_key': provider.api_key,
                'api_type': provider.api_type,
                'test_model': provider.test_model,
                'active': provider.active,
                'models': provider.models
            }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    def load_config(self, config_path: str):
        """从文件加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            for name, config in config_data.items():
                if name in self.providers:
                    # 更新现有提供商
                    provider = self.providers[name]
                    for key, value in config.items():
                        if hasattr(provider, key):
                            setattr(provider, key, value)
                            
        except FileNotFoundError:
            print("配置文件不存在，使用默认配置")
        except Exception as e:
            print(f"加载配置失败: {e}")
'''

    # 创建简化的GUI文件
    files_content["api_manager_package/ui/__init__.py"] = '''from .api_manager_gui import APIManagerGUI

__all__ = ['APIManagerGUI']
'''

    files_content["api_manager_package/ui/api_manager_gui.py"] = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API管理器GUI界面 - 简化版
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.universal_api_manager import UniversalAPIManager

class APIManagerGUI:
    def __init__(self):
        self.api_manager = UniversalAPIManager()
        self.root = tk.Tk()
        self.root.title("🚀 通用API管理器")
        self.root.geometry("600x400")
        
        self.setup_ui()
        self.refresh_provider_list()
    
    def setup_ui(self):
        """设置用户界面"""
        
        # 主标题
        title_label = ttk.Label(self.root, text="🚀 通用API管理器", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 提供商列表
        frame = ttk.LabelFrame(self.root, text="API提供商列表")
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Treeview
        columns = ("名称", "状态", "URL")
        self.tree = ttk.Treeview(frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        self.tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="➕ 添加API", command=self.add_api).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧪 测试连接", command=self.test_api).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 刷新", command=self.refresh_provider_list).pack(side=tk.LEFT, padx=5)
    
    def refresh_provider_list(self):
        """刷新提供商列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加提供商
        for name, provider in self.api_manager.providers.items():
            status_icon = "✅" if provider.status == "正常" else "❌" if provider.status == "异常" else "⚪"
            status_text = f"{status_icon} {provider.status}"
            
            self.tree.insert("", tk.END, values=(
                provider.display_name,
                status_text,
                provider.base_url
            ), tags=(name,))
    
    def add_api(self):
        """添加API（简化版）"""
        messagebox.showinfo("提示", "请使用代码方式添加自定义API\\n\\n示例：\\napi_manager.add_custom_provider(\\n    name='my_api',\\n    display_name='我的API',\\n    base_url='https://api.example.com/v1',\\n    api_key='your_key'\\n)")
    
    def test_api(self):
        """测试API"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个API提供商")
            return
        
        item = self.tree.item(selection[0])
        provider_name = item['tags'][0]
        provider = self.api_manager.providers[provider_name]
        
        if not provider.api_key:
            messagebox.showwarning("警告", f"{provider.display_name} 未配置API密钥")
            return
        
        messagebox.showinfo("提示", f"开始测试 {provider.display_name}...")
        success = self.api_manager.test_provider_connection(provider_name)
        
        if success:
            messagebox.showinfo("成功", f"{provider.display_name} 连接测试成功！")
        else:
            messagebox.showerror("失败", f"{provider.display_name} 连接测试失败\\n\\n{provider.error_message}")
        
        self.refresh_provider_list()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = APIManagerGUI()
    app.run()
'''

    # examples目录文件
    files_content["api_manager_package/examples/basic_usage.py"] = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.universal_api_manager import UniversalAPIManager

def main():
    print("🚀 API管理系统基础示例")
    print("=" * 50)
    
    # 创建管理器
    api_manager = UniversalAPIManager()
    
    # 显示默认提供商
    print("\\n📋 默认API提供商:")
    for name, provider in api_manager.providers.items():
        print(f"  • {provider.display_name} ({name})")
        print(f"    URL: {provider.base_url}")
        print(f"    类型: {provider.api_type}")
        print(f"    状态: {provider.status}")
        print()
    
    print("✅ 示例完成")

if __name__ == "__main__":
    main()
'''
    
    # 创建配置文件
    config_data = {
        "openai_official": {
            "display_name": "OpenAI官方",
            "base_url": "https://api.openai.com/v1",
            "api_key": "",
            "api_type": "openai",
            "test_model": "gpt-3.5-turbo",
            "active": True,
            "models": []
        },
        "deepseek": {
            "display_name": "DeepSeek",
            "base_url": "https://api.deepseek.com/v1",
            "api_key": "",
            "api_type": "openai", 
            "test_model": "deepseek-chat",
            "active": True,
            "models": []
        }
    }
    
    files_content["api_manager_package/config/default_providers.json"] = json.dumps(config_data, ensure_ascii=False, indent=2)
    
    # 创建所有文件
    for file_path, content in files_content.items():
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"📄 创建文件: {file_path}")

def main():
    print("🚀 创建API管理器包")
    print("=" * 40)
    
    try:
        print("📁 创建目录结构...")
        create_directory_structure()
        
        print("\n📄 创建文件...")
        create_files()
        
        print("\n✅ API管理器包创建完成！")
        print("\n📁 生成的目录结构:")
        print("api_manager_package/")
        print("├── core/                    # 核心模块")
        print("├── ui/                      # 用户界面")
        print("├── examples/                # 使用示例")
        print("├── config/                  # 配置文件")
        print("├── tests/                   # 测试文件")
        print("├── README.md               # 说明文档")
        print("├── requirements.txt        # 依赖包")
        print("└── quick_start.py         # 快速启动")
        
        print("\n🚀 使用方法:")
        print("1. cd api_manager_package")
        print("2. python quick_start.py")
        print("3. 或运行 python examples/basic_usage.py")
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()