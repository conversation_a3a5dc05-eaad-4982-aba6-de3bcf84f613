-- 添加测试错题到数据库
-- 在data/exam_system.db中执行

-- 清空现有错题
DELETE FROM wrong_questions;

-- 添加测试错题
INSERT INTO wrong_questions (question_text, question_type, correct_answer, user_answer, explanation, is_favorite, created_at) VALUES
('测试错题1：Python中哪个关键字用于定义函数？', 'single_choice', 'B', 'A', 'Python中使用def关键字来定义函数，而不是function。', 0, datetime('now')),
('测试错题2：Python是开源的编程语言。', 'true_false', '对', '错', 'Python确实是开源的编程语言，由Python软件基金会维护。', 1, datetime('now')),
('测试错题3：列表和元组的主要区别是什么？', 'short_answer', '列表可变，元组不可变', '不知道', '列表(list)是可变的数据结构，可以修改元素；元组(tuple)是不可变的数据结构，创建后不能修改。', 0, datetime('now')),
('测试错题4：以下哪个是Python的Web框架？', 'single_choice', 'C', 'A', 'Django是Python最流行的Web框架之一，Flask也是常用的轻量级框架。', 0, datetime('now')),
('测试错题5：Python支持面向对象编程。', 'true_false', '对', '错', 'Python完全支持面向对象编程，包括类、继承、多态等特性。', 1, datetime('now'));

-- 查询验证
SELECT COUNT(*) as '错题总数' FROM wrong_questions;
SELECT * FROM wrong_questions;
