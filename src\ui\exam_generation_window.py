#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
试卷生成窗口
提供AI生成试卷的用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import json
from src.api.optimized_question_generator import OptimizedQuestionGenerator

class ExamGenerationWindow:
    def __init__(self, parent, material_manager, ai_manager, exam_manager):
        """初始化试卷生成窗口"""
        self.parent = parent
        self.material_manager = material_manager
        self.ai_manager = ai_manager
        self.exam_manager = exam_manager
        
        self.window = tk.Toplevel(parent)
        self.window.title("生成试卷")
        self.window.geometry("900x800")  # 增加窗口大小
        self.window.transient(parent)
        self.window.grab_set()

        # 设置最小窗口大小
        self.window.minsize(800, 600)
        
        self.setup_ui()
        self.load_materials()
        self.load_ai_clients()

    def on_material_selected(self, event=None):
        """材料选择事件处理"""
        try:
            selected_material = self.material_var.get()
            if selected_material:
                # 提取材料标题（格式：ID - 标题）
                material_title = selected_material.split(' - ', 1)[1] if ' - ' in selected_material else selected_material

                # 生成试卷标题
                exam_title = f"{material_title} - 测试"

                # 更新试卷标题
                self.title_var.set(exam_title)

                print(f"📝 自动设置试卷标题: {exam_title}")

        except Exception as e:
            print(f"⚠️ 设置试卷标题失败: {e}")
            # 不影响正常使用，继续执行
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="AI试卷生成", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 配置框架
        config_frame = ttk.LabelFrame(main_frame, text="生成配置", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：材料选择和AI选择
        row1 = ttk.Frame(config_frame)
        row1.pack(fill=tk.X, pady=(0, 10))
        
        # 材料选择
        ttk.Label(row1, text="选择材料:").pack(side=tk.LEFT)
        self.material_var = tk.StringVar()
        self.material_combo = ttk.Combobox(row1, textvariable=self.material_var, width=30, state="readonly")
        self.material_combo.pack(side=tk.LEFT, padx=(5, 20))

        # 绑定材料选择事件
        self.material_combo.bind('<<ComboboxSelected>>', self.on_material_selected)
        
        # AI选择
        ttk.Label(row1, text="AI服务:").pack(side=tk.LEFT)
        self.ai_var = tk.StringVar()
        self.ai_combo = ttk.Combobox(row1, textvariable=self.ai_var, width=15, state="readonly")
        self.ai_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.ai_combo.bind('<<ComboboxSelected>>', self.on_ai_change)

        # 模型选择
        ttk.Label(row1, text="模型:").pack(side=tk.LEFT)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(row1, textvariable=self.model_var, width=20, state="readonly")
        self.model_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # 第二行：试卷信息
        row2 = ttk.Frame(config_frame)
        row2.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(row2, text="试卷标题:").pack(side=tk.LEFT)
        self.title_var = tk.StringVar(value="AI生成试卷")
        title_entry = ttk.Entry(row2, textvariable=self.title_var, width=30)
        title_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row2, text="考试时间(分钟):").pack(side=tk.LEFT)
        self.time_var = tk.StringVar(value="60")
        time_entry = ttk.Entry(row2, textvariable=self.time_var, width=10)
        time_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # 第三行：题目配置
        row3 = ttk.Frame(config_frame)
        row3.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(row3, text="总题目数:").pack(side=tk.LEFT)
        self.total_questions_var = tk.StringVar(value="10")  # 默认减少到10题
        total_entry = ttk.Entry(row3, textvariable=self.total_questions_var, width=10)
        total_entry.pack(side=tk.LEFT, padx=(5, 10))

        # Token优化提示
        self.token_label = ttk.Label(row3, text="💡 建议10题以下", foreground="orange", font=("Arial", 9))
        self.token_label.pack(side=tk.LEFT, padx=(5, 0))

        # 绑定数量变化事件
        self.total_questions_var.trace('w', self.on_questions_change)
        
        # 优化选项
        optimization_frame = ttk.LabelFrame(config_frame, text="🚀 Token优化选项", padding=10)
        optimization_frame.pack(fill=tk.X, pady=(10, 0))

        self.use_optimization = tk.BooleanVar(value=True)
        ttk.Checkbutton(optimization_frame, text="启用智能优化 (减少90%+ Token消耗)",
                       variable=self.use_optimization).pack(anchor=tk.W)

        self.batch_generation = tk.BooleanVar(value=True)
        ttk.Checkbutton(optimization_frame, text="分批生成 (大量题目时推荐)",
                       variable=self.batch_generation).pack(anchor=tk.W)

        self.use_knowledge_points = tk.BooleanVar(value=True)
        ttk.Checkbutton(optimization_frame, text="知识点提取模式 (提取关键点而非完整材料)",
                       variable=self.use_knowledge_points).pack(anchor=tk.W)

        # 题目类型选择
        types_frame = ttk.LabelFrame(config_frame, text="题目类型", padding=10)
        types_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.question_types = {}
        supported_types = self.ai_manager.get_supported_question_types()
        
        for i, type_info in enumerate(supported_types):
            var = tk.BooleanVar(value=True if type_info['type'] in ['single_choice', 'true_false'] else False)
            self.question_types[type_info['type']] = var
            
            cb = ttk.Checkbutton(types_frame, text=type_info['name'], variable=var)
            cb.grid(row=i//3, column=i%3, sticky=tk.W, padx=10, pady=2)
        
        # 生成按钮
        generate_btn = ttk.Button(config_frame, text="生成试卷", command=self.generate_exam)
        generate_btn.pack(pady=(20, 0))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(10, 0))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.pack(pady=(5, 0))
        
        # 预览框架
        preview_frame = ttk.LabelFrame(main_frame, text="试卷预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        self.preview_text = scrolledtext.ScrolledText(preview_frame, wrap=tk.WORD, state=tk.DISABLED, height=15)
        self.preview_text.pack(fill=tk.BOTH, expand=True)

        # 按钮框架 - 固定在底部
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0), side=tk.BOTTOM)

        # 保存按钮 - 使用更醒目的样式
        self.save_btn = ttk.Button(button_frame, text="💾 保存试卷", command=self.save_exam, state=tk.DISABLED)
        self.save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 重新生成按钮
        self.regenerate_btn = ttk.Button(button_frame, text="🔄 重新生成", command=self.regenerate_exam, state=tk.DISABLED)
        self.regenerate_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 关闭按钮
        ttk.Button(button_frame, text="❌ 关闭", command=self.window.destroy).pack(side=tk.RIGHT)
        
        # 存储生成的题目
        self.generated_questions = []

        # 存储当前生成参数，用于重新生成
        self.current_generation_params = None

        # 初始化优化生成器
        self.optimizer = None

    def on_questions_change(self, *args):
        """题目数量变化处理"""
        try:
            num_questions = int(self.total_questions_var.get())

            if num_questions <= 5:
                self.token_label.config(text="💚 Token消耗很少", foreground="green")
            elif num_questions <= 10:
                self.token_label.config(text="💛 Token消耗适中", foreground="orange")
            elif num_questions <= 20:
                self.token_label.config(text="🧡 Token消耗较多", foreground="red")
            else:
                self.token_label.config(text="❤️ Token消耗很大，建议分批", foreground="red")

        except ValueError:
            self.token_label.config(text="💡 请输入有效数字", foreground="gray")
    
    def load_materials(self):
        """加载材料列表"""
        materials = self.material_manager.get_all_materials()
        material_list = [f"{mat[0]} - {mat[1]}" for mat in materials]
        self.material_combo['values'] = material_list
        
        if material_list:
            self.material_combo.current(0)
    
    def load_ai_clients(self):
        """加载AI客户端列表"""
        clients = self.ai_manager.get_available_clients()
        if clients:
            self.ai_combo['values'] = clients
            self.ai_combo.current(0)
            self.load_models()  # 加载第一个客户端的模型
        else:
            messagebox.showwarning("警告", "没有配置可用的AI客户端！\n请在设置中配置API密钥。")

    def on_ai_change(self, event=None):
        """AI服务改变事件"""
        self.load_models()

    def load_models(self):
        """加载当前AI服务的可用模型"""
        ai_service = self.ai_var.get()
        if not ai_service:
            return

        models = self.ai_manager.get_available_models(ai_service)
        if models:
            self.model_combo['values'] = models
            # 设置当前模型
            current_model = self.ai_manager.get_client_model(ai_service)
            if current_model and current_model in models:
                self.model_combo.set(current_model)
            else:
                self.model_combo.current(0)
        else:
            self.model_combo['values'] = []
            self.model_combo.set("")
    
    def generate_exam(self):
        """生成试卷"""
        # 验证输入
        if not self.material_var.get():
            messagebox.showwarning("警告", "请选择学习材料！")
            return
        
        if not self.ai_var.get():
            messagebox.showwarning("警告", "请选择AI服务！")
            return

        if not self.model_var.get():
            messagebox.showwarning("警告", "请选择模型！")
            return
        
        # 获取选中的题目类型
        selected_types = [t for t, var in self.question_types.items() if var.get()]
        if not selected_types:
            messagebox.showwarning("警告", "请至少选择一种题目类型！")
            return
        
        try:
            total_questions = int(self.total_questions_var.get())
            if total_questions <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的题目数量！")
            return
        
        # 获取材料内容
        material_id = int(self.material_var.get().split(' - ')[0])
        material = self.material_manager.get_material_by_id(material_id)
        
        if not material:
            messagebox.showerror("错误", "无法获取材料内容！")
            return
        
        # 设置选择的模型
        ai_service = self.ai_var.get()
        selected_model = self.model_var.get()
        if selected_model:
            self.ai_manager.set_client_model(ai_service, selected_model)

        # 保存生成参数，用于重新生成
        self.current_generation_params = {
            'material_content': material[2],
            'question_types': selected_types,
            'num_questions': total_questions,
            'ai_service': ai_service,
            'model': selected_model
        }

        # 在新线程中生成试卷
        self.start_generation(material[2], selected_types, total_questions)
    
    def start_generation(self, material_content, question_types, num_questions):
        """开始生成试卷"""
        self.progress.start()
        self.status_label.config(text="正在生成试卷...")
        
        # 禁用生成按钮
        for widget in self.window.winfo_children():
            self.disable_widget_recursive(widget)
        
        # 在新线程中执行生成
        thread = threading.Thread(
            target=self.generate_questions_thread,
            args=(material_content, question_types, num_questions)
        )
        thread.daemon = True
        thread.start()
    
    def disable_widget_recursive(self, widget):
        """递归禁用控件"""
        try:
            widget.config(state=tk.DISABLED)
        except:
            pass
        
        for child in widget.winfo_children():
            self.disable_widget_recursive(child)
    
    def enable_widget_recursive(self, widget):
        """递归启用控件"""
        try:
            # 跳过进度条和某些只读控件
            widget_class = widget.__class__.__name__
            if widget_class not in ['Progressbar', 'Text']:
                widget.config(state=tk.NORMAL)
        except:
            pass

        for child in widget.winfo_children():
            self.enable_widget_recursive(child)
    
    def generate_questions_thread(self, material_content, question_types, num_questions):
        """在线程中生成题目"""
        try:
            # 检查是否使用优化模式
            if self.use_optimization.get():
                questions = self._generate_with_optimization(material_content, question_types, num_questions)
            else:
                # 传统生成方式
                questions = self.ai_manager.generate_questions(
                    material=material_content,
                    question_types=question_types,
                    num_questions=num_questions,
                    client_name=self.ai_var.get()
                )

            # 在主线程中更新UI
            self.window.after(0, self.on_generation_complete, questions)

        except Exception as e:
            self.window.after(0, self.on_generation_error, str(e))

    def _generate_with_optimization(self, material_content, question_types, num_questions):
        """使用优化模式生成题目"""

        print(f"🚀 使用优化模式生成 {num_questions} 道题目...")

        # 策略1: 材料压缩
        if self.use_knowledge_points.get() and len(material_content) > 1000:
            # 简单的材料压缩：取前1000字符 + 关键段落
            compressed_material = self._compress_material(material_content)
            print(f"📝 材料压缩: {len(material_content)} → {len(compressed_material)} 字符")
        else:
            compressed_material = material_content

        # 策略2: 分批生成
        if self.batch_generation.get() and num_questions > 8:
            return self._batch_generate_questions(compressed_material, question_types, num_questions)
        else:
            # 直接生成
            return self.ai_manager.generate_questions(
                material=compressed_material,
                question_types=question_types,
                num_questions=num_questions,
                client_name=self.ai_var.get()
            )

    def _compress_material(self, material: str) -> str:
        """压缩材料内容"""
        if len(material) <= 1000:
            return material

        # 简单压缩策略：取开头500字 + 结尾500字
        start_part = material[:500]
        end_part = material[-500:]

        # 如果中间有重要关键词，也包含进来
        keywords = ['重要', '关键', '核心', '主要', '基本', '原理', '方法', '特点']
        middle_sentences = []

        sentences = material[500:-500].split('。')
        for sentence in sentences[:10]:  # 最多取10个中间句子
            if any(keyword in sentence for keyword in keywords):
                middle_sentences.append(sentence)

        middle_part = '。'.join(middle_sentences[:3])  # 最多3个关键句子

        if middle_part:
            compressed = f"{start_part}...{middle_part}...{end_part}"
        else:
            compressed = f"{start_part}...{end_part}"

        return compressed

    def _batch_generate_questions(self, material: str, question_types: list, num_questions: int) -> list:
        """分批生成题目"""

        batch_size = 5  # 每批5题
        all_questions = []

        print(f"📦 分批生成模式: {num_questions}题 → {(num_questions + batch_size - 1) // batch_size}批")

        for i in range(0, num_questions, batch_size):
            batch_num = min(batch_size, num_questions - i)

            print(f"正在生成第{i+1}-{i+batch_num}题...")

            try:
                batch_questions = self.ai_manager.generate_questions(
                    material=material,
                    question_types=question_types,
                    num_questions=batch_num,
                    client_name=self.ai_var.get()
                )

                if batch_questions:
                    all_questions.extend(batch_questions)
                    print(f"✅ 第{i+1}-{i+batch_num}题生成完成")
                else:
                    print(f"⚠️ 第{i+1}-{i+batch_num}题生成失败")

            except Exception as e:
                print(f"❌ 批次生成失败: {e}")
                # 继续下一批
                continue

        print(f"🎉 分批生成完成，共生成 {len(all_questions)} 道题目")
        return all_questions
    
    def on_generation_complete(self, questions):
        """生成完成回调"""
        self.progress.stop()
        self.status_label.config(text=f"生成完成，共{len(questions)}道题目")

        # 保存生成的题目
        self.generated_questions = questions

        # 显示预览
        self.show_preview(questions)

        # 启用控件（除了进度条）
        for widget in self.window.winfo_children():
            self.enable_widget_recursive(widget)

        # 确保保存和重新生成按钮启用
        self.save_btn.config(state=tk.NORMAL)
        self.regenerate_btn.config(state=tk.NORMAL)

        print(f"试卷生成成功，共{len(questions)}道题目，保存按钮已启用")
        print("请点击'💾 保存试卷'按钮来保存试卷到数据库")

        # 显示保存提示
        from tkinter import messagebox
        messagebox.showinfo("生成完成", f"🎉 试卷生成成功！\n\n📊 共生成 {len(questions)} 道题目\n\n💾 请点击下方的'保存试卷'按钮来保存试卷到数据库\n🔄 如果不满意，可以点击'重新生成'按钮")
    
    def on_generation_error(self, error_msg):
        """生成错误回调"""
        self.progress.stop()
        self.status_label.config(text="生成失败")

        # 启用控件
        for widget in self.window.winfo_children():
            self.enable_widget_recursive(widget)

        # 根据错误类型提供不同的解决建议
        if "timeout" in error_msg.lower() or "超时" in error_msg:
            error_detail = f"生成试卷失败：{error_msg}\n\n可能的解决方案：\n" \
                          "1. 检查网络连接是否稳定\n" \
                          "2. 尝试减少题目数量\n" \
                          "3. 稍后重试\n" \
                          "4. 尝试使用其他AI服务"
        elif "连接" in error_msg or "connection" in error_msg.lower():
            error_detail = f"生成试卷失败：{error_msg}\n\n可能的解决方案：\n" \
                          "1. 检查网络连接\n" \
                          "2. 检查API密钥是否正确\n" \
                          "3. 检查API端点配置\n" \
                          "4. 尝试使用VPN或代理"
        else:
            error_detail = f"生成试卷失败：{error_msg}\n\n建议：\n" \
                          "1. 检查API配置是否正确\n" \
                          "2. 确认账户余额充足\n" \
                          "3. 尝试使用其他AI服务"

        messagebox.showerror("生成失败", error_detail)
    
    def show_preview(self, questions):
        """显示试卷预览"""
        self.preview_text.config(state=tk.NORMAL)
        self.preview_text.delete(1.0, tk.END)
        
        # 显示试卷标题
        self.preview_text.insert(tk.END, f"{self.title_var.get()}\n")
        self.preview_text.insert(tk.END, f"考试时间：{self.time_var.get()}分钟\n")
        self.preview_text.insert(tk.END, f"总题数：{len(questions)}题\n")
        self.preview_text.insert(tk.END, "="*50 + "\n\n")
        
        # 显示题目
        for i, question in enumerate(questions, 1):
            self.preview_text.insert(tk.END, f"{i}. {question['question']}\n")
            
            if question['type'] in ['single_choice', 'multiple_choice']:
                for j, option in enumerate(question.get('options', []), 1):
                    self.preview_text.insert(tk.END, f"   {chr(64+j)}. {option}\n")
            
            self.preview_text.insert(tk.END, f"   答案：{question['correct_answer']}\n")
            self.preview_text.insert(tk.END, f"   解析：{question.get('explanation', '暂无解析')}\n\n")
        
        self.preview_text.config(state=tk.DISABLED)
    
    def save_exam(self):
        """保存试卷"""
        if not self.generated_questions:
            messagebox.showwarning("警告", "没有可保存的试卷！")
            return

        try:
            title = self.title_var.get().strip()
            if not title:
                title = "AI生成试卷"

            time_limit = int(self.time_var.get())
            description = f"使用{self.ai_var.get()}生成的试卷，共{len(self.generated_questions)}道题目"

            print(f"准备保存试卷: {title}")
            print(f"题目数量: {len(self.generated_questions)}")
            print(f"时间限制: {time_limit}分钟")

            # 验证题目格式
            for i, q in enumerate(self.generated_questions):
                if not isinstance(q, dict):
                    raise ValueError(f"题目{i+1}格式错误")
                if 'question' not in q or 'correct_answer' not in q:
                    raise ValueError(f"题目{i+1}缺少必要字段")

            print("题目格式验证通过")

            # 保存到数据库
            exam_id = self.exam_manager.create_exam(
                title=title,
                description=description,
                questions=self.generated_questions,
                time_limit=time_limit
            )

            print(f"试卷保存成功，ID: {exam_id}")

            # 验证保存结果
            saved_exam = self.exam_manager.get_exam_by_id(exam_id)
            if saved_exam:
                print("保存验证成功")
                messagebox.showinfo("成功", f"试卷保存成功！\n\n试卷ID：{exam_id}\n标题：{title}\n题目数量：{len(self.generated_questions)}")
            else:
                print("保存验证失败")
                messagebox.showwarning("警告", "试卷可能未正确保存，请检查数据库")

            self.window.destroy()

        except ValueError as e:
            print(f"数据验证错误: {str(e)}")
            messagebox.showerror("数据错误", f"保存试卷失败：{str(e)}")
        except Exception as e:
            print(f"保存试卷失败: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"保存试卷失败：{str(e)}\n\n请检查控制台输出获取详细信息")

    def regenerate_exam(self):
        """重新生成试卷"""
        if not self.current_generation_params:
            messagebox.showwarning("警告", "没有可重新生成的参数！")
            return

        # 确认重新生成
        if not messagebox.askyesno("确认", "确定要重新生成试卷吗？当前内容将被覆盖。"):
            return

        params = self.current_generation_params

        # 重新设置模型
        if params.get('ai_service') and params.get('model'):
            self.ai_manager.set_client_model(params['ai_service'], params['model'])

        # 开始重新生成
        self.start_generation(
            params['material_content'],
            params['question_types'],
            params['num_questions']
        )
