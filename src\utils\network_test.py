#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接测试工具
用于诊断API连接问题
"""

import requests
import time
import socket
from urllib.parse import urlparse

def test_dns_resolution(hostname):
    """测试DNS解析"""
    try:
        ip = socket.gethostbyname(hostname)
        print(f"✓ DNS解析成功: {hostname} -> {ip}")
        return True
    except socket.gaierror as e:
        print(f"✗ DNS解析失败: {hostname} - {e}")
        return False

def test_tcp_connection(hostname, port=443):
    """测试TCP连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(f"✓ TCP连接成功: {hostname}:{port}")
            return True
        else:
            print(f"✗ TCP连接失败: {hostname}:{port}")
            return False
    except Exception as e:
        print(f"✗ TCP连接出错: {hostname}:{port} - {e}")
        return False

def test_http_request(url, timeout=30):
    """测试HTTP请求"""
    try:
        start_time = time.time()
        response = requests.get(url, timeout=timeout)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"✓ HTTP请求成功: {url}")
        print(f"  状态码: {response.status_code}")
        print(f"  响应时间: {duration:.2f}秒")
        return True
        
    except requests.exceptions.Timeout:
        print(f"✗ HTTP请求超时: {url}")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"✗ HTTP连接失败: {url} - {e}")
        return False
    except Exception as e:
        print(f"✗ HTTP请求出错: {url} - {e}")
        return False

def test_doubao_api_connectivity():
    """测试豆包API连接性"""
    print("=== 豆包API连接测试 ===\n")
    
    # 测试目标
    hostname = "ark.cn-beijing.volces.com"
    port = 443
    base_url = f"https://{hostname}"
    api_endpoint = f"{base_url}/api/v3"
    
    # 1. DNS解析测试
    print("1. DNS解析测试")
    dns_ok = test_dns_resolution(hostname)
    print()
    
    # 2. TCP连接测试
    print("2. TCP连接测试")
    tcp_ok = test_tcp_connection(hostname, port)
    print()
    
    # 3. HTTP连接测试
    print("3. HTTP连接测试")
    http_ok = test_http_request(base_url, timeout=10)
    print()
    
    # 4. API端点测试
    print("4. API端点测试")
    api_ok = test_http_request(api_endpoint, timeout=10)
    print()
    
    # 总结
    print("=== 测试总结 ===")
    if dns_ok and tcp_ok:
        print("✓ 基础网络连接正常")
        if not http_ok or not api_ok:
            print("⚠ HTTP连接可能有问题，建议:")
            print("  - 检查防火墙设置")
            print("  - 检查代理配置")
            print("  - 尝试使用VPN")
    else:
        print("✗ 基础网络连接有问题，建议:")
        print("  - 检查网络连接")
        print("  - 检查DNS设置")
        print("  - 联系网络管理员")
    
    return dns_ok and tcp_ok and http_ok and api_ok

def test_api_with_retry(api_key, model="doubao-seed-1-6-flash-250615"):
    """使用重试机制测试API"""
    print("\n=== API调用测试 ===")
    
    endpoint = "https://ark.cn-beijing.volces.com/api/v3"
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "你好"
            }
        ],
        "max_tokens": 10
    }
    
    max_retries = 3
    retry_delay = 2
    
    for attempt in range(max_retries):
        try:
            print(f"尝试 {attempt + 1}/{max_retries}...")
            
            response = requests.post(
                f"{endpoint}/chat/completions",
                headers=headers,
                json=data,
                timeout=(10, 60)
            )
            
            if response.status_code == 200:
                print("✓ API调用成功!")
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"  响应内容: {content}")
                return True
            else:
                print(f"✗ API调用失败: {response.status_code}")
                print(f"  错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"✗ 第{attempt + 1}次尝试超时")
        except requests.exceptions.ConnectionError as e:
            print(f"✗ 第{attempt + 1}次连接失败: {e}")
        except Exception as e:
            print(f"✗ 第{attempt + 1}次请求出错: {e}")
        
        if attempt < max_retries - 1:
            print(f"  {retry_delay}秒后重试...")
            time.sleep(retry_delay)
            retry_delay *= 2
    
    print("✗ 所有重试均失败")
    return False

if __name__ == "__main__":
    # 测试网络连接
    connectivity_ok = test_doubao_api_connectivity()
    
    if connectivity_ok:
        # 如果网络连接正常，测试API调用
        api_key = "2f62a42c-6ea7-4c2e-8b51-9d2e93f6e9c6"  # 您的API密钥
        test_api_with_retry(api_key)
    else:
        print("\n网络连接有问题，请先解决网络问题再测试API调用")
