#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试选择窗口
让用户选择要参加的考试
"""

import tkinter as tk
from tkinter import ttk, messagebox
from src.ui.exam_window import ExamWindow

class ExamSelectionWindow:
    def __init__(self, parent, exam_manager, wrong_question_manager):
        """初始化考试选择窗口"""
        self.parent = parent
        self.exam_manager = exam_manager
        self.wrong_question_manager = wrong_question_manager
        
        self.window = tk.Toplevel(parent)
        self.window.title("选择考试")
        self.window.geometry("700x500")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_exams()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        ttk.Label(main_frame, text="选择考试", font=("Arial", 16, "bold")).pack(pady=(0, 20))
        
        # 考试列表框架
        list_frame = ttk.LabelFrame(main_frame, text="可用考试", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建树形视图
        columns = ('ID', '标题', '描述', '时间限制', '创建时间')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.tree.heading(col, text=col)
            if col == 'ID':
                self.tree.column(col, width=50)
            elif col == '时间限制':
                self.tree.column(col, width=80)
            elif col == '创建时间':
                self.tree.column(col, width=150)
            elif col == '描述':
                self.tree.column(col, width=200)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 双击开始考试
        self.tree.bind('<Double-Button-1>', self.on_double_click)
        
        # 详情框架
        detail_frame = ttk.LabelFrame(main_frame, text="考试详情", padding=10)
        detail_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.detail_text = tk.Text(detail_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_exam_select)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="开始考试", command=self.start_exam).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="预览试卷", command=self.preview_exam).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除考试", command=self.delete_exam).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="刷新", command=self.load_exams).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def load_exams(self):
        """加载考试列表"""
        try:
            print("正在加载考试列表...")

            # 清空现有项目
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 获取考试列表
            exams = self.exam_manager.get_all_exams()
            print(f"从数据库获取到 {len(exams)} 个考试")

            for i, exam in enumerate(exams):
                print(f"考试 {i+1}: ID={exam[0]}, 标题='{exam[1]}'")
                self.tree.insert('', tk.END, values=exam)

            if not exams:
                print("没有找到任何考试")
                self.detail_text.config(state=tk.NORMAL)
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.insert(1.0, "暂无可用考试。\n\n请先使用'生成试卷'功能创建考试。")
                self.detail_text.config(state=tk.DISABLED)
            else:
                print("考试列表加载完成")

        except Exception as e:
            print(f"加载考试列表失败: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"加载考试列表失败：{str(e)}")
    
    def on_exam_select(self, event):
        """考试选择事件"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            exam_id = item['values'][0]
            
            # 获取考试详情
            exam_data = self.exam_manager.get_exam_by_id(exam_id)
            if exam_data:
                self.show_exam_details(exam_data)
    
    def show_exam_details(self, exam_data):
        """显示考试详情"""
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        
        # 统计题目类型
        type_count = {}
        total_score = 0
        
        for question in exam_data['questions']:
            q_type = question['type']
            score = question.get('score', 1)
            
            if q_type not in type_count:
                type_count[q_type] = {'count': 0, 'score': 0}
            
            type_count[q_type]['count'] += 1
            type_count[q_type]['score'] += score
            total_score += score
        
        # 显示详情
        details = f"考试标题：{exam_data['title']}\n"
        details += f"考试描述：{exam_data.get('description', '无')}\n"
        details += f"考试时间：{exam_data['time_limit']}分钟\n"
        details += f"题目总数：{len(exam_data['questions'])}题\n"
        details += f"总分：{total_score}分\n\n"
        
        details += "题目分布：\n"
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        
        for q_type, info in type_count.items():
            type_name = type_names.get(q_type, q_type)
            details += f"  {type_name}：{info['count']}题，{info['score']}分\n"
        
        self.detail_text.insert(1.0, details)
        self.detail_text.config(state=tk.DISABLED)
    
    def on_double_click(self, event):
        """双击开始考试"""
        self.start_exam()
    
    def start_exam(self):
        """开始考试"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个考试！")
            return
        
        item = self.tree.item(selection[0])
        exam_id = item['values'][0]
        
        # 获取考试数据
        exam_data = self.exam_manager.get_exam_by_id(exam_id)
        if not exam_data:
            messagebox.showerror("错误", "无法获取考试数据！")
            return
        
        # 最终确认
        if not messagebox.askyesno("🎯 开始考试",
                                 f"确定开始考试吗？\n开始后将无法暂停或重新开始！"):
            return

        # 关闭选择窗口
        self.window.destroy()

        # 直接启动传统考试界面（简化版）
        try:
            from src.ui.exam_window import ExamWindow
            ExamWindow(self.parent, self.exam_manager, self.wrong_question_manager, exam_data)
            print("📝 启动考试界面")
        except Exception as e:
            print(f"❌ 考试界面启动失败: {e}")
            messagebox.showerror("错误", f"考试界面启动失败：{str(e)}")
            return


    
    def preview_exam(self):
        """预览试卷"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个考试！")
            return
        
        item = self.tree.item(selection[0])
        exam_id = item['values'][0]
        
        # 获取考试数据
        exam_data = self.exam_manager.get_exam_by_id(exam_id)
        if not exam_data:
            messagebox.showerror("错误", "无法获取考试数据！")
            return
        
        # 打开预览窗口
        ExamPreviewWindow(self.window, exam_data)
    
    def delete_exam(self):
        """删除考试"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个考试！")
            return
        
        item = self.tree.item(selection[0])
        exam_id = item['values'][0]
        exam_title = item['values'][1]
        
        if messagebox.askyesno("确认删除", f"确定要删除考试'{exam_title}'吗？\n\n此操作将同时删除相关的考试记录，且无法恢复！"):
            try:
                self.exam_manager.delete_exam(exam_id)
                messagebox.showinfo("成功", "考试删除成功！")
                self.load_exams()
            except Exception as e:
                messagebox.showerror("错误", f"删除考试失败：{str(e)}")


class ExamPreviewWindow:
    def __init__(self, parent, exam_data):
        """考试预览窗口"""
        self.exam_data = exam_data
        
        self.window = tk.Toplevel(parent)
        self.window.title(f"预览 - {exam_data['title']}")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.show_preview()
    
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        ttk.Label(main_frame, text=f"试卷预览 - {self.exam_data['title']}", 
                 font=("Arial", 14, "bold")).pack(pady=(0, 10))
        
        # 预览文本
        self.preview_text = tk.Text(main_frame, wrap=tk.WORD, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=scrollbar.set)
        
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 关闭按钮
        ttk.Button(main_frame, text="关闭", command=self.window.destroy).pack(pady=(10, 0))
    
    def show_preview(self):
        """显示预览内容"""
        self.preview_text.config(state=tk.NORMAL)
        self.preview_text.delete(1.0, tk.END)
        
        # 试卷标题信息
        content = f"{self.exam_data['title']}\n"
        content += f"考试时间：{self.exam_data['time_limit']}分钟\n"
        content += f"总题数：{len(self.exam_data['questions'])}题\n"
        content += "="*60 + "\n\n"
        
        # 显示题目
        type_names = {
            'single_choice': '单选题',
            'multiple_choice': '多选题',
            'true_false': '判断题',
            'short_answer': '简答题',
            'case_analysis': '案例分析题'
        }
        
        for i, question in enumerate(self.exam_data['questions'], 1):
            type_name = type_names.get(question['type'], question['type'])
            score = question.get('score', 1)
            
            content += f"{i}. ({type_name}, {score}分) {question['question']}\n"
            
            if question['type'] in ['single_choice', 'multiple_choice']:
                for j, option in enumerate(question.get('options', []), 1):
                    content += f"   {chr(64+j)}. {option}\n"
            
            content += f"   答案：{question['correct_answer']}\n"
            
            explanation = question.get('explanation', '暂无解析')
            content += f"   解析：{explanation}\n\n"
        
        self.preview_text.insert(1.0, content)
        self.preview_text.config(state=tk.DISABLED)
