#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试错题添加功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wrong_question_add():
    """测试错题添加"""
    print("=== 测试错题添加功能 ===")
    
    try:
        from src.core.wrong_question_manager import WrongQuestionManager
        from src.core.exam_manager import ExamManager
        from src.utils.database_manager import DatabaseManager
        
        # 初始化管理器
        db = DatabaseManager()
        wq_manager = WrongQuestionManager(db)
        exam_manager = ExamManager(db)
        
        print("✅ 管理器初始化成功")
        
        # 1. 测试直接添加错题
        print("\n1. 测试直接添加错题...")
        try:
            question_id = wq_manager.add_wrong_question(
                question_text="测试错题：Python的创始人是谁？",
                question_type="single_choice",
                correct_answer="<PERSON> van <PERSON>",
                user_answer="<PERSON>",
                explanation="Python是由Guido van <PERSON>创建的。",
                exam_id=1,
                exam_title="Python基础测试",
                exam_record_id=1
            )
            print(f"✅ 直接添加错题成功，ID: {question_id}")
        except Exception as e:
            print(f"❌ 直接添加错题失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 2. 测试批量添加错题
        print("\n2. 测试批量添加错题...")
        
        # 创建模拟考试记录
        mock_exam_record = {
            'id': 999,
            'exam_id': 999,
            'exam_title': '模拟测试试卷',
            'questions': [
                {
                    'type': 'single_choice',
                    'question': '模拟题目1：Python是什么类型的语言？',
                    'options': ['编译型', '解释型', '汇编型', '机器型'],
                    'correct_answer': 'B',
                    'explanation': 'Python是解释型语言',
                    'score': 1
                },
                {
                    'type': 'true_false',
                    'question': '模拟题目2：Python支持面向对象编程',
                    'correct_answer': '对',
                    'explanation': 'Python完全支持面向对象编程',
                    'score': 1
                },
                {
                    'type': 'single_choice',
                    'question': '模拟题目3：以下哪个是Python的关键字？',
                    'options': ['function', 'def', 'method', 'class_def'],
                    'correct_answer': 'B',
                    'explanation': 'def是Python中定义函数的关键字',
                    'score': 1
                }
            ],
            'answers': {
                '0': 'A',  # 错误答案，正确是B
                '1': '错',  # 错误答案，正确是对
                '2': 'B'   # 正确答案
            }
        }
        
        print(f"模拟考试记录包含 {len(mock_exam_record['questions'])} 道题目")
        print(f"模拟答案: {mock_exam_record['answers']}")
        
        try:
            added_count = wq_manager.batch_add_wrong_questions_from_exam(mock_exam_record)
            print(f"✅ 批量添加完成，添加了 {added_count} 道错题")
        except Exception as e:
            print(f"❌ 批量添加失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 3. 验证添加结果
        print("\n3. 验证添加结果...")
        try:
            all_questions = wq_manager.get_all_wrong_questions()
            print(f"错题本中共有 {len(all_questions)} 道错题")
            
            if all_questions:
                print("最新的错题:")
                for i, q in enumerate(all_questions[-3:], 1):  # 显示最后3道
                    print(f"  {i}. {q[1][:50]}...")
                    print(f"     正确答案: {q[3]}, 我的答案: {q[4]}")
                    if len(q) > 9 and q[9]:  # 检查是否有试卷信息
                        print(f"     来源试卷: {q[9]}")
            else:
                print("错题本为空")
                
        except Exception as e:
            print(f"❌ 验证结果失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. 测试按试卷分组
        print("\n4. 测试按试卷分组...")
        try:
            exam_summary = wq_manager.get_exam_wrong_question_summary()
            print(f"按试卷分组结果:")
            for exam in exam_summary:
                print(f"  试卷: {exam[0]}, 错题数: {exam[1]}, 收藏数: {exam[2]}")
        except Exception as e:
            print(f"❌ 按试卷分组失败: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n🎉 测试完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_wrong_question_add()
