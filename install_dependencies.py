#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖包自动安装脚本
"""

import sys
import subprocess
import importlib

def run_pip_command(command):
    """运行pip命令"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name, description=""):
    """安装单个包"""
    print(f"📦 安装 {package_name}...")
    if description:
        print(f"   用途: {description}")
    
    success, stdout, stderr = run_pip_command(f"pip install {package_name}")
    
    if success:
        print(f"✅ {package_name} 安装成功")
        return True
    else:
        print(f"❌ {package_name} 安装失败")
        if stderr:
            print(f"   错误: {stderr}")
        return False

def main():
    """主函数"""
    print("🔧 智能化考试系统 - 依赖包安装工具")
    print("=" * 50)
    
    # 检查Python版本
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        input("按回车键退出...")
        return
    
    print("✅ Python版本符合要求")
    
    # 定义依赖包
    packages = {
        # 基础依赖（必需）
        'requests': '网络请求库（必需）',
        
        # 智能功能依赖（推荐）
        'numpy': '数值计算库（智能分析功能）',
        'matplotlib': '图表绘制库（数据可视化）',
        'pandas': '数据处理库（学习分析）',
        
        # 现代UI依赖（可选）
        'PyQt6': '现代UI框架（灵动风格界面）'
    }
    
    print(f"\n📋 将要检查和安装 {len(packages)} 个依赖包...")
    print()
    
    # 检查已安装的包
    installed = []
    missing = []
    
    for package, description in packages.items():
        if check_package(package):
            print(f"✅ {package}: {description} - 已安装")
            installed.append(package)
        else:
            print(f"❌ {package}: {description} - 未安装")
            missing.append(package)
    
    if not missing:
        print("\n🎉 所有依赖包都已安装！")
        input("按回车键退出...")
        return
    
    print(f"\n📦 需要安装 {len(missing)} 个包:")
    for package in missing:
        print(f"   • {package}: {packages[package]}")
    
    # 询问用户是否安装
    print("\n🤔 安装选项:")
    print("1. 安装所有缺失的包")
    print("2. 只安装基础包 (requests)")
    print("3. 只安装智能功能包 (numpy, matplotlib, pandas)")
    print("4. 只安装现代UI包 (PyQt6)")
    print("5. 自定义选择")
    print("6. 跳过安装")
    
    choice = input("\n请选择 (1-6): ").strip()
    
    to_install = []
    
    if choice == "1":
        to_install = missing
    elif choice == "2":
        to_install = [pkg for pkg in missing if pkg in ['requests']]
    elif choice == "3":
        to_install = [pkg for pkg in missing if pkg in ['numpy', 'matplotlib', 'pandas']]
    elif choice == "4":
        to_install = [pkg for pkg in missing if pkg in ['PyQt6']]
    elif choice == "5":
        print("\n请选择要安装的包 (输入序号，用空格分隔):")
        for i, package in enumerate(missing, 1):
            print(f"{i}. {package}: {packages[package]}")
        
        try:
            indices = input("选择: ").strip().split()
            to_install = [missing[int(i)-1] for i in indices if i.isdigit() and 1 <= int(i) <= len(missing)]
        except:
            print("输入无效，跳过安装")
            to_install = []
    elif choice == "6":
        print("跳过安装")
        to_install = []
    else:
        print("无效选择，跳过安装")
        to_install = []
    
    if not to_install:
        print("\n👋 未选择安装任何包")
        input("按回车键退出...")
        return
    
    # 开始安装
    print(f"\n🚀 开始安装 {len(to_install)} 个包...")
    print("=" * 30)
    
    success_count = 0
    failed_packages = []
    
    for package in to_install:
        if install_package(package, packages[package]):
            success_count += 1
        else:
            failed_packages.append(package)
        print()
    
    # 安装结果
    print("=" * 50)
    print("📊 安装结果:")
    print(f"✅ 成功安装: {success_count} 个")
    print(f"❌ 安装失败: {len(failed_packages)} 个")
    
    if failed_packages:
        print("\n❌ 安装失败的包:")
        for package in failed_packages:
            print(f"   • {package}")
        
        print("\n🔧 可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 升级pip: python -m pip install --upgrade pip")
        print("3. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        print("4. 手动安装: pip install " + " ".join(failed_packages))
    
    # 最终检查
    print("\n🔍 最终检查...")
    all_installed = True
    for package in packages:
        if check_package(package):
            print(f"✅ {package}")
        else:
            print(f"❌ {package}")
            all_installed = False
    
    if all_installed:
        print("\n🎉 所有依赖包安装完成！")
        print("现在可以使用所有功能:")
        print("• 基础考试功能")
        print("• 智能分析功能")
        print("• 现代UI界面")
    else:
        print("\n⚠️ 部分依赖包未安装，某些功能可能受限")
        print("但基础功能仍然可以正常使用")
    
    print("\n🚀 推荐启动方式:")
    if check_package('PyQt6'):
        print("• 现代UI: python demo_modern_ui.py")
    print("• 稳定版: python main_stable.py")
    print("• 启动菜单: run_menu.bat")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
