#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版考试记录窗口
提供美观的界面和丰富的功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime, timedelta
import json
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class EnhancedExamHistoryWindow:
    def __init__(self, parent, exam_manager, wrong_question_manager):
        self.parent = parent
        self.exam_manager = exam_manager
        self.wrong_question_manager = wrong_question_manager
        self.window = None
        self.current_view = "list"  # list, chart, analysis
        
        self.show()
        
    def show(self):
        """显示增强版考试记录窗口"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 考试记录 - 增强版")
        self.window.geometry("1200x800")
        self.window.transient(self.parent)
        
        # 设置窗口图标和样式
        self.setup_styles()
        self.create_interface()
        self.load_exam_records()
        
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        
        # 配置标题样式
        style.configure("Title.TLabel", font=("Microsoft YaHei", 16, "bold"), foreground="#2c3e50")
        style.configure("Subtitle.TLabel", font=("Microsoft YaHei", 12, "bold"), foreground="#34495e")
        style.configure("Info.TLabel", font=("Microsoft YaHei", 10), foreground="#7f8c8d")
        
        # 配置按钮样式
        style.configure("Action.TButton", font=("Microsoft YaHei", 9))
        
    def create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题栏
        self.create_header(main_frame)
        
        # 工具栏
        self.create_toolbar(main_frame)
        
        # 主内容区域
        self.create_content_area(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)
        
    def create_header(self, parent):
        """创建标题栏"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 标题
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(side=tk.LEFT)
        
        ttk.Label(title_frame, text="📊 考试记录", style="Title.TLabel").pack(anchor=tk.W)
        
        # 统计信息
        self.stats_frame = ttk.Frame(title_frame)
        self.stats_frame.pack(anchor=tk.W, pady=(5, 0))
        
        # 右侧操作按钮
        action_frame = ttk.Frame(header_frame)
        action_frame.pack(side=tk.RIGHT)
        
        ttk.Button(action_frame, text="🔄 刷新", style="Action.TButton",
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="📈 分析报告", style="Action.TButton",
                  command=self.show_analysis_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="📋 导出数据", style="Action.TButton",
                  command=self.export_data).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.LabelFrame(parent, text="视图选择", padding=10)
        toolbar_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 视图切换按钮
        view_frame = ttk.Frame(toolbar_frame)
        view_frame.pack(side=tk.LEFT)
        
        ttk.Button(view_frame, text="📋 列表视图", 
                  command=lambda: self.switch_view("list")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(view_frame, text="📊 图表视图", 
                  command=lambda: self.switch_view("chart")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(view_frame, text="🔍 详细分析", 
                  command=lambda: self.switch_view("analysis")).pack(side=tk.LEFT, padx=(0, 5))
        
        # 筛选选项
        filter_frame = ttk.Frame(toolbar_frame)
        filter_frame.pack(side=tk.RIGHT)
        
        ttk.Label(filter_frame, text="时间范围:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.time_filter = ttk.Combobox(filter_frame, width=12, state="readonly")
        self.time_filter['values'] = ('全部', '最近7天', '最近30天', '最近3个月', '最近1年')
        self.time_filter.set('全部')
        self.time_filter.pack(side=tk.LEFT, padx=(0, 10))
        self.time_filter.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        ttk.Label(filter_frame, text="试卷类型:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.type_filter = ttk.Combobox(filter_frame, width=12, state="readonly")
        self.type_filter['values'] = ('全部', 'AI生成试卷', '自定义试卷', '练习模式')
        self.type_filter.set('全部')
        self.type_filter.pack(side=tk.LEFT)
        self.type_filter.bind('<<ComboboxSelected>>', self.on_filter_change)
        
    def create_content_area(self, parent):
        """创建主内容区域"""
        # 创建notebook用于不同视图
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 列表视图
        self.create_list_view()
        
        # 图表视图
        self.create_chart_view()
        
        # 分析视图
        self.create_analysis_view()
        
    def create_list_view(self):
        """创建列表视图"""
        list_frame = ttk.Frame(self.notebook)
        self.notebook.add(list_frame, text="📋 考试列表")
        
        # 考试记录表格
        columns = ('ID', '试卷名称', '考试时间', '得分', '总分', '正确率', '用时', '状态', '操作')
        self.exam_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)
        
        # 设置列
        column_configs = {
            'ID': {'width': 50, 'anchor': 'center'},
            '试卷名称': {'width': 200, 'anchor': 'w'},
            '考试时间': {'width': 130, 'anchor': 'center'},
            '得分': {'width': 60, 'anchor': 'center'},
            '总分': {'width': 60, 'anchor': 'center'},
            '正确率': {'width': 80, 'anchor': 'center'},
            '用时': {'width': 80, 'anchor': 'center'},
            '状态': {'width': 80, 'anchor': 'center'},
            '操作': {'width': 100, 'anchor': 'center'}
        }
        
        for col, config in column_configs.items():
            self.exam_tree.heading(col, text=col)
            self.exam_tree.column(col, width=config['width'], anchor=config['anchor'])
        
        # 滚动条
        tree_scrollbar_v = ttk.Scrollbar(list_frame, orient="vertical", command=self.exam_tree.yview)
        tree_scrollbar_h = ttk.Scrollbar(list_frame, orient="horizontal", command=self.exam_tree.xview)
        self.exam_tree.configure(yscrollcommand=tree_scrollbar_v.set, xscrollcommand=tree_scrollbar_h.set)
        
        # 布局
        self.exam_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        tree_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定事件
        self.exam_tree.bind('<Double-Button-1>', self.on_exam_double_click)
        self.exam_tree.bind('<Button-3>', self.show_context_menu)
        
        # 批量操作按钮
        batch_frame = ttk.Frame(list_frame)
        batch_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(batch_frame, text="批量操作:", style="Info.TLabel").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(batch_frame, text="☑️ 全选", command=self.select_all_exams).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="❌ 取消选择", command=self.deselect_all_exams).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="🗑️ 批量删除", command=self.batch_delete_exams).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="📊 批量分析", command=self.batch_analyze_exams).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_chart_view(self):
        """创建图表视图"""
        chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(chart_frame, text="📊 统计图表")
        
        # 图表选择
        chart_control_frame = ttk.Frame(chart_frame)
        chart_control_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(chart_control_frame, text="图表类型:", style="Info.TLabel").pack(side=tk.LEFT, padx=(0, 5))
        
        self.chart_type = ttk.Combobox(chart_control_frame, width=15, state="readonly")
        self.chart_type['values'] = ('成绩趋势图', '正确率分布', '考试频率统计', '知识点掌握度')
        self.chart_type.set('成绩趋势图')
        self.chart_type.pack(side=tk.LEFT, padx=(0, 10))
        self.chart_type.bind('<<ComboboxSelected>>', self.update_chart)
        
        ttk.Button(chart_control_frame, text="🔄 更新图表", 
                  command=self.update_chart).pack(side=tk.LEFT, padx=(0, 5))
        
        # 图表显示区域
        self.chart_frame = ttk.Frame(chart_frame)
        self.chart_frame.pack(fill=tk.BOTH, expand=True)
        
    def create_analysis_view(self):
        """创建分析视图"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="🔍 详细分析")
        
        # 分析内容
        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, wrap=tk.WORD, 
                                                      font=("Microsoft YaHei", 10))
        self.analysis_text.pack(fill=tk.BOTH, expand=True)
        
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="就绪", style="Info.TLabel")
        self.status_label.pack(side=tk.LEFT)
        
        # 右侧显示当前时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ttk.Label(status_frame, text=current_time, style="Info.TLabel").pack(side=tk.RIGHT)
        
    def load_exam_records(self):
        """加载考试记录"""
        try:
            # 清空现有数据
            for item in self.exam_tree.get_children():
                self.exam_tree.delete(item)
                
            # 获取考试记录
            records = self.exam_manager.get_exam_records()
            
            for record in records:
                # 计算正确率
                percentage = f"{record[3]/record[4]*100:.1f}%" if record[4] > 0 else "0%"
                
                # 格式化时间
                exam_time = record[7][:16] if record[7] else "未知"
                
                # 确定状态
                score_rate = record[3]/record[4] if record[4] > 0 else 0
                if score_rate >= 0.9:
                    status = "🏆 优秀"
                elif score_rate >= 0.8:
                    status = "👍 良好"
                elif score_rate >= 0.6:
                    status = "📚 及格"
                else:
                    status = "❌ 需提高"
                
                # 试卷名称
                exam_title = record[8] if len(record) > 8 and record[8] else "未命名试卷"
                
                self.exam_tree.insert('', tk.END, values=(
                    record[0],  # ID
                    exam_title[:30] + "..." if len(exam_title) > 30 else exam_title,  # 试卷名称
                    exam_time,  # 考试时间
                    record[3],  # 得分
                    record[4],  # 总分
                    percentage,  # 正确率
                    "未记录",  # 用时（暂时）
                    status,  # 状态
                    "查看详情"  # 操作
                ))
                
            # 更新统计信息
            self.update_statistics()
            self.status_label.config(text=f"已加载 {len(records)} 条考试记录")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载考试记录失败: {str(e)}")
            
    def update_statistics(self):
        """更新统计信息"""
        try:
            stats = self.exam_manager.get_exam_statistics()
            
            # 清空现有统计信息
            for widget in self.stats_frame.winfo_children():
                widget.destroy()
                
            # 显示统计信息
            stats_text = f"📊 总考试: {stats['total_records']}次  |  📈 平均分: {stats['average_percentage']:.1f}%  |  🏆 最高分: {stats['highest_percentage']:.1f}%"
            ttk.Label(self.stats_frame, text=stats_text, style="Info.TLabel").pack()
            
        except Exception as e:
            print(f"更新统计信息失败: {e}")
            
    def switch_view(self, view_type):
        """切换视图"""
        self.current_view = view_type
        
        if view_type == "list":
            self.notebook.select(0)
        elif view_type == "chart":
            self.notebook.select(1)
            self.update_chart()
        elif view_type == "analysis":
            self.notebook.select(2)
            self.generate_analysis_report()
            
    def on_filter_change(self, event=None):
        """筛选条件改变"""
        # 重新加载数据（根据筛选条件）
        self.load_exam_records()
        
    def refresh_data(self):
        """刷新数据"""
        self.load_exam_records()
        if self.current_view == "chart":
            self.update_chart()
        elif self.current_view == "analysis":
            self.generate_analysis_report()
            
    def on_exam_double_click(self, event):
        """考试记录双击事件"""
        selection = self.exam_tree.selection()
        if not selection:
            return
            
        item = self.exam_tree.item(selection[0])
        exam_id = item['values'][0]
        
        # 显示考试详情
        self.show_exam_detail(exam_id)
        
    def show_exam_detail(self, exam_id):
        """显示考试详情"""
        try:
            # 获取考试详细信息
            record = self.exam_manager.get_exam_record_by_id(exam_id)
            if not record:
                messagebox.showwarning("提示", "未找到考试记录")
                return
                
            # 创建详情窗口
            detail_window = tk.Toplevel(self.window)
            detail_window.title(f"考试详情 - {record[8] if len(record) > 8 else '未命名试卷'}")
            detail_window.geometry("600x500")
            detail_window.transient(self.window)
            
            # 详情内容
            detail_frame = ttk.Frame(detail_window)
            detail_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
            
            # 基本信息
            info_text = f"""📋 考试详情

🏷️ 试卷名称: {record[8] if len(record) > 8 else '未命名试卷'}
🕒 考试时间: {record[7]}
📊 考试成绩: {record[3]}/{record[4]} ({record[3]/record[4]*100:.1f}%)
📝 题目总数: {record[4]}道
✅ 正确题数: {record[3]}道
❌ 错误题数: {record[4] - record[3]}道

📈 成绩分析:
"""
            
            score_rate = record[3]/record[4] if record[4] > 0 else 0
            if score_rate >= 0.9:
                info_text += "🏆 优秀！继续保持这种学习状态\n"
            elif score_rate >= 0.8:
                info_text += "👍 良好！稍加努力就能达到优秀\n"
            elif score_rate >= 0.6:
                info_text += "📚 及格，还有提升空间\n"
            else:
                info_text += "❌ 需要加强学习，建议重点复习错题\n"
                
            info_text += f"\n💡 学习建议:\n"
            if record[4] - record[3] > 0:
                info_text += f"• 查看本次考试的 {record[4] - record[3]} 道错题\n"
                info_text += f"• 重点复习相关知识点\n"
                info_text += f"• 建议进行针对性练习\n"
            else:
                info_text += f"• 全部答对，可以尝试更高难度的题目\n"
                
            text_widget = scrolledtext.ScrolledText(detail_frame, wrap=tk.WORD, 
                                                   font=("Microsoft YaHei", 10))
            text_widget.pack(fill=tk.BOTH, expand=True)
            text_widget.insert(1.0, info_text)
            text_widget.config(state=tk.DISABLED)
            
            # 操作按钮
            button_frame = ttk.Frame(detail_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))
            
            ttk.Button(button_frame, text="📝 查看错题", 
                      command=lambda: self.view_exam_wrong_questions(exam_id)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="🔄 重新考试", 
                      command=lambda: self.retake_exam(exam_id)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="🗑️ 删除记录", 
                      command=lambda: self.delete_exam_record(exam_id, detail_window)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="关闭", 
                      command=detail_window.destroy).pack(side=tk.RIGHT)
                      
        except Exception as e:
            messagebox.showerror("错误", f"显示考试详情失败: {str(e)}")
            
    def update_chart(self, event=None):
        """更新图表"""
        try:
            # 清空现有图表
            for widget in self.chart_frame.winfo_children():
                widget.destroy()
                
            chart_type = self.chart_type.get()
            
            if chart_type == "成绩趋势图":
                self.create_score_trend_chart()
            elif chart_type == "正确率分布":
                self.create_accuracy_distribution_chart()
            elif chart_type == "考试频率统计":
                self.create_exam_frequency_chart()
            elif chart_type == "知识点掌握度":
                self.create_knowledge_mastery_chart()
                
        except Exception as e:
            messagebox.showerror("错误", f"更新图表失败: {str(e)}")
            
    def create_score_trend_chart(self):
        """创建成绩趋势图"""
        # 获取考试记录
        records = self.exam_manager.get_exam_records()
        
        if not records:
            ttk.Label(self.chart_frame, text="暂无考试数据", 
                     font=("Microsoft YaHei", 12)).pack(expand=True)
            return
            
        # 准备数据
        dates = []
        scores = []
        
        for record in records[-20:]:  # 最近20次考试
            dates.append(record[7][:10] if record[7] else "未知")
            score_rate = record[3]/record[4]*100 if record[4] > 0 else 0
            scores.append(score_rate)
            
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(range(len(scores)), scores, marker='o', linewidth=2, markersize=6)
        ax.set_title('考试成绩趋势图', fontsize=14, fontweight='bold')
        ax.set_xlabel('考试次数')
        ax.set_ylabel('正确率 (%)')
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 100)
        
        # 添加平均线
        if scores:
            avg_score = sum(scores) / len(scores)
            ax.axhline(y=avg_score, color='r', linestyle='--', alpha=0.7, 
                      label=f'平均分: {avg_score:.1f}%')
            ax.legend()
        
        # 嵌入到tkinter
        canvas = FigureCanvasTkAgg(fig, self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def generate_analysis_report(self):
        """生成分析报告"""
        try:
            # 获取统计数据
            stats = self.exam_manager.get_exam_statistics()
            records = self.exam_manager.get_exam_records()
            
            report = f"""📊 考试数据分析报告
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{'='*50}

📈 基础统计
• 总考试次数: {stats['total_records']}次
• 平均正确率: {stats['average_percentage']:.1f}%
• 最高正确率: {stats['highest_percentage']:.1f}%
• 最低正确率: {stats.get('lowest_percentage', 0):.1f}%

📊 成绩分布
"""
            
            # 成绩分布统计
            if records:
                excellent = sum(1 for r in records if r[3]/r[4] >= 0.9)
                good = sum(1 for r in records if 0.8 <= r[3]/r[4] < 0.9)
                pass_count = sum(1 for r in records if 0.6 <= r[3]/r[4] < 0.8)
                fail = sum(1 for r in records if r[3]/r[4] < 0.6)
                
                report += f"• 优秀 (90%+): {excellent}次 ({excellent/len(records)*100:.1f}%)\n"
                report += f"• 良好 (80-89%): {good}次 ({good/len(records)*100:.1f}%)\n"
                report += f"• 及格 (60-79%): {pass_count}次 ({pass_count/len(records)*100:.1f}%)\n"
                report += f"• 不及格 (<60%): {fail}次 ({fail/len(records)*100:.1f}%)\n"
                
            report += f"""
🎯 学习建议
• 建议保持每周至少2-3次的练习频率
• 重点关注正确率低于80%的知识点
• 定期复习错题，巩固薄弱环节
• 可以尝试增加题目难度，提升挑战性

📅 最近表现
"""
            
            # 最近5次考试分析
            if records:
                recent_records = records[-5:]
                recent_avg = sum(r[3]/r[4] for r in recent_records) / len(recent_records) * 100
                
                report += f"• 最近5次考试平均分: {recent_avg:.1f}%\n"
                
                if recent_avg > stats['average_percentage']:
                    report += "• 📈 最近表现优于历史平均水平，继续保持！\n"
                else:
                    report += "• 📉 最近表现低于历史平均水平，需要加强复习\n"
                    
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(1.0, report)
            
        except Exception as e:
            messagebox.showerror("错误", f"生成分析报告失败: {str(e)}")
            
    # 批量操作方法
    def select_all_exams(self):
        """全选所有考试记录"""
        for item in self.exam_tree.get_children():
            self.exam_tree.selection_add(item)
        self.status_label.config(text=f"已选择 {len(self.exam_tree.selection())} 条记录")
        
    def deselect_all_exams(self):
        """取消选择所有考试记录"""
        self.exam_tree.selection_remove(self.exam_tree.selection())
        self.status_label.config(text="已取消所有选择")
        
    def batch_delete_exams(self):
        """批量删除考试记录"""
        selection = self.exam_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的考试记录")
            return
            
        if not messagebox.askyesno("确认删除", 
                                  f"确定要删除 {len(selection)} 条考试记录吗？\n此操作不可撤销！"):
            return
            
        try:
            success_count = 0
            for item in selection:
                exam_id = self.exam_tree.item(item)['values'][0]
                # 这里需要实现删除考试记录的方法
                # self.exam_manager.delete_exam_record(exam_id)
                success_count += 1
                
            self.refresh_data()
            self.status_label.config(text=f"已删除 {success_count} 条考试记录")
            
        except Exception as e:
            messagebox.showerror("错误", f"批量删除失败: {str(e)}")
            
    def batch_analyze_exams(self):
        """批量分析考试记录"""
        selection = self.exam_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要分析的考试记录")
            return
            
        messagebox.showinfo("提示", f"批量分析功能开发中，已选择 {len(selection)} 条记录")
        
    def show_analysis_report(self):
        """显示分析报告"""
        self.switch_view("analysis")
        
    def export_data(self):
        """导出数据"""
        messagebox.showinfo("提示", "数据导出功能开发中...")
        
    def show_context_menu(self, event):
        """显示右键菜单"""
        # 创建右键菜单
        context_menu = tk.Menu(self.window, tearoff=0)
        context_menu.add_command(label="📝 查看详情", command=lambda: self.on_exam_double_click(None))
        context_menu.add_command(label="🔄 重新考试", command=self.retake_selected_exam)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ 删除记录", command=self.delete_selected_exam)
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
            
    def retake_selected_exam(self):
        """重新考试"""
        messagebox.showinfo("提示", "重新考试功能开发中...")
        
    def delete_selected_exam(self):
        """删除选中的考试记录"""
        selection = self.exam_tree.selection()
        if not selection:
            return
            
        if messagebox.askyesno("确认删除", "确定要删除选中的考试记录吗？"):
            messagebox.showinfo("提示", "删除功能开发中...")
            
    def view_exam_wrong_questions(self, exam_id):
        """查看考试错题"""
        try:
            # 打开错题本并筛选该考试的错题
            from src.ui.advanced_wrong_questions_window import AdvancedWrongQuestionsWindow
            wrong_window = AdvancedWrongQuestionsWindow(self.window, self.wrong_question_manager)
            wrong_window.show()
            # 这里可以添加筛选逻辑
        except Exception as e:
            messagebox.showerror("错误", f"查看错题失败: {str(e)}")
            
    def retake_exam(self, exam_id):
        """重新考试"""
        messagebox.showinfo("提示", "重新考试功能开发中...")
        
    def delete_exam_record(self, exam_id, detail_window):
        """删除考试记录"""
        if messagebox.askyesno("确认删除", "确定要删除这条考试记录吗？"):
            try:
                # 这里需要实现删除考试记录的方法
                # self.exam_manager.delete_exam_record(exam_id)
                detail_window.destroy()
                self.refresh_data()
                messagebox.showinfo("成功", "考试记录已删除")
            except Exception as e:
                messagebox.showerror("错误", f"删除失败: {str(e)}")
                
    def create_accuracy_distribution_chart(self):
        """创建正确率分布图"""
        ttk.Label(self.chart_frame, text="正确率分布图开发中...", 
                 font=("Microsoft YaHei", 12)).pack(expand=True)
                 
    def create_exam_frequency_chart(self):
        """创建考试频率统计图"""
        ttk.Label(self.chart_frame, text="考试频率统计图开发中...", 
                 font=("Microsoft YaHei", 12)).pack(expand=True)
                 
    def create_knowledge_mastery_chart(self):
        """创建知识点掌握度图"""
        ttk.Label(self.chart_frame, text="知识点掌握度图开发中...", 
                 font=("Microsoft YaHei", 12)).pack(expand=True)
