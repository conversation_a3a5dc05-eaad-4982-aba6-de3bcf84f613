#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有考试界面
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_interfaces():
    """测试所有界面"""
    try:
        print("🧪 测试所有考试界面...")
        
        # 导入必要的模块
        from src.core.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.core.wrong_question_manager import WrongQuestionManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        exam_manager = ExamManager(db_manager)
        wrong_question_manager = WrongQuestionManager(db_manager)
        
        # 创建测试数据
        test_questions = [
            {
                'type': 'single_choice',
                'question': '以下哪个是iOS的特色设计元素？',
                'options': ['圆角设计', '方形按钮', '复杂界面', '暗色主题'],
                'correct_answer': 'A',
                'explanation': 'iOS以圆角设计著称',
                'score': 2
            },
            {
                'type': 'multiple_choice',
                'question': 'iOS界面设计的优点包括哪些？（多选）',
                'options': ['简洁美观', '操作直观', '视觉舒适', '响应流畅'],
                'correct_answer': 'ABCD',
                'explanation': '这些都是iOS界面的优点',
                'score': 3
            },
            {
                'type': 'true_false',
                'question': 'iOS风格界面比传统界面更现代化。',
                'correct_answer': '对',
                'explanation': 'iOS风格确实更加现代化',
                'score': 1
            },
            {
                'type': 'short_answer',
                'question': '请描述您对这个iOS风格考试界面的感受。',
                'correct_answer': '美观、流畅、现代',
                'explanation': '希望您喜欢这个界面',
                'score': 4
            }
        ]
        
        # 创建测试考试
        exam_id = exam_manager.create_exam(
            title="🍎 界面测试专用考试",
            description="测试不同风格的考试界面",
            questions=test_questions,
            time_limit=15
        )
        
        exam_data = exam_manager.get_exam_by_id(exam_id)
        print(f"✅ 创建测试考试成功: {exam_data['title']}")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("🧪 界面测试选择器")
        root.geometry("500x400")
        root.configure(bg='#F5F5F5')
        
        # 居中显示
        root.geometry("+{}+{}".format(
            (root.winfo_screenwidth() // 2) - 250,
            (root.winfo_screenheight() // 2) - 200
        ))
        
        # 标题
        title_label = tk.Label(root, text="🧪 选择要测试的界面",
                              font=("Microsoft YaHei", 18, "bold"),
                              bg='#F5F5F5', fg='#333333')
        title_label.pack(pady=30)
        
        # 按钮框架
        button_frame = tk.Frame(root, bg='#F5F5F5')
        button_frame.pack(expand=True)
        
        def test_ios():
            """测试iOS界面"""
            try:
                root.withdraw()
                from src.ui.ios_style_exam_window import iOSStyleExamWindow
                iOSStyleExamWindow(root, exam_manager, wrong_question_manager, exam_data)
                print("🍎 启动iOS风格界面")
            except Exception as e:
                messagebox.showerror("错误", f"iOS界面启动失败：{str(e)}")
                root.deiconify()
                
        def test_beautiful():
            """测试美观界面"""
            try:
                root.withdraw()
                from src.ui.beautiful_exam_window import BeautifulExamWindow
                BeautifulExamWindow(root, exam_manager, wrong_question_manager, exam_data)
                print("✨ 启动美观舒适界面")
            except Exception as e:
                messagebox.showerror("错误", f"美观界面启动失败：{str(e)}")
                root.deiconify()
                
        def test_classic():
            """测试传统界面"""
            try:
                root.withdraw()
                from src.ui.exam_window import ExamWindow
                ExamWindow(root, exam_manager, wrong_question_manager, exam_data)
                print("📝 启动传统界面")
            except Exception as e:
                messagebox.showerror("错误", f"传统界面启动失败：{str(e)}")
                root.deiconify()
        
        # 创建测试按钮
        ios_btn = tk.Button(button_frame, text="🍎 测试iOS风格界面",
                           font=("Microsoft YaHei", 14, "bold"),
                           bg='#007AFF', fg='white',
                           relief='flat', padx=30, pady=15,
                           command=test_ios)
        ios_btn.pack(pady=10)
        
        beautiful_btn = tk.Button(button_frame, text="✨ 测试美观舒适界面",
                                 font=("Microsoft YaHei", 14, "bold"),
                                 bg='#5856D6', fg='white',
                                 relief='flat', padx=30, pady=15,
                                 command=test_beautiful)
        beautiful_btn.pack(pady=10)
        
        classic_btn = tk.Button(button_frame, text="📝 测试传统界面",
                               font=("Microsoft YaHei", 14),
                               bg='#8E8E93', fg='white',
                               relief='flat', padx=30, pady=15,
                               command=test_classic)
        classic_btn.pack(pady=10)
        
        # 退出按钮
        exit_btn = tk.Button(button_frame, text="❌ 退出",
                            font=("Microsoft YaHei", 12),
                            bg='#FF3B30', fg='white',
                            relief='flat', padx=20, pady=10,
                            command=root.quit)
        exit_btn.pack(pady=20)
        
        # 运行界面
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误对话框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", f"界面测试失败：\n{str(e)}")
        root.destroy()

if __name__ == "__main__":
    test_interfaces()
