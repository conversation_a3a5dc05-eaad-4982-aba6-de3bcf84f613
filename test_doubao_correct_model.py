#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试豆包API正确的模型名称
"""

import requests
import json

def test_doubao_with_correct_model():
    """使用正确的模型名称测试豆包API"""
    
    api_key = "2f62a42c-6ea7-4c2e-8b51-9d2e93f6e9c6"
    endpoint = "https://ark.cn-beijing.volces.com/api/v3"
    model = "doubao-seed-1-6-250615"  # 使用正确的推理接入点ID
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "你好，请简单介绍一下Python编程语言。"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    print("=== 豆包API测试（正确模型） ===")
    print(f"API Key: {api_key[:10]}...")
    print(f"Endpoint: {endpoint}")
    print(f"Model: {model}")
    print()
    
    try:
        print("正在发送请求...")
        response = requests.post(
            f"{endpoint}/chat/completions",
            headers=headers,
            json=data,
            timeout=(10, 60)
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ API调用成功!")
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"\n豆包回复:")
                print(content)
                print("\n=== 测试成功 ===")
                return True
            else:
                print("✗ 响应格式异常")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
        else:
            print(f"✗ API调用失败: {response.status_code}")
            try:
                error_detail = response.json()
                print("错误详情:")
                print(json.dumps(error_detail, indent=2, ensure_ascii=False))
            except:
                print(f"错误信息: {response.text}")
                
    except requests.exceptions.Timeout:
        print("✗ 请求超时")
    except requests.exceptions.ConnectionError as e:
        print(f"✗ 连接失败: {e}")
    except Exception as e:
        print(f"✗ 请求出错: {e}")
    
    return False

if __name__ == "__main__":
    success = test_doubao_with_correct_model()
    
    if success:
        print("\n🎉 豆包API配置正确！现在可以在考试系统中正常使用了。")
    else:
        print("\n❌ 豆包API仍有问题，请检查:")
        print("1. API密钥是否正确")
        print("2. 推理接入点ID是否正确")
        print("3. 账户是否有足够余额")
        print("4. 推理接入点是否已启用")
