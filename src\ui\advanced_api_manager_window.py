#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级API管理界面
类似SillyTavern的API管理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from typing import Dict, List
from src.api.universal_api_manager import UniversalAPIManager, APIProvider

class AdvancedAPIManagerWindow:
    """高级API管理窗口"""
    
    def __init__(self, parent):
        self.parent = parent
        self.api_manager = UniversalAPIManager()
        
        # 加载配置
        try:
            self.api_manager.load_config("config/api_providers.json")
        except:
            pass
        
        self.window = tk.Toplevel(parent)
        self.window.title("高级API管理器")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        
        self.setup_ui()
        self.refresh_provider_list()
    
    def setup_ui(self):
        """设置用户界面"""
        
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="高级API管理器", font=("Arial", 16, "bold")).pack(side=tk.LEFT)
        ttk.Button(title_frame, text="测试所有", command=self.test_all_providers).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(title_frame, text="保存配置", command=self.save_config).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(title_frame, text="导入配置", command=self.import_config).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 创建Notebook
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # API提供商管理标签页
        self.providers_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.providers_frame, text="API提供商")
        self.setup_providers_tab()
        
        # 添加自定义API标签页
        self.custom_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.custom_frame, text="添加自定义API")
        self.setup_custom_tab()
        
        # 模型管理标签页
        self.models_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.models_frame, text="模型管理")
        self.setup_models_tab()
    
    def setup_providers_tab(self):
        """设置API提供商标签页"""
        
        # 左侧：提供商列表
        left_frame = ttk.Frame(self.providers_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        ttk.Label(left_frame, text="API提供商列表", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 5))
        
        # 提供商树形视图
        columns = ("name", "status", "models", "last_tested")
        self.providers_tree = ttk.Treeview(left_frame, columns=columns, show="tree headings", height=15)
        
        self.providers_tree.heading("#0", text="提供商")
        self.providers_tree.heading("name", text="名称")
        self.providers_tree.heading("status", text="状态")
        self.providers_tree.heading("models", text="模型数")
        self.providers_tree.heading("last_tested", text="最后测试")
        
        self.providers_tree.column("#0", width=150)
        self.providers_tree.column("name", width=100)
        self.providers_tree.column("status", width=80)
        self.providers_tree.column("models", width=80)
        self.providers_tree.column("last_tested", width=120)
        
        # 滚动条
        providers_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.providers_tree.yview)
        self.providers_tree.configure(yscrollcommand=providers_scrollbar.set)
        
        self.providers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        providers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.providers_tree.bind("<<TreeviewSelect>>", self.on_provider_select)
        
        # 右侧：提供商详情
        right_frame = ttk.Frame(self.providers_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        ttk.Label(right_frame, text="提供商详情", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 5))
        
        # 详情表单
        details_frame = ttk.LabelFrame(right_frame, text="配置信息")
        details_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 显示名称
        ttk.Label(details_frame, text="显示名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.display_name_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.display_name_var, width=40).grid(row=0, column=1, padx=5, pady=2)
        
        # API地址
        ttk.Label(details_frame, text="API地址:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.base_url_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.base_url_var, width=40).grid(row=1, column=1, padx=5, pady=2)
        
        # API密钥
        ttk.Label(details_frame, text="API密钥:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.api_key_var = tk.StringVar()
        api_key_entry = ttk.Entry(details_frame, textvariable=self.api_key_var, width=40, show="*")
        api_key_entry.grid(row=2, column=1, padx=5, pady=2)
        
        # 显示/隐藏密钥按钮
        self.show_key_var = tk.BooleanVar()
        show_key_cb = ttk.Checkbutton(details_frame, text="显示", variable=self.show_key_var, 
                                     command=lambda: api_key_entry.config(show="" if self.show_key_var.get() else "*"))
        show_key_cb.grid(row=2, column=2, padx=5, pady=2)
        
        # API类型
        ttk.Label(details_frame, text="API类型:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.api_type_var = tk.StringVar()
        api_type_combo = ttk.Combobox(details_frame, textvariable=self.api_type_var, 
                                     values=["openai", "claude", "gemini", "custom"], width=37)
        api_type_combo.grid(row=3, column=1, padx=5, pady=2)
        
        # 测试模型
        ttk.Label(details_frame, text="测试模型:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        self.test_model_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.test_model_var, width=40).grid(row=4, column=1, padx=5, pady=2)
        
        # 启用状态
        self.active_var = tk.BooleanVar()
        ttk.Checkbutton(details_frame, text="启用此API", variable=self.active_var).grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 操作按钮
        buttons_frame = ttk.Frame(right_frame)
        buttons_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(buttons_frame, text="保存更改", command=self.save_provider_changes).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="测试连接", command=self.test_current_provider).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="获取模型", command=self.fetch_models).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="删除", command=self.delete_provider).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态信息
        status_frame = ttk.LabelFrame(right_frame, text="状态信息")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.status_text = tk.Text(status_frame, height=8, wrap=tk.WORD)
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_custom_tab(self):
        """设置自定义API标签页"""
        
        # 自定义API表单
        form_frame = ttk.LabelFrame(self.custom_frame, text="添加自定义API提供商")
        form_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 内部名称
        ttk.Label(form_frame, text="内部名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.custom_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.custom_name_var, width=30).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(form_frame, text="(用于内部标识，不能重复)", foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=5)
        
        # 显示名称
        ttk.Label(form_frame, text="显示名称:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.custom_display_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.custom_display_name_var, width=30).grid(row=1, column=1, padx=5, pady=5)
        
        # API地址
        ttk.Label(form_frame, text="API地址:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.custom_base_url_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.custom_base_url_var, width=50).grid(row=2, column=1, columnspan=2, padx=5, pady=5)
        
        # API密钥
        ttk.Label(form_frame, text="API密钥:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.custom_api_key_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.custom_api_key_var, width=50, show="*").grid(row=3, column=1, columnspan=2, padx=5, pady=5)
        
        # API类型
        ttk.Label(form_frame, text="API类型:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.custom_api_type_var = tk.StringVar(value="openai")
        ttk.Combobox(form_frame, textvariable=self.custom_api_type_var, 
                    values=["openai", "claude", "gemini", "custom"], width=27).grid(row=4, column=1, padx=5, pady=5)
        
        # 测试模型
        ttk.Label(form_frame, text="测试模型:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        self.custom_test_model_var = tk.StringVar(value="gpt-3.5-turbo")
        ttk.Entry(form_frame, textvariable=self.custom_test_model_var, width=30).grid(row=5, column=1, padx=5, pady=5)
        
        # 添加按钮
        ttk.Button(form_frame, text="添加API提供商", command=self.add_custom_provider).grid(row=6, column=1, pady=10)
        
        # 常用API模板
        templates_frame = ttk.LabelFrame(self.custom_frame, text="常用API模板")
        templates_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 模板列表
        templates = [
            ("OpenAI中转1", "https://api.openai-proxy.com/v1", "openai", "gpt-3.5-turbo"),
            ("OpenAI中转2", "https://api.chatanywhere.com.cn/v1", "openai", "gpt-3.5-turbo"),
            ("Claude中转", "https://api.claude-proxy.com/v1", "openai", "claude-3-sonnet"),
            ("本地Ollama", "http://localhost:11434/v1", "openai", "llama2"),
            ("OneAPI", "http://localhost:3000/v1", "openai", "gpt-3.5-turbo"),
        ]
        
        for i, (name, url, api_type, model) in enumerate(templates):
            row = i // 2
            col = i % 2
            
            template_frame = ttk.Frame(templates_frame)
            template_frame.grid(row=row, column=col, padx=5, pady=5, sticky=tk.W)
            
            ttk.Label(template_frame, text=name, font=("Arial", 10, "bold")).pack(anchor=tk.W)
            ttk.Label(template_frame, text=f"地址: {url}", foreground="gray").pack(anchor=tk.W)
            ttk.Button(template_frame, text="使用模板", 
                      command=lambda u=url, t=api_type, m=model, n=name: self.use_template(n, u, t, m)).pack(anchor=tk.W, pady=2)
    
    def setup_models_tab(self):
        """设置模型管理标签页"""
        
        # 左侧：提供商选择
        left_frame = ttk.Frame(self.models_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        
        ttk.Label(left_frame, text="选择提供商", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 5))
        
        self.model_provider_var = tk.StringVar()
        self.model_provider_combo = ttk.Combobox(left_frame, textvariable=self.model_provider_var, width=20)
        self.model_provider_combo.pack(pady=5)
        self.model_provider_combo.bind("<<ComboboxSelected>>", self.on_model_provider_select)
        
        ttk.Button(left_frame, text="刷新模型列表", command=self.refresh_models).pack(pady=5)
        
        # 右侧：模型列表
        right_frame = ttk.Frame(self.models_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        ttk.Label(right_frame, text="可用模型", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 5))
        
        # 模型列表
        self.models_listbox = tk.Listbox(right_frame, height=20)
        models_scrollbar = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.models_listbox.yview)
        self.models_listbox.configure(yscrollcommand=models_scrollbar.set)
        
        self.models_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        models_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def refresh_provider_list(self):
        """刷新提供商列表"""
        # 清空树形视图
        for item in self.providers_tree.get_children():
            self.providers_tree.delete(item)

        # 添加提供商
        for name, provider in self.api_manager.providers.items():
            status_color = "green" if provider.status == "正常" else "red" if provider.status == "异常" else "gray"

            item = self.providers_tree.insert("", tk.END,
                                             text=provider.display_name,
                                             values=(name, provider.status, len(provider.models),
                                                   provider.last_tested or "未测试"),
                                             tags=(status_color,))

        # 设置标签颜色
        self.providers_tree.tag_configure("green", foreground="green")
        self.providers_tree.tag_configure("red", foreground="red")
        self.providers_tree.tag_configure("gray", foreground="gray")

        # 更新模型提供商下拉框
        provider_names = [p.display_name for p in self.api_manager.providers.values() if p.active]
        self.model_provider_combo['values'] = provider_names

    def on_provider_select(self, event):
        """提供商选择事件"""
        selection = self.providers_tree.selection()
        if not selection:
            return

        item = selection[0]
        provider_name = self.providers_tree.item(item)['values'][0]

        if provider_name in self.api_manager.providers:
            provider = self.api_manager.providers[provider_name]

            # 填充表单
            self.display_name_var.set(provider.display_name)
            self.base_url_var.set(provider.base_url)
            self.api_key_var.set(provider.api_key)
            self.api_type_var.set(provider.api_type)
            self.test_model_var.set(provider.test_model)
            self.active_var.set(provider.active)

            # 更新状态信息
            self.update_status_info(provider)

    def update_status_info(self, provider: APIProvider):
        """更新状态信息"""
        self.status_text.delete(1.0, tk.END)

        info = f"提供商: {provider.display_name}\n"
        info += f"状态: {provider.status}\n"
        info += f"最后测试: {provider.last_tested or '未测试'}\n"
        info += f"可用模型数: {len(provider.models)}\n"

        if provider.error_message:
            info += f"错误信息: {provider.error_message}\n"

        if provider.models:
            info += f"\n可用模型:\n"
            for model in provider.models[:10]:  # 只显示前10个
                info += f"  - {model}\n"
            if len(provider.models) > 10:
                info += f"  ... 还有 {len(provider.models) - 10} 个模型\n"

        self.status_text.insert(1.0, info)

    def save_provider_changes(self):
        """保存提供商更改"""
        selection = self.providers_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个提供商")
            return

        item = selection[0]
        provider_name = self.providers_tree.item(item)['values'][0]

        # 更新提供商配置
        self.api_manager.update_provider(
            provider_name,
            display_name=self.display_name_var.get(),
            base_url=self.base_url_var.get().rstrip('/'),
            api_key=self.api_key_var.get(),
            api_type=self.api_type_var.get(),
            test_model=self.test_model_var.get(),
            active=self.active_var.get()
        )

        messagebox.showinfo("成功", "提供商配置已更新")
        self.refresh_provider_list()

    def test_current_provider(self):
        """测试当前选中的提供商"""
        selection = self.providers_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个提供商")
            return

        item = selection[0]
        provider_name = self.providers_tree.item(item)['values'][0]

        # 先保存当前更改
        self.save_provider_changes()

        # 在线程中测试
        def test_thread():
            success = self.api_manager.test_provider_connection(provider_name)

            # 在主线程中更新UI
            self.window.after(0, lambda: self.on_test_complete(provider_name, success))

        threading.Thread(target=test_thread, daemon=True).start()
        messagebox.showinfo("提示", "正在测试连接，请稍候...")

    def on_test_complete(self, provider_name: str, success: bool):
        """测试完成回调"""
        provider = self.api_manager.providers[provider_name]

        if success:
            messagebox.showinfo("成功", f"{provider.display_name} 连接测试成功！")
        else:
            messagebox.showerror("失败", f"{provider.display_name} 连接测试失败：\n{provider.error_message}")

        self.refresh_provider_list()
        self.update_status_info(provider)

    def fetch_models(self):
        """获取模型列表"""
        selection = self.providers_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个提供商")
            return

        item = selection[0]
        provider_name = self.providers_tree.item(item)['values'][0]

        # 先保存当前更改
        self.save_provider_changes()

        # 在线程中获取模型
        def fetch_thread():
            success = self.api_manager.test_provider_connection(provider_name)
            self.window.after(0, lambda: self.on_fetch_complete(provider_name, success))

        threading.Thread(target=fetch_thread, daemon=True).start()
        messagebox.showinfo("提示", "正在获取模型列表，请稍候...")

    def on_fetch_complete(self, provider_name: str, success: bool):
        """获取模型完成回调"""
        provider = self.api_manager.providers[provider_name]

        if success and provider.models:
            messagebox.showinfo("成功", f"获取到 {len(provider.models)} 个模型")

            # 更新模型下拉框
            self.refresh_provider_list()

            # 如果当前选中的是这个提供商，更新模型列表
            if self.model_provider_var.get() == provider.display_name:
                self.update_models_list(provider.models)
        else:
            messagebox.showwarning("提示", "未能获取到模型列表")

        self.refresh_provider_list()
        self.update_status_info(provider)

    def delete_provider(self):
        """删除提供商"""
        selection = self.providers_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个提供商")
            return

        item = selection[0]
        provider_name = self.providers_tree.item(item)['values'][0]
        provider = self.api_manager.providers[provider_name]

        if messagebox.askyesno("确认删除", f"确定要删除提供商 '{provider.display_name}' 吗？"):
            self.api_manager.remove_provider(provider_name)
            self.refresh_provider_list()
            messagebox.showinfo("成功", "提供商已删除")

    def add_custom_provider(self):
        """添加自定义提供商"""
        name = self.custom_name_var.get().strip()
        display_name = self.custom_display_name_var.get().strip()
        base_url = self.custom_base_url_var.get().strip()
        api_key = self.custom_api_key_var.get().strip()
        api_type = self.custom_api_type_var.get()
        test_model = self.custom_test_model_var.get().strip()

        if not all([name, display_name, base_url]):
            messagebox.showwarning("警告", "请填写必要的字段（内部名称、显示名称、API地址）")
            return

        if name in self.api_manager.providers:
            messagebox.showwarning("警告", "内部名称已存在，请使用其他名称")
            return

        # 添加提供商
        self.api_manager.add_custom_provider(
            name=name,
            display_name=display_name,
            base_url=base_url,
            api_key=api_key,
            api_type=api_type,
            test_model=test_model
        )

        # 清空表单
        self.custom_name_var.set("")
        self.custom_display_name_var.set("")
        self.custom_base_url_var.set("")
        self.custom_api_key_var.set("")
        self.custom_api_type_var.set("openai")
        self.custom_test_model_var.set("gpt-3.5-turbo")

        self.refresh_provider_list()
        messagebox.showinfo("成功", f"自定义提供商 '{display_name}' 已添加")

    def use_template(self, name: str, url: str, api_type: str, model: str):
        """使用模板"""
        self.custom_name_var.set(name.lower().replace(" ", "_"))
        self.custom_display_name_var.set(name)
        self.custom_base_url_var.set(url)
        self.custom_api_type_var.set(api_type)
        self.custom_test_model_var.set(model)

    def test_all_providers(self):
        """测试所有提供商"""
        def test_thread():
            results = self.api_manager.test_all_providers()
            self.window.after(0, lambda: self.on_test_all_complete(results))

        threading.Thread(target=test_thread, daemon=True).start()
        messagebox.showinfo("提示", "正在测试所有提供商，请稍候...")

    def on_test_all_complete(self, results: Dict[str, bool]):
        """测试所有提供商完成"""
        success_count = sum(results.values())
        total_count = len(results)

        messagebox.showinfo("测试完成", f"测试完成！\n成功: {success_count}/{total_count}")
        self.refresh_provider_list()

    def save_config(self):
        """保存配置"""
        try:
            import os
            if not os.path.exists("config"):
                os.makedirs("config")

            self.api_manager.save_config("config/api_providers.json")
            messagebox.showinfo("成功", "配置已保存到 config/api_providers.json")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败：{str(e)}")

    def import_config(self):
        """导入配置"""
        file_path = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.api_manager.load_config(file_path)
                self.refresh_provider_list()
                messagebox.showinfo("成功", "配置导入成功")
            except Exception as e:
                messagebox.showerror("错误", f"导入配置失败：{str(e)}")

    def on_model_provider_select(self, event):
        """模型提供商选择事件"""
        provider_display_name = self.model_provider_var.get()

        # 找到对应的提供商
        for provider in self.api_manager.providers.values():
            if provider.display_name == provider_display_name:
                self.update_models_list(provider.models)
                break

    def update_models_list(self, models: List[str]):
        """更新模型列表"""
        self.models_listbox.delete(0, tk.END)
        for model in models:
            self.models_listbox.insert(tk.END, model)

    def refresh_models(self):
        """刷新模型列表"""
        provider_display_name = self.model_provider_var.get()
        if not provider_display_name:
            messagebox.showwarning("警告", "请先选择一个提供商")
            return

        # 找到对应的提供商
        provider_name = None
        for name, provider in self.api_manager.providers.items():
            if provider.display_name == provider_display_name:
                provider_name = name
                break

        if not provider_name:
            return

        # 在线程中刷新模型
        def refresh_thread():
            success = self.api_manager.test_provider_connection(provider_name)
            provider = self.api_manager.providers[provider_name]
            self.window.after(0, lambda: self.update_models_list(provider.models))

            if success and provider.models:
                self.window.after(0, lambda: messagebox.showinfo("成功", f"刷新完成，获取到 {len(provider.models)} 个模型"))
            else:
                self.window.after(0, lambda: messagebox.showwarning("提示", "未能获取到模型列表"))

        threading.Thread(target=refresh_thread, daemon=True).start()
        messagebox.showinfo("提示", "正在刷新模型列表，请稍候...")
