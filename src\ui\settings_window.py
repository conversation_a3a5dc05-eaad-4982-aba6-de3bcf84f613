#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统设置窗口
提供系统配置和管理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from src.utils.test_data_generator import TestDataGenerator

class SettingsWindow:
    def __init__(self, parent, config_manager, db_manager, ai_manager):
        """初始化设置窗口"""
        self.parent = parent
        self.config = config_manager
        self.db = db_manager
        self.ai_manager = ai_manager
        
        self.window = tk.Toplevel(parent)
        self.window.title("系统设置")
        self.window.geometry("600x500")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # API设置标签页
        self.create_api_tab(notebook)
        
        # 系统设置标签页
        self.create_system_tab(notebook)
        
        # 数据管理标签页
        self.create_data_tab(notebook)
        
        # 测试工具标签页
        self.create_test_tab(notebook)
        
        # 底部按钮
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(button_frame, text="保存设置", command=self.save_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="重置", command=self.reset_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def create_api_tab(self, notebook):
        """创建API设置标签页"""
        api_frame = ttk.Frame(notebook)
        notebook.add(api_frame, text="API设置")
        
        # 滚动框架
        canvas = tk.Canvas(api_frame)
        scrollbar = ttk.Scrollbar(api_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # OpenAI设置
        openai_frame = ttk.LabelFrame(scrollable_frame, text="OpenAI API", padding=10)
        openai_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(openai_frame, text="API Key:").pack(anchor=tk.W)
        self.openai_key_var = tk.StringVar()
        openai_key_entry = ttk.Entry(openai_frame, textvariable=self.openai_key_var, width=50, show="*")
        openai_key_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(openai_frame, text="Base URL:").pack(anchor=tk.W)
        self.openai_url_var = tk.StringVar()
        openai_url_entry = ttk.Entry(openai_frame, textvariable=self.openai_url_var, width=50)
        openai_url_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(openai_frame, text="模型:").pack(anchor=tk.W)
        self.openai_model_var = tk.StringVar()
        self.openai_model_combo = ttk.Combobox(openai_frame, textvariable=self.openai_model_var, width=30)
        self.openai_model_combo['values'] = ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-3.5-turbo-16k']
        self.openai_model_combo.pack(fill=tk.X, pady=(0, 5))

        button_frame_openai = ttk.Frame(openai_frame)
        button_frame_openai.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(button_frame_openai, text="测试连接",
                  command=lambda: self.test_api_connection("openai")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame_openai, text="获取模型列表",
                  command=lambda: self.refresh_models("openai")).pack(side=tk.LEFT)

        # DeepSeek设置
        deepseek_frame = ttk.LabelFrame(scrollable_frame, text="DeepSeek API", padding=10)
        deepseek_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(deepseek_frame, text="API Key:").pack(anchor=tk.W)
        self.deepseek_key_var = tk.StringVar()
        deepseek_key_entry = ttk.Entry(deepseek_frame, textvariable=self.deepseek_key_var, width=50, show="*")
        deepseek_key_entry.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(deepseek_frame, text="Base URL:").pack(anchor=tk.W)
        self.deepseek_url_var = tk.StringVar()
        deepseek_url_entry = ttk.Entry(deepseek_frame, textvariable=self.deepseek_url_var, width=50)
        deepseek_url_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(deepseek_frame, text="模型:").pack(anchor=tk.W)
        self.deepseek_model_var = tk.StringVar()
        deepseek_model_entry = ttk.Entry(deepseek_frame, textvariable=self.deepseek_model_var, width=30)
        deepseek_model_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(deepseek_frame, text="测试连接",
                  command=lambda: self.test_api_connection("deepseek")).pack(anchor=tk.W)
        
        # Gemini设置
        gemini_frame = ttk.LabelFrame(scrollable_frame, text="Gemini API", padding=10)
        gemini_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(gemini_frame, text="API Key:").pack(anchor=tk.W)
        self.gemini_key_var = tk.StringVar()
        gemini_key_entry = ttk.Entry(gemini_frame, textvariable=self.gemini_key_var, width=50, show="*")
        gemini_key_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(gemini_frame, text="模型:").pack(anchor=tk.W)
        self.gemini_model_var = tk.StringVar()
        gemini_model_entry = ttk.Entry(gemini_frame, textvariable=self.gemini_model_var, width=30)
        gemini_model_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(gemini_frame, text="测试连接",
                  command=lambda: self.test_api_connection("gemini")).pack(anchor=tk.W)
        
        # Doubao设置
        doubao_frame = ttk.LabelFrame(scrollable_frame, text="Doubao API", padding=10)
        doubao_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(doubao_frame, text="API Key:").pack(anchor=tk.W)
        self.doubao_key_var = tk.StringVar()
        doubao_key_entry = ttk.Entry(doubao_frame, textvariable=self.doubao_key_var, width=50, show="*")
        doubao_key_entry.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(doubao_frame, text="Endpoint:").pack(anchor=tk.W)
        self.doubao_endpoint_var = tk.StringVar()
        doubao_endpoint_entry = ttk.Entry(doubao_frame, textvariable=self.doubao_endpoint_var, width=50)
        doubao_endpoint_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(doubao_frame, text="模型:").pack(anchor=tk.W)
        self.doubao_model_var = tk.StringVar()
        doubao_model_combo = ttk.Combobox(doubao_frame, textvariable=self.doubao_model_var, width=30)
        doubao_model_combo['values'] = ['doubao-seed-1-6-250615', 'doubao-pro-4k', 'doubao-pro-32k', 'doubao-pro-128k', 'doubao-lite-4k', 'doubao-lite-32k']
        doubao_model_combo.pack(fill=tk.X, pady=(0, 5))

        button_frame_doubao = ttk.Frame(doubao_frame)
        button_frame_doubao.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(button_frame_doubao, text="测试连接",
                  command=lambda: self.test_api_connection("doubao")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame_doubao, text="网络诊断",
                  command=self.test_doubao_network).pack(side=tk.LEFT)

        # 中转API设置
        relay_frame = ttk.LabelFrame(scrollable_frame, text="中转API (通义千问/其他)", padding=10)
        relay_frame.pack(fill=tk.X, pady=(0, 10))

        # 添加说明
        info_label = ttk.Label(relay_frame, text="支持通义千问、智谱AI、Kimi等中转API服务",
                              foreground="gray", font=("Arial", 9))
        info_label.pack(anchor=tk.W, pady=(0, 5))

        ttk.Label(relay_frame, text="API Key:").pack(anchor=tk.W)
        self.relay_key_var = tk.StringVar()
        relay_key_entry = ttk.Entry(relay_frame, textvariable=self.relay_key_var, width=50, show="*")
        relay_key_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(relay_frame, text="Base URL:").pack(anchor=tk.W)
        self.relay_url_var = tk.StringVar()
        relay_url_entry = ttk.Entry(relay_frame, textvariable=self.relay_url_var, width=50)
        relay_url_entry.pack(fill=tk.X, pady=(0, 5))

        # 添加常用URL示例
        url_examples_frame = ttk.Frame(relay_frame)
        url_examples_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(url_examples_frame, text="常用URL:", font=("Arial", 9)).pack(side=tk.LEFT)
        ttk.Button(url_examples_frame, text="通义千问",
                  command=lambda: self.relay_url_var.set("https://dashscope.aliyuncs.com/compatible-mode/v1")).pack(side=tk.LEFT, padx=(5, 2))
        ttk.Button(url_examples_frame, text="智谱AI",
                  command=lambda: self.relay_url_var.set("https://open.bigmodel.cn/api/paas/v4")).pack(side=tk.LEFT, padx=(2, 2))
        ttk.Button(url_examples_frame, text="Kimi",
                  command=lambda: self.relay_url_var.set("https://api.moonshot.cn/v1")).pack(side=tk.LEFT, padx=(2, 2))

        ttk.Label(relay_frame, text="模型:").pack(anchor=tk.W)
        self.relay_model_var = tk.StringVar()
        self.relay_model_combo = ttk.Combobox(relay_frame, textvariable=self.relay_model_var, width=30)
        self.relay_model_combo['values'] = [
            'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext',  # 通义千问
            'glm-4', 'glm-4v', 'glm-3-turbo',  # 智谱AI
            'moonshot-v1-8k', 'moonshot-v1-32k', 'moonshot-v1-128k',  # Kimi
            'gpt-4', 'gpt-3.5-turbo'  # 通用模型名
        ]
        self.relay_model_combo.pack(fill=tk.X, pady=(0, 5))

        # 自定义模型管理
        custom_model_frame = ttk.Frame(relay_frame)
        custom_model_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(custom_model_frame, text="自定义模型:", font=("Arial", 9)).pack(side=tk.LEFT)
        self.custom_model_var = tk.StringVar()
        custom_model_entry = ttk.Entry(custom_model_frame, textvariable=self.custom_model_var, width=25)
        custom_model_entry.pack(side=tk.LEFT, padx=(5, 5))

        ttk.Button(custom_model_frame, text="添加到列表",
                  command=self.add_custom_model).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(custom_model_frame, text="管理模型",
                  command=self.manage_custom_models).pack(side=tk.LEFT)

        button_frame_relay = ttk.Frame(relay_frame)
        button_frame_relay.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(button_frame_relay, text="测试连接",
                  command=lambda: self.test_api_connection("relay")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame_relay, text="专业配置",
                  command=self.open_professional_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame_relay, text="网络诊断",
                  command=self.test_relay_network).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame_relay, text="获取模型列表",
                  command=lambda: self.refresh_models("relay")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame_relay, text="重置为默认",
                  command=self.reset_relay_models).pack(side=tk.LEFT)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_system_tab(self, notebook):
        """创建系统设置标签页"""
        system_frame = ttk.Frame(notebook)
        notebook.add(system_frame, text="系统设置")
        
        # 界面设置
        ui_frame = ttk.LabelFrame(system_frame, text="界面设置", padding=10)
        ui_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 窗口大小
        size_frame = ttk.Frame(ui_frame)
        size_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(size_frame, text="窗口宽度:").pack(side=tk.LEFT)
        self.window_width_var = tk.StringVar()
        ttk.Entry(size_frame, textvariable=self.window_width_var, width=10).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(size_frame, text="窗口高度:").pack(side=tk.LEFT)
        self.window_height_var = tk.StringVar()
        ttk.Entry(size_frame, textvariable=self.window_height_var, width=10).pack(side=tk.LEFT, padx=(5, 0))
        
        # 字体大小
        font_frame = ttk.Frame(ui_frame)
        font_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(font_frame, text="字体大小:").pack(side=tk.LEFT)
        self.font_size_var = tk.StringVar()
        ttk.Entry(font_frame, textvariable=self.font_size_var, width=10).pack(side=tk.LEFT, padx=(5, 0))
        
        # 考试设置
        exam_frame = ttk.LabelFrame(system_frame, text="考试设置", padding=10)
        exam_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 默认考试时间
        time_frame = ttk.Frame(exam_frame)
        time_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(time_frame, text="默认考试时间(分钟):").pack(side=tk.LEFT)
        self.default_exam_time_var = tk.StringVar()
        ttk.Entry(time_frame, textvariable=self.default_exam_time_var, width=10).pack(side=tk.LEFT, padx=(5, 0))
        
        # 默认题目数量
        questions_frame = ttk.Frame(exam_frame)
        questions_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(questions_frame, text="默认题目数量:").pack(side=tk.LEFT)
        self.questions_per_exam_var = tk.StringVar()
        ttk.Entry(questions_frame, textvariable=self.questions_per_exam_var, width=10).pack(side=tk.LEFT, padx=(5, 0))
        
        # 自动保存间隔
        save_frame = ttk.Frame(exam_frame)
        save_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(save_frame, text="自动保存间隔(秒):").pack(side=tk.LEFT)
        self.auto_save_interval_var = tk.StringVar()
        ttk.Entry(save_frame, textvariable=self.auto_save_interval_var, width=10).pack(side=tk.LEFT, padx=(5, 0))
    
    def create_data_tab(self, notebook):
        """创建数据管理标签页"""
        data_frame = ttk.Frame(notebook)
        notebook.add(data_frame, text="数据管理")
        
        # 数据库信息
        db_frame = ttk.LabelFrame(data_frame, text="数据库信息", padding=10)
        db_frame.pack(fill=tk.X, pady=(0, 10))
        
        db_path = self.config.get('DATABASE', 'db_path', 'data/exam_system.db')
        ttk.Label(db_frame, text=f"数据库路径: {db_path}").pack(anchor=tk.W)
        
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            size_mb = size / (1024 * 1024)
            ttk.Label(db_frame, text=f"数据库大小: {size_mb:.2f} MB").pack(anchor=tk.W)
        
        # 数据操作
        operations_frame = ttk.LabelFrame(data_frame, text="数据操作", padding=10)
        operations_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(operations_frame, text="备份数据库", command=self.backup_database).pack(fill=tk.X, pady=2)
        ttk.Button(operations_frame, text="恢复数据库", command=self.restore_database).pack(fill=tk.X, pady=2)
        ttk.Button(operations_frame, text="清空所有数据", command=self.clear_all_data).pack(fill=tk.X, pady=2)
        ttk.Button(operations_frame, text="重建数据库", command=self.rebuild_database).pack(fill=tk.X, pady=2)
        
        # 统计信息
        stats_frame = ttk.LabelFrame(data_frame, text="数据统计", padding=10)
        stats_frame.pack(fill=tk.BOTH, expand=True)
        
        self.stats_text = tk.Text(stats_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        ttk.Button(stats_frame, text="刷新统计", command=self.update_statistics).pack(pady=(10, 0))
    
    def create_test_tab(self, notebook):
        """创建测试工具标签页"""
        test_frame = ttk.Frame(notebook)
        notebook.add(test_frame, text="测试工具")
        
        # 测试数据生成
        test_data_frame = ttk.LabelFrame(test_frame, text="测试数据生成", padding=10)
        test_data_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(test_data_frame, text="生成示例数据用于测试系统功能").pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Button(test_data_frame, text="生成学习材料", command=self.generate_test_materials).pack(fill=tk.X, pady=2)
        ttk.Button(test_data_frame, text="生成示例考试", command=self.generate_test_exam).pack(fill=tk.X, pady=2)
        ttk.Button(test_data_frame, text="生成错题数据", command=self.generate_test_wrong_questions).pack(fill=tk.X, pady=2)
        ttk.Button(test_data_frame, text="生成知识点", command=self.generate_test_knowledge_points).pack(fill=tk.X, pady=2)
        ttk.Button(test_data_frame, text="生成所有测试数据", command=self.generate_all_test_data).pack(fill=tk.X, pady=(10, 0))
        
        # API测试
        api_test_frame = ttk.LabelFrame(test_frame, text="API测试", padding=10)
        api_test_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(api_test_frame, text="测试所有API连接", command=self.test_all_apis).pack(fill=tk.X, pady=2)
        
        # 测试结果显示
        result_frame = ttk.LabelFrame(test_frame, text="测试结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.test_result_text = tk.Text(result_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
        result_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.test_result_text.yview)
        self.test_result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.test_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_settings(self):
        """加载设置"""
        # 加载API设置
        api_config = self.config.get_api_config()
        self.openai_key_var.set(api_config.get('openai_api_key', ''))
        self.openai_url_var.set(api_config.get('openai_base_url', ''))
        self.openai_model_var.set(api_config.get('openai_model', ''))
        self.deepseek_key_var.set(api_config.get('deepseek_api_key', ''))
        self.deepseek_url_var.set(api_config.get('deepseek_base_url', ''))
        self.deepseek_model_var.set(api_config.get('deepseek_model', ''))
        self.gemini_key_var.set(api_config.get('gemini_api_key', ''))
        self.gemini_model_var.set(api_config.get('gemini_model', ''))
        self.doubao_key_var.set(api_config.get('doubao_api_key', ''))
        self.doubao_endpoint_var.set(api_config.get('doubao_endpoint', ''))
        self.doubao_model_var.set(api_config.get('doubao_model', ''))
        self.relay_key_var.set(api_config.get('relay_api_key', ''))
        self.relay_url_var.set(api_config.get('relay_base_url', ''))
        self.relay_model_var.set(api_config.get('relay_model', ''))

        # 加载自定义模型
        custom_models = self.load_custom_models()
        if custom_models:
            self.relay_model_combo['values'] = custom_models

        # 加载UI设置
        ui_config = self.config.get_ui_config()
        self.window_width_var.set(str(ui_config.get('window_width', 1200)))
        self.window_height_var.set(str(ui_config.get('window_height', 800)))
        self.font_size_var.set(str(ui_config.get('font_size', 12)))
        
        # 加载考试设置
        self.default_exam_time_var.set(self.config.get('EXAM', 'default_exam_time', '60'))
        self.questions_per_exam_var.set(self.config.get('EXAM', 'questions_per_exam', '20'))
        self.auto_save_interval_var.set(self.config.get('EXAM', 'auto_save_interval', '30'))
        
        # 更新统计信息
        self.update_statistics()
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存API设置
            self.config.set('API', 'openai_api_key', self.openai_key_var.get())
            self.config.set('API', 'openai_base_url', self.openai_url_var.get())
            self.config.set('API', 'openai_model', self.openai_model_var.get())
            self.config.set('API', 'deepseek_api_key', self.deepseek_key_var.get())
            self.config.set('API', 'deepseek_base_url', self.deepseek_url_var.get())
            self.config.set('API', 'deepseek_model', self.deepseek_model_var.get())
            self.config.set('API', 'gemini_api_key', self.gemini_key_var.get())
            self.config.set('API', 'gemini_model', self.gemini_model_var.get())
            self.config.set('API', 'doubao_api_key', self.doubao_key_var.get())
            self.config.set('API', 'doubao_endpoint', self.doubao_endpoint_var.get())
            self.config.set('API', 'doubao_model', self.doubao_model_var.get())
            self.config.set('API', 'relay_api_key', self.relay_key_var.get())
            self.config.set('API', 'relay_base_url', self.relay_url_var.get())
            self.config.set('API', 'relay_model', self.relay_model_var.get())
            
            # 保存UI设置
            self.config.set('UI', 'window_width', self.window_width_var.get())
            self.config.set('UI', 'window_height', self.window_height_var.get())
            self.config.set('UI', 'font_size', self.font_size_var.get())
            
            # 保存考试设置
            self.config.set('EXAM', 'default_exam_time', self.default_exam_time_var.get())
            self.config.set('EXAM', 'questions_per_exam', self.questions_per_exam_var.get())
            self.config.set('EXAM', 'auto_save_interval', self.auto_save_interval_var.get())
            
            messagebox.showinfo("成功", "设置保存成功！\n部分设置需要重启程序后生效。")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败：{str(e)}")
    
    def reset_settings(self):
        """重置设置"""
        if messagebox.askyesno("确认", "确定要重置所有设置为默认值吗？"):
            self.config.create_default_config()
            self.load_settings()
            messagebox.showinfo("成功", "设置已重置为默认值！")
    
    def test_api_connection(self, api_name):
        """测试API连接"""
        try:
            if api_name == "openai":
                # 临时更新配置
                self.config.set('API', 'openai_api_key', self.openai_key_var.get())
                self.config.set('API', 'openai_base_url', self.openai_url_var.get())
                self.config.set('API', 'openai_model', self.openai_model_var.get())
            elif api_name == "deepseek":
                self.config.set('API', 'deepseek_api_key', self.deepseek_key_var.get())
                self.config.set('API', 'deepseek_base_url', self.deepseek_url_var.get())
                self.config.set('API', 'deepseek_model', self.deepseek_model_var.get())
            elif api_name == "gemini":
                self.config.set('API', 'gemini_api_key', self.gemini_key_var.get())
                self.config.set('API', 'gemini_model', self.gemini_model_var.get())
            elif api_name == "doubao":
                self.config.set('API', 'doubao_api_key', self.doubao_key_var.get())
                self.config.set('API', 'doubao_endpoint', self.doubao_endpoint_var.get())
                self.config.set('API', 'doubao_model', self.doubao_model_var.get())
            elif api_name == "relay":
                self.config.set('API', 'relay_api_key', self.relay_key_var.get())
                self.config.set('API', 'relay_base_url', self.relay_url_var.get())
                self.config.set('API', 'relay_model', self.relay_model_var.get())
            
            # 重新初始化AI管理器
            self.ai_manager.init_clients()
            
            # 测试连接
            success = self.ai_manager.test_client_connection(api_name)
            
            if success:
                messagebox.showinfo("成功", f"{api_name.upper()} API连接测试成功！")
            else:
                messagebox.showerror("失败", f"{api_name.upper()} API连接测试失败！")
                
        except Exception as e:
            messagebox.showerror("错误", f"测试连接时出错：{str(e)}")

    def refresh_models(self, api_name):
        """刷新模型列表"""
        try:
            # 先更新配置
            if api_name == "openai":
                self.config.set('API', 'openai_api_key', self.openai_key_var.get())
                self.config.set('API', 'openai_base_url', self.openai_url_var.get())
            elif api_name == "relay":
                self.config.set('API', 'relay_api_key', self.relay_key_var.get())
                self.config.set('API', 'relay_base_url', self.relay_url_var.get())
                self.config.set('API', 'relay_model', self.relay_model_var.get())

            # 重新初始化AI管理器
            self.ai_manager.init_clients()

            # 获取模型列表
            if api_name in self.ai_manager.clients:
                client = self.ai_manager.clients[api_name]
                models = []

                # 优先使用get_models_from_api方法（真正从API获取）
                if hasattr(client, 'get_models_from_api'):
                    print(f"🔍 使用get_models_from_api方法获取{api_name}模型列表")
                    models = client.get_models_from_api()
                elif hasattr(client, 'get_available_models'):
                    print(f"🔍 使用get_available_models方法获取{api_name}模型列表")
                    models = client.get_available_models()
                else:
                    messagebox.showinfo("提示", "该API不支持获取模型列表")
                    return

                if models:
                    # 更新对应的模型下拉框
                    if api_name == "openai":
                        self.openai_model_combo['values'] = models
                        # 显示前几个模型作为预览
                        preview = models[:5]
                        preview_text = ", ".join(preview)
                        if len(models) > 5:
                            preview_text += f"... (共{len(models)}个)"
                        messagebox.showinfo("成功", f"获取到 {len(models)} 个OpenAI模型\n\n前几个模型:\n{preview_text}")
                    elif api_name == "relay":
                        self.relay_model_combo['values'] = models
                        # 显示前几个模型作为预览
                        preview = models[:5]
                        preview_text = ", ".join(preview)
                        if len(models) > 5:
                            preview_text += f"... (共{len(models)}个)"
                        messagebox.showinfo("成功", f"获取到 {len(models)} 个中转API模型\n\n前几个模型:\n{preview_text}")
                    else:
                        messagebox.showinfo("成功", f"获取到 {len(models)} 个{api_name}模型")
                else:
                    messagebox.showwarning("警告", f"未能获取到{api_name}的模型列表\n请检查API配置和网络连接")
            else:
                messagebox.showerror("错误", "API客户端未初始化，请检查配置")

        except Exception as e:
            messagebox.showerror("错误", f"获取模型列表失败：{str(e)}")

    def test_doubao_network(self):
        """测试豆包网络连接"""
        try:
            from src.utils.network_test import test_doubao_api_connectivity, test_api_with_retry

            # 在新窗口中显示测试结果
            test_window = tk.Toplevel(self.window)
            test_window.title("豆包网络诊断")
            test_window.geometry("600x400")
            test_window.transient(self.window)

            # 创建文本框显示测试结果
            text_frame = ttk.Frame(test_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            result_text = tk.Text(text_frame, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=result_text.yview)
            result_text.configure(yscrollcommand=scrollbar.set)

            result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 按钮框架
            button_frame = ttk.Frame(test_window)
            button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

            ttk.Button(button_frame, text="关闭", command=test_window.destroy).pack(side=tk.RIGHT)

            # 开始测试
            result_text.insert(tk.END, "开始豆包网络诊断...\n\n")
            result_text.update()

            # 重定向输出到文本框
            import sys
            from io import StringIO

            old_stdout = sys.stdout
            sys.stdout = StringIO()

            try:
                # 执行网络测试
                connectivity_ok = test_doubao_api_connectivity()

                if connectivity_ok:
                    api_key = self.doubao_key_var.get()
                    model = self.doubao_model_var.get() or "doubao-seed-1-6-250615"
                    if api_key and api_key != 'your_doubao_api_key_here':
                        test_api_with_retry(api_key, model)
                    else:
                        print("\n⚠ 未配置API密钥，跳过API调用测试")

                # 获取输出
                output = sys.stdout.getvalue()
                sys.stdout = old_stdout

                result_text.insert(tk.END, output)
                result_text.insert(tk.END, "\n诊断完成！")

            except Exception as e:
                sys.stdout = old_stdout
                result_text.insert(tk.END, f"\n诊断过程中出错: {str(e)}")

            result_text.see(tk.END)

        except ImportError:
            messagebox.showerror("错误", "网络诊断模块未找到")
        except Exception as e:
            messagebox.showerror("错误", f"网络诊断失败：{str(e)}")
    
    def test_all_apis(self):
        """测试所有API连接"""
        results = self.ai_manager.test_all_connections()
        
        result_text = "API连接测试结果：\n\n"
        for api_name, success in results.items():
            status = "成功" if success else "失败"
            result_text += f"{api_name.upper()}: {status}\n"
        
        self.show_test_result(result_text)
    
    def backup_database(self):
        """备份数据库"""
        try:
            import shutil
            from datetime import datetime
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = filedialog.asksaveasfilename(
                title="保存备份文件",
                defaultextension=".db",
                initialvalue=f"exam_backup_{timestamp}.db",
                filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")]
            )
            
            if backup_file:
                db_path = self.config.get('DATABASE', 'db_path', 'data/exam_system.db')
                shutil.copy2(db_path, backup_file)
                messagebox.showinfo("成功", "数据库备份完成！")
                
        except Exception as e:
            messagebox.showerror("错误", f"备份失败：{str(e)}")
    
    def restore_database(self):
        """恢复数据库"""
        if messagebox.askyesno("确认", "恢复数据库将覆盖当前所有数据，确定继续吗？"):
            backup_file = filedialog.askopenfilename(
                title="选择备份文件",
                filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")]
            )
            
            if backup_file:
                try:
                    import shutil
                    db_path = self.config.get('DATABASE', 'db_path', 'data/exam_system.db')
                    shutil.copy2(backup_file, db_path)
                    messagebox.showinfo("成功", "数据库恢复完成！请重启程序。")
                except Exception as e:
                    messagebox.showerror("错误", f"恢复失败：{str(e)}")
    
    def clear_all_data(self):
        """清空所有数据"""
        if messagebox.askyesno("危险操作", "这将删除所有数据，包括材料、考试、错题等，确定继续吗？"):
            if messagebox.askyesno("最终确认", "此操作不可恢复！确定要清空所有数据吗？"):
                try:
                    # 删除所有表的数据
                    tables = ['materials', 'exams', 'exam_records', 'wrong_questions', 'knowledge_points']
                    for table in tables:
                        self.db.execute_update(f"DELETE FROM {table}")
                    
                    messagebox.showinfo("成功", "所有数据已清空！")
                    self.update_statistics()
                    
                except Exception as e:
                    messagebox.showerror("错误", f"清空数据失败：{str(e)}")
    
    def rebuild_database(self):
        """重建数据库"""
        if messagebox.askyesno("确认", "重建数据库将删除所有数据并重新创建表结构，确定继续吗？"):
            try:
                self.db.init_database()
                messagebox.showinfo("成功", "数据库重建完成！")
                self.update_statistics()
            except Exception as e:
                messagebox.showerror("错误", f"重建数据库失败：{str(e)}")
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            stats_text = "数据库统计信息：\n\n"
            
            # 材料统计
            materials_count = self.db.execute_query("SELECT COUNT(*) FROM materials")[0][0]
            stats_text += f"学习材料数量: {materials_count}\n"
            
            # 考试统计
            exams_count = self.db.execute_query("SELECT COUNT(*) FROM exams")[0][0]
            stats_text += f"考试数量: {exams_count}\n"
            
            # 考试记录统计
            records_count = self.db.execute_query("SELECT COUNT(*) FROM exam_records")[0][0]
            stats_text += f"考试记录数量: {records_count}\n"
            
            # 错题统计
            wrong_questions_count = self.db.execute_query("SELECT COUNT(*) FROM wrong_questions")[0][0]
            stats_text += f"错题数量: {wrong_questions_count}\n"
            
            # 知识点统计
            knowledge_points_count = self.db.execute_query("SELECT COUNT(*) FROM knowledge_points")[0][0]
            stats_text += f"知识点数量: {knowledge_points_count}\n"
            
            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            self.stats_text.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"更新统计信息失败: {e}")
    
    def generate_test_materials(self):
        """生成测试材料"""
        try:
            generator = TestDataGenerator(self.db)
            material_ids = generator.generate_sample_materials()
            self.show_test_result(f"成功生成 {len(material_ids)} 个测试材料")
            self.update_statistics()
        except Exception as e:
            self.show_test_result(f"生成测试材料失败: {str(e)}")
    
    def generate_test_exam(self):
        """生成测试考试"""
        try:
            generator = TestDataGenerator(self.db)
            exam_id = generator.generate_sample_exam()
            if exam_id:
                self.show_test_result(f"成功生成测试考试，ID: {exam_id}")
            else:
                self.show_test_result("生成测试考试失败")
            self.update_statistics()
        except Exception as e:
            self.show_test_result(f"生成测试考试失败: {str(e)}")
    
    def generate_test_wrong_questions(self):
        """生成测试错题"""
        try:
            generator = TestDataGenerator(self.db)
            question_ids = generator.generate_sample_wrong_questions()
            self.show_test_result(f"成功生成 {len(question_ids)} 道测试错题")
            self.update_statistics()
        except Exception as e:
            self.show_test_result(f"生成测试错题失败: {str(e)}")
    
    def generate_test_knowledge_points(self):
        """生成测试知识点"""
        try:
            generator = TestDataGenerator(self.db)
            point_ids = generator.generate_sample_knowledge_points()
            self.show_test_result(f"成功生成 {len(point_ids)} 个测试知识点")
            self.update_statistics()
        except Exception as e:
            self.show_test_result(f"生成测试知识点失败: {str(e)}")
    
    def generate_all_test_data(self):
        """生成所有测试数据"""
        try:
            generator = TestDataGenerator(self.db)
            results = generator.generate_all_sample_data()
            
            result_text = "测试数据生成完成：\n\n"
            result_text += f"学习材料: {len(results['materials'])} 个\n"
            result_text += f"考试: {'1个' if results['exam'] else '0个'}\n"
            result_text += f"错题: {len(results['wrong_questions'])} 道\n"
            result_text += f"知识点: {len(results['knowledge_points'])} 个\n"
            
            self.show_test_result(result_text)
            self.update_statistics()
            
        except Exception as e:
            self.show_test_result(f"生成所有测试数据失败: {str(e)}")
    
    def show_test_result(self, text):
        """显示测试结果"""
        self.test_result_text.config(state=tk.NORMAL)
        self.test_result_text.insert(tk.END, f"{text}\n\n")
        self.test_result_text.see(tk.END)
        self.test_result_text.config(state=tk.DISABLED)

    def add_custom_model(self):
        """添加自定义模型到列表"""
        model_name = self.custom_model_var.get().strip()
        if not model_name:
            messagebox.showwarning("警告", "请输入模型名称")
            return

        # 获取当前模型列表
        current_models = list(self.relay_model_combo['values'])

        # 检查是否已存在
        if model_name in current_models:
            messagebox.showinfo("提示", f"模型 '{model_name}' 已存在")
            return

        # 添加到列表
        current_models.append(model_name)
        self.relay_model_combo['values'] = current_models

        # 设置为当前选择
        self.relay_model_combo.set(model_name)

        # 清空输入框
        self.custom_model_var.set("")

        # 保存到配置
        self.save_custom_models(current_models)

        messagebox.showinfo("成功", f"已添加自定义模型: {model_name}")

    def save_custom_models(self, models):
        """保存自定义模型到配置文件"""
        try:
            config = self.config_manager.load_config()
            if 'custom_models' not in config:
                config['custom_models'] = {}
            config['custom_models']['relay'] = models
            self.config_manager.save_config(config)
        except Exception as e:
            print(f"保存自定义模型失败: {e}")

    def load_custom_models(self):
        """加载自定义模型"""
        try:
            config = self.config_manager.load_config()
            if 'custom_models' in config and 'relay' in config['custom_models']:
                return config['custom_models']['relay']
        except Exception as e:
            print(f"加载自定义模型失败: {e}")
        return []

    def reset_relay_models(self):
        """重置为默认模型列表"""
        if messagebox.askyesno("确认", "确定要重置为默认模型列表吗？这将清除所有自定义模型。"):
            # 获取默认模型列表
            if "relay" in self.ai_manager.clients:
                client = self.ai_manager.clients["relay"]
                default_models = client._get_default_models()
                self.relay_model_combo['values'] = default_models
                self.save_custom_models(default_models)
                messagebox.showinfo("成功", "已重置为默认模型列表")

    def manage_custom_models(self):
        """管理自定义模型窗口"""
        manage_window = tk.Toplevel(self)
        manage_window.title("管理自定义模型")
        manage_window.geometry("500x400")
        manage_window.transient(self)
        manage_window.grab_set()

        # 模型列表
        list_frame = ttk.Frame(manage_window)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(list_frame, text="当前模型列表:", font=("Arial", 10, "bold")).pack(anchor=tk.W)

        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        scrollbar = ttk.Scrollbar(list_container)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        model_listbox = tk.Listbox(list_container, yscrollcommand=scrollbar.set)
        model_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=model_listbox.yview)

        # 填充当前模型
        current_models = list(self.relay_model_combo['values'])
        for model in current_models:
            model_listbox.insert(tk.END, model)

        # 按钮框架
        button_frame = ttk.Frame(manage_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def delete_selected():
            selection = model_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请选择要删除的模型")
                return

            model_name = model_listbox.get(selection[0])
            if messagebox.askyesno("确认", f"确定要删除模型 '{model_name}' 吗？"):
                model_listbox.delete(selection[0])
                # 更新下拉框
                updated_models = [model_listbox.get(i) for i in range(model_listbox.size())]
                self.relay_model_combo['values'] = updated_models
                self.save_custom_models(updated_models)
                messagebox.showinfo("成功", f"已删除模型: {model_name}")

        def add_preset_models():
            """添加预设的常用模型"""
            preset_models = [
                # 通义千问系列
                'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext',
                'qwen2-72b-instruct', 'qwen2-7b-instruct', 'qwen2-1.5b-instruct',
                # 智谱AI系列
                'glm-4', 'glm-4v', 'glm-3-turbo', 'chatglm3-6b',
                # Kimi系列
                'moonshot-v1-8k', 'moonshot-v1-32k', 'moonshot-v1-128k',
                # Claude系列
                'claude-3-haiku', 'claude-3-sonnet', 'claude-3-opus',
                # GPT系列
                'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o',
                # 其他常用模型
                'deepseek-chat', 'yi-34b-chat', 'baichuan2-13b-chat'
            ]

            current_models = [model_listbox.get(i) for i in range(model_listbox.size())]
            added_count = 0

            for model in preset_models:
                if model not in current_models:
                    model_listbox.insert(tk.END, model)
                    current_models.append(model)
                    added_count += 1

            if added_count > 0:
                self.relay_model_combo['values'] = current_models
                self.save_custom_models(current_models)
                messagebox.showinfo("成功", f"已添加 {added_count} 个预设模型")
            else:
                messagebox.showinfo("提示", "所有预设模型都已存在")

        ttk.Button(button_frame, text="删除选中", command=delete_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="添加预设模型", command=add_preset_models).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", command=manage_window.destroy).pack(side=tk.RIGHT)

    def test_relay_network(self):
        """测试中转API网络连接"""
        try:
            from src.utils.relay_network_test import test_relay_api_connectivity, get_troubleshooting_tips

            # 在新窗口中显示测试结果
            test_window = tk.Toplevel(self.window)
            test_window.title("中转API网络诊断")
            test_window.geometry("700x500")
            test_window.transient(self.window)

            # 创建文本框显示测试结果
            text_frame = ttk.Frame(test_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(text_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            text_widget = tk.Text(text_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set,
                                font=("Consolas", 9))
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.config(command=text_widget.yview)

            # 按钮框架
            button_frame = ttk.Frame(test_window)
            button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

            def run_diagnosis():
                """运行诊断"""
                text_widget.delete(1.0, tk.END)
                text_widget.insert(tk.END, "🚀 开始中转API网络诊断...\n\n")
                text_widget.update()

                # 获取当前配置
                base_url = self.relay_url_var.get().strip()
                api_key = self.relay_key_var.get().strip()
                model = self.relay_model_var.get().strip()

                if not all([base_url, api_key, model]):
                    text_widget.insert(tk.END, "❌ 请先完整配置Base URL、API Key和模型名称\n")
                    return

                # 重定向输出到文本框
                import sys
                from io import StringIO

                old_stdout = sys.stdout
                sys.stdout = StringIO()

                try:
                    # 运行诊断
                    result = test_relay_api_connectivity(base_url, api_key, model)
                    get_troubleshooting_tips(base_url, api_key, model)

                    # 获取输出
                    output = sys.stdout.getvalue()
                    text_widget.insert(tk.END, output)

                    if result:
                        text_widget.insert(tk.END, "\n🎉 诊断完成：连接正常！\n")
                    else:
                        text_widget.insert(tk.END, "\n⚠️ 诊断完成：发现问题，请参考上述建议。\n")

                except Exception as e:
                    text_widget.insert(tk.END, f"\n❌ 诊断过程出错: {str(e)}\n")
                finally:
                    sys.stdout = old_stdout

                text_widget.see(tk.END)

            def copy_config():
                """复制配置信息"""
                config_info = f"""中转API配置信息:
Base URL: {self.relay_url_var.get()}
Model: {self.relay_model_var.get()}
API Key: {self.relay_key_var.get()[:10]}...

请检查以上配置是否正确。
"""
                test_window.clipboard_clear()
                test_window.clipboard_append(config_info)
                messagebox.showinfo("已复制", "配置信息已复制到剪贴板")

            ttk.Button(button_frame, text="开始诊断", command=run_diagnosis).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="复制配置", command=copy_config).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="关闭", command=test_window.destroy).pack(side=tk.RIGHT)

            # 显示初始信息
            text_widget.insert(tk.END, "🔍 中转API网络诊断工具\n")
            text_widget.insert(tk.END, "=" * 50 + "\n\n")
            text_widget.insert(tk.END, "此工具将全面测试您的中转API连接：\n")
            text_widget.insert(tk.END, "• DNS解析测试\n")
            text_widget.insert(tk.END, "• TCP连接测试\n")
            text_widget.insert(tk.END, "• HTTP基础连接测试\n")
            text_widget.insert(tk.END, "• API端点测试\n")
            text_widget.insert(tk.END, "• 聊天API功能测试\n\n")
            text_widget.insert(tk.END, "点击'开始诊断'按钮开始测试...\n")

        except ImportError:
            messagebox.showerror("错误", "网络诊断模块加载失败")
        except Exception as e:
            messagebox.showerror("错误", f"启动网络诊断失败: {str(e)}")

    def open_professional_config(self):
        """打开专业API配置界面"""
        try:
            from src.ui.professional_api_config import ProfessionalAPIConfig

            # 创建并显示专业配置界面
            professional_config = ProfessionalAPIConfig(self.window, self.config)
            professional_config.show()

        except ImportError as e:
            messagebox.showerror("错误", f"专业配置模块加载失败: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"启动专业配置失败: {str(e)}")
