#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试考试系统数据库
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_database():
    """调试数据库内容"""
    try:
        from src.utils.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.utils.config_manager import ConfigManager
        
        print("=== 考试系统数据库调试 ===")
        
        # 初始化管理器
        config = ConfigManager()
        db = DatabaseManager(config)
        exam_manager = ExamManager(db)
        
        print("✓ 管理器初始化成功")
        
        # 检查数据库表
        print("\n1. 检查数据库表结构...")
        tables = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        print(f"数据库表: {[table[0] for table in tables]}")
        
        # 检查exams表结构
        if any('exams' in table for table in tables):
            columns = db.execute_query("PRAGMA table_info(exams)")
            print("exams表结构:")
            for col in columns:
                print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
        
        # 检查现有考试数据
        print("\n2. 检查现有考试数据...")
        exams = exam_manager.get_all_exams()
        print(f"数据库中的考试数量: {len(exams)}")
        
        for i, exam in enumerate(exams):
            print(f"考试 {i+1}: ID={exam[0]}, 标题='{exam[1]}', 时间={exam[3]}分钟")
        
        # 测试创建考试
        print("\n3. 测试创建考试...")
        test_questions = [
            {
                "type": "single_choice",
                "question": "测试题目：Python是什么？",
                "options": ["编程语言", "动物", "食物", "工具"],
                "correct_answer": "A",
                "explanation": "Python是一种编程语言",
                "score": 1
            },
            {
                "type": "true_false",
                "question": "测试题目：Python是开源的。",
                "correct_answer": "对",
                "explanation": "Python确实是开源的",
                "score": 1
            }
        ]
        
        try:
            exam_id = exam_manager.create_exam(
                title="调试测试考试",
                description="用于调试的测试考试",
                questions=test_questions,
                time_limit=30
            )
            print(f"✓ 测试考试创建成功，ID: {exam_id}")
            
            # 验证考试是否保存
            saved_exam = exam_manager.get_exam_by_id(exam_id)
            if saved_exam:
                print(f"✓ 考试保存验证成功")
                print(f"  标题: {saved_exam['title']}")
                print(f"  题目数量: {len(saved_exam['questions'])}")
            else:
                print("✗ 考试保存验证失败")
            
            # 检查更新后的考试列表
            updated_exams = exam_manager.get_all_exams()
            print(f"✓ 更新后考试数量: {len(updated_exams)}")
            
        except Exception as e:
            print(f"✗ 创建测试考试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # 检查材料数据
        print("\n4. 检查材料数据...")
        from src.core.material_manager import MaterialManager
        material_manager = MaterialManager(db)
        materials = material_manager.get_all_materials()
        print(f"数据库中的材料数量: {len(materials)}")
        
        if not materials:
            print("⚠ 没有学习材料，这可能影响试卷生成")
        
        print("\n=== 调试完成 ===")
        
    except Exception as e:
        print(f"✗ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_database()
