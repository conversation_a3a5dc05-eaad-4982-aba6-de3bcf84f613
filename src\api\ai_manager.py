#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI管理器
统一管理各种AI API客户端
"""

from typing import List, Dict, Any, Optional
from src.api.openai_client import OpenAIClient
from src.api.deepseek_client import DeepSeekClient
from src.api.gemini_client import Gemini<PERSON><PERSON>
from src.api.doubao_client import DoubaoClient
from src.api.enhanced_doubao_client import EnhancedDoubaoC<PERSON>
from src.api.relay_api_client import RelayAPIClient

class AIManager:
    """AI管理器"""
    
    def __init__(self, config_manager):
        """初始化AI管理器"""
        self.config = config_manager
        self.clients = {}
        self.current_client = None
        
        self.init_clients()
    
    def init_clients(self):
        """初始化AI客户端"""
        api_config = self.config.get_api_config()

        # 初始化OpenAI客户端
        if api_config['openai_api_key'] and api_config['openai_api_key'] != 'your_openai_api_key_here':
            try:
                self.clients['openai'] = OpenAIClient(
                    api_key=api_config['openai_api_key'],
                    base_url=api_config['openai_base_url'],
                    model=api_config.get('openai_model', 'gpt-3.5-turbo')
                )
            except Exception as e:
                print(f"OpenAI客户端初始化失败: {e}")

        # 初始化DeepSeek客户端
        if api_config['deepseek_api_key'] and api_config['deepseek_api_key'] != 'your_deepseek_api_key_here':
            try:
                client = DeepSeekClient(
                    api_key=api_config['deepseek_api_key'],
                    base_url=api_config['deepseek_base_url']
                )
                # 设置模型（如果DeepSeek客户端支持）
                if hasattr(client, 'set_model') and api_config.get('deepseek_model'):
                    client.set_model(api_config['deepseek_model'])
                self.clients['deepseek'] = client
            except Exception as e:
                print(f"DeepSeek客户端初始化失败: {e}")

        # 初始化Gemini客户端
        if api_config['gemini_api_key'] and api_config['gemini_api_key'] != 'your_gemini_api_key_here':
            try:
                client = GeminiClient(
                    api_key=api_config['gemini_api_key']
                )
                # 设置模型（如果Gemini客户端支持）
                if hasattr(client, 'set_model') and api_config.get('gemini_model'):
                    client.set_model(api_config['gemini_model'])
                self.clients['gemini'] = client
            except Exception as e:
                print(f"Gemini客户端初始化失败: {e}")

        # 初始化Doubao客户端（使用增强版本）
        if (api_config['doubao_api_key'] and api_config['doubao_api_key'] != 'your_doubao_api_key_here' and
            api_config['doubao_endpoint'] and api_config['doubao_endpoint'] != 'your_doubao_endpoint_here'):
            try:
                client = EnhancedDoubaoClient(
                    api_key=api_config['doubao_api_key'],
                    endpoint=api_config['doubao_endpoint'],
                    model=api_config.get('doubao_model', 'doubao-seed-1-6-flash-250615')
                )
                self.clients['doubao'] = client
                print("✓ 增强豆包客户端初始化成功")
            except Exception as e:
                print(f"✗ 增强豆包客户端初始化失败: {e}")
                # 如果增强版本失败，尝试使用原版本
                try:
                    client = DoubaoClient(
                        api_key=api_config['doubao_api_key'],
                        endpoint=api_config['doubao_endpoint'],
                        model=api_config.get('doubao_model', 'doubao-seed-1-6-flash-250615')
                    )
                    self.clients['doubao'] = client
                    print("✓ 标准豆包客户端初始化成功")
                except Exception as e2:
                    print(f"✗ 标准豆包客户端也初始化失败: {e2}")

        # 初始化中转API客户端
        if (api_config['relay_api_key'] and api_config['relay_api_key'] != 'your_relay_api_key_here' and
            api_config['relay_base_url']):
            try:
                client = RelayAPIClient(
                    api_key=api_config['relay_api_key'],
                    base_url=api_config['relay_base_url'],
                    model=api_config.get('relay_model', 'qwen-turbo')
                )
                self.clients['relay'] = client
                print("✓ 中转API客户端初始化成功")
            except Exception as e:
                print(f"✗ 中转API客户端初始化失败: {e}")

        # 设置默认客户端
        if self.clients:
            self.current_client = list(self.clients.keys())[0]
    
    def get_available_clients(self) -> List[str]:
        """获取可用的AI客户端列表"""
        return list(self.clients.keys())
    
    def set_current_client(self, client_name: str) -> bool:
        """设置当前使用的AI客户端"""
        if client_name in self.clients:
            self.current_client = client_name
            return True
        return False
    
    def get_current_client_name(self) -> Optional[str]:
        """获取当前客户端名称"""
        return self.current_client
    
    def test_client_connection(self, client_name: str) -> bool:
        """测试指定客户端的连接"""
        if client_name in self.clients:
            return self.clients[client_name].test_connection()
        return False
    
    def test_all_connections(self) -> Dict[str, bool]:
        """测试所有客户端的连接"""
        results = {}
        for name, client in self.clients.items():
            results[name] = client.test_connection()
        return results
    
    def generate_questions(self, material: str, question_types: List[str], 
                          num_questions: int = 20, client_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """生成题目"""
        client_name = client_name or self.current_client
        
        if not client_name or client_name not in self.clients:
            raise Exception("没有可用的AI客户端")
        
        client = self.clients[client_name]
        return client.generate_questions(material, question_types, num_questions)
    
    def evaluate_answer(self, question: str, answer: str, correct_answer: str, 
                       client_name: Optional[str] = None) -> Dict[str, Any]:
        """评估答案"""
        client_name = client_name or self.current_client
        
        if not client_name or client_name not in self.clients:
            return {
                "score": 5,
                "max_score": 10,
                "feedback": "没有可用的AI客户端进行评估"
            }
        
        client = self.clients[client_name]
        return client.evaluate_answer(question, answer, correct_answer)
    
    def get_client_info(self) -> Dict[str, Any]:
        """获取客户端信息"""
        info = {
            'available_clients': list(self.clients.keys()),
            'current_client': self.current_client,
            'total_clients': len(self.clients)
        }
        
        # 测试连接状态
        connection_status = {}
        for name in self.clients.keys():
            try:
                connection_status[name] = self.test_client_connection(name)
            except:
                connection_status[name] = False
        
        info['connection_status'] = connection_status
        return info
    
    def is_available(self) -> bool:
        """检查是否有可用的AI客户端"""
        return len(self.clients) > 0 and self.current_client is not None
    
    def get_available_models(self, client_name: str) -> List[str]:
        """获取指定客户端的可用模型"""
        if client_name not in self.clients:
            return []

        client = self.clients[client_name]
        if hasattr(client, 'get_available_models'):
            return client.get_available_models()
        elif hasattr(client, 'get_models_from_api'):
            return client.get_models_from_api()
        else:
            return []

    def set_client_model(self, client_name: str, model: str) -> bool:
        """设置客户端使用的模型"""
        if client_name not in self.clients:
            return False

        client = self.clients[client_name]
        if hasattr(client, 'set_model'):
            client.set_model(model)
            # 同时更新配置文件
            config_key = f"{client_name}_model"
            self.config.set('API', config_key, model)
            return True
        return False

    def get_client_model(self, client_name: str) -> str:
        """获取客户端当前使用的模型"""
        if client_name not in self.clients:
            return ""

        client = self.clients[client_name]
        if hasattr(client, 'model'):
            return client.model
        else:
            # 从配置文件获取
            config_key = f"{client_name}_model"
            return self.config.get('API', config_key, '')

    def get_all_client_models(self) -> Dict[str, Dict[str, Any]]:
        """获取所有客户端的模型信息"""
        result = {}
        for client_name in self.clients.keys():
            result[client_name] = {
                'current_model': self.get_client_model(client_name),
                'available_models': self.get_available_models(client_name)
            }
        return result

    def get_supported_question_types(self) -> List[Dict[str, str]]:
        """获取支持的题目类型"""
        return [
            {'type': 'single_choice', 'name': '单选题', 'description': '4个选项，只有一个正确答案'},
            {'type': 'multiple_choice', 'name': '多选题', 'description': '4个选项，可能有多个正确答案'},
            {'type': 'true_false', 'name': '判断题', 'description': '对或错'},
            {'type': 'short_answer', 'name': '简答题', 'description': '需要简短回答'},
            {'type': 'case_analysis', 'name': '案例分析题', 'description': '需要详细分析'}
        ]
