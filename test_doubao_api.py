#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包API测试脚本
用于测试豆包API的连接和调用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.doubao_client import DoubaoClient

def test_doubao_api():
    """测试豆包API"""
    
    # 从配置文件读取API信息
    api_key = "2f62a42c-6ea7-4c2e-8b51-9d2e93f6e9c6"
    endpoint = "https://ark.cn-beijing.volces.com/api/v3"
    model = "doubao-pro-4k"
    
    print("=== 豆包API测试 ===")
    print(f"API Key: {api_key[:10]}...")
    print(f"Endpoint: {endpoint}")
    print(f"Model: {model}")
    print()
    
    try:
        # 创建客户端
        client = DoubaoClient(api_key=api_key, endpoint=endpoint, model=model)
        print("✓ 客户端创建成功")
        
        # 测试连接
        print("正在测试连接...")
        if client.test_connection():
            print("✓ 连接测试成功")
        else:
            print("✗ 连接测试失败")
            return
        
        # 测试题目生成
        print("\n正在测试题目生成...")
        material = """
        Python是一种高级编程语言，具有简洁的语法和强大的功能。
        Python支持面向对象编程，也支持函数式编程。
        Python有丰富的标准库和第三方库。
        """
        
        questions = client.generate_questions(
            material=material,
            question_types=['single_choice', 'true_false'],
            num_questions=3
        )
        
        if questions:
            print(f"✓ 成功生成 {len(questions)} 道题目")
            for i, q in enumerate(questions, 1):
                print(f"\n题目 {i}:")
                print(f"  类型: {q.get('type', 'unknown')}")
                print(f"  题目: {q.get('question', 'N/A')[:50]}...")
                print(f"  答案: {q.get('correct_answer', 'N/A')}")
        else:
            print("✗ 题目生成失败")
        
        # 测试答案评估
        print("\n正在测试答案评估...")
        evaluation = client.evaluate_answer(
            question="Python是什么？",
            answer="Python是一种编程语言",
            correct_answer="Python是一种高级编程语言，具有简洁的语法"
        )
        
        print(f"✓ 评估完成")
        print(f"  得分: {evaluation.get('score', 0)}/{evaluation.get('max_score', 10)}")
        print(f"  反馈: {evaluation.get('feedback', 'N/A')[:100]}...")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_doubao_api()
