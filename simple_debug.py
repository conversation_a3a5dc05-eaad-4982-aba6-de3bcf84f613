#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import json

def check_database():
    """检查数据库"""
    print("=== 检查数据库 ===")
    
    db_path = "data/exam_system.db"
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
        
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查错题表
        cursor.execute("SELECT COUNT(*) FROM wrong_questions")
        wrong_count = cursor.fetchone()[0]
        print(f"错题数量: {wrong_count}")
        
        # 检查考试记录表
        cursor.execute("SELECT COUNT(*) FROM exam_records")
        record_count = cursor.fetchone()[0]
        print(f"考试记录数量: {record_count}")
        
        # 检查最新的考试记录
        cursor.execute("""
            SELECT er.id, er.exam_id, er.answers, er.score, er.total_score, er.created_at
            FROM exam_records er
            ORDER BY er.created_at DESC LIMIT 1
        """)
        
        latest_record = cursor.fetchone()
        if latest_record:
            print(f"最新考试记录:")
            print(f"  ID: {latest_record[0]}")
            print(f"  考试ID: {latest_record[1]}")
            print(f"  分数: {latest_record[3]}/{latest_record[4]}")
            print(f"  时间: {latest_record[5]}")
            
            # 解析答案
            try:
                answers = json.loads(latest_record[2])
                print(f"  答案: {answers}")
                
                # 检查是否有错误答案
                wrong_answers = []
                for key, value in answers.items():
                    if value != "B":  # 假设正确答案是B
                        wrong_answers.append((key, value))
                
                print(f"  可能的错误答案: {wrong_answers}")
                
            except Exception as e:
                print(f"  答案解析失败: {e}")
        else:
            print("没有考试记录")
            
        # 检查错题表结构
        cursor.execute("PRAGMA table_info(wrong_questions)")
        columns = cursor.fetchall()
        print(f"错题表字段:")
        for col in columns:
            print(f"  {col[1]} {col[2]}")
            
    except Exception as e:
        print(f"检查失败: {e}")
    finally:
        conn.close()

def add_test_wrong_question():
    """添加测试错题"""
    print("\n=== 添加测试错题 ===")
    
    db_path = "data/exam_system.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 直接插入一道错题
        cursor.execute("""
            INSERT INTO wrong_questions 
            (question_text, question_type, correct_answer, user_answer, explanation, exam_title, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        """, (
            "直接插入的测试错题：Python是什么？",
            "single_choice",
            "编程语言",
            "动物",
            "Python是一种编程语言，不是动物",
            "手动测试试卷"
        ))
        
        conn.commit()
        question_id = cursor.lastrowid
        print(f"✅ 成功添加测试错题，ID: {question_id}")
        
        # 验证添加结果
        cursor.execute("SELECT COUNT(*) FROM wrong_questions")
        count = cursor.fetchone()[0]
        print(f"现在错题总数: {count}")
        
    except Exception as e:
        print(f"添加测试错题失败: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
    add_test_wrong_question()
    check_database()
