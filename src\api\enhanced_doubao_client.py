#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的豆包API客户端
基于官方文档的标准实现
"""

import requests
import json
import time
from typing import List, Dict, Any
from src.api.base_ai_client import BaseAIClient

class EnhancedDoubaoClient(BaseAIClient):
    """增强的豆包API客户端"""
    
    def __init__(self, api_key: str, endpoint: str = "https://ark.cn-beijing.volces.com/api/v3", 
                 model: str = "doubao-seed-1-6-flash-250615"):
        self.api_key = api_key
        self.endpoint = endpoint.rstrip('/')
        self.model = model
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        try:
            # 尝试从API获取模型列表
            response = requests.get(
                f"{self.endpoint}/models",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                models_data = response.json()
                if 'data' in models_data:
                    models = [model['id'] for model in models_data['data']]
                    print(f"从API获取到 {len(models)} 个模型")
                    return models
        except Exception as e:
            print(f"获取模型列表失败: {e}")
        
        # 如果API获取失败，返回常见的豆包模型
        return [
            "doubao-seed-1-6-flash-250615",
            "doubao-seed-1-6-250615", 
            "doubao-pro-4k",
            "doubao-pro-32k",
            "doubao-pro-128k",
            "doubao-lite-4k",
            "doubao-lite-32k"
        ]
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello"
                    }
                ],
                "max_tokens": 10,
                "temperature": 0.1
            }
            
            response = requests.post(
                f"{self.endpoint}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✓ 豆包API连接测试成功")
                return True
            else:
                error_info = f"HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    if 'error' in error_detail:
                        error_info += f": {error_detail['error'].get('message', '')}"
                except:
                    error_info += f": {response.text[:100]}"
                
                print(f"✗ 豆包API连接测试失败: {error_info}")
                return False
                
        except requests.exceptions.Timeout:
            print("✗ 豆包API连接超时")
            return False
        except requests.exceptions.ConnectionError:
            print("✗ 豆包API连接失败，请检查网络")
            return False
        except Exception as e:
            print(f"✗ 豆包API测试出错: {str(e)}")
            return False
    
    def generate_questions(self, material: str, question_types: List[str], 
                          num_questions: int = 20) -> List[Dict[str, Any]]:
        """使用豆包生成题目"""
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                prompt = self.create_prompt_for_questions(material, question_types, num_questions)
                
                # 构建请求数据（严格按照豆包官方格式）
                data = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "你是一个专业的教育工作者，擅长根据学习材料生成高质量的考试题目。请严格按照要求的JSON格式返回题目。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": 0.7,
                    "max_tokens": 4000
                }
                
                print(f"正在使用豆包模型 {self.model} 生成题目...")
                
                # 发送请求到豆包API
                response = requests.post(
                    f"{self.endpoint}/chat/completions",
                    headers=self.headers,
                    json=data,
                    timeout=(10, 120)  # 连接超时10秒，读取超时120秒
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content']
                        print(f"✓ 豆包API调用成功，返回内容长度: {len(content)}")
                        
                        # 解析题目
                        questions = self.parse_questions_response(content)
                        validated_questions = self.validate_questions(questions)
                        
                        print(f"✓ 成功生成 {len(validated_questions)} 道题目")
                        return validated_questions
                    else:
                        raise Exception("API响应格式异常：缺少choices字段")
                else:
                    error_msg = f"API请求失败: {response.status_code}"
                    try:
                        error_detail = response.json()
                        if 'error' in error_detail:
                            error_msg += f" - {error_detail['error'].get('message', '')}"
                            
                            # 特殊处理模型不存在的错误
                            if "NotFound" in error_detail['error'].get('code', ''):
                                error_msg += f"\n\n可能的原因：\n1. 模型名称 '{self.model}' 不正确\n2. 您的API密钥没有访问此模型的权限\n3. 推理接入点未启用\n\n请检查豆包控制台中的推理接入点配置"
                    except:
                        error_msg += f" - {response.text[:200]}"
                    
                    if attempt < max_retries - 1:
                        print(f"豆包API请求失败，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                        continue
                    else:
                        raise Exception(error_msg)
                        
            except requests.exceptions.Timeout as e:
                if attempt < max_retries - 1:
                    print(f"豆包API连接超时，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"豆包API连接超时，请检查网络连接: {str(e)}")
            except requests.exceptions.ConnectionError as e:
                if attempt < max_retries - 1:
                    print(f"豆包API连接错误，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"豆包API连接失败，请检查网络连接: {str(e)}")
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"豆包API请求出错，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception(f"豆包生成题目失败: {str(e)}")
        
        raise Exception("豆包生成题目失败: 超过最大重试次数")
    
    def evaluate_answer(self, question: str, answer: str, correct_answer: str) -> Dict[str, Any]:
        """评估答案"""
        try:
            prompt = f"""
请评估以下答案的正确性：

题目：{question}
标准答案：{correct_answer}
学生答案：{answer}

请按以下JSON格式返回评估结果：
{{
    "score": 分数(0-10),
    "max_score": 10,
    "is_correct": true/false,
    "feedback": "详细反馈",
    "suggestions": "改进建议"
}}
"""
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的教师，擅长评估学生答案并给出建设性反馈。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }
            
            response = requests.post(
                f"{self.endpoint}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    
                    # 尝试解析JSON
                    try:
                        evaluation = json.loads(content)
                        return evaluation
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，返回基本评估
                        return {
                            "score": 5,
                            "max_score": 10,
                            "is_correct": False,
                            "feedback": content,
                            "suggestions": "请参考标准答案"
                        }
                else:
                    raise Exception("API响应格式异常")
            else:
                raise Exception(f"API请求失败: {response.status_code}")
                
        except Exception as e:
            return {
                "score": 0,
                "max_score": 10,
                "is_correct": False,
                "feedback": f"评估失败: {str(e)}",
                "suggestions": "请检查网络连接或API配置"
            }
