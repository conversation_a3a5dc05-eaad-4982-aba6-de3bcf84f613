# 📁 AI考试生成系统 - 项目结构说明

## 🚀 主要启动文件

### 传统界面启动
```bash
python main_simple.py
```
- 使用Tkinter的传统界面
- 功能完整，稳定可靠
- 适合日常使用

### 现代化界面启动
```bash
python run_modern_ui.py
```
- 使用PyQt6的现代化界面
- 美观的UI设计
- 高DPI显示器优化

## 📂 核心目录结构

```
Kaoshi/
├── main_simple.py              # 传统界面启动器
├── run_modern_ui.py           # 现代界面启动器
├── requirements.txt           # 项目依赖
├── config.ini                # 配置文件
├── 
├── src/                      # 源代码目录
│   ├── ui/                   # 用户界面
│   ├── core/                 # 核心功能
│   ├── api/                  # API接口
│   ├── utils/                # 工具函数
│   └── config/               # 配置管理
│
├── data/                     # 数据目录
│   └── exam_system.db        # 数据库文件
│
├── config/                   # 配置目录
│   └── config.ini            # 配置文件
│
├── backups/                  # 备份目录
│   └── *.zip                 # 自动备份文件
│
├── api_manager_package/      # API管理包
│   ├── core/                 # 核心功能
│   ├── ui/                   # 管理界面
│   └── examples/             # 使用示例
│
└── tests/                    # 测试目录
    └── *.py                  # 单元测试
```

## 🔧 核心功能模块

### 用户界面 (src/ui/)
- `main_window.py` - 主窗口
- `exam_selection_window.py` - 考试选择
- `exam_window.py` - 考试界面
- `wrong_questions_window.py` - 错题本
- `modern_exam_generator.py` - 现代化界面

### 核心功能 (src/core/)
- `exam_manager.py` - 考试管理
- `question_generator.py` - 题目生成
- `database_manager.py` - 数据库管理
- `wrong_question_manager.py` - 错题管理

### API接口 (src/api/)
- `doubao_api.py` - 豆包API
- `api_manager.py` - API管理器
- `model_config.py` - 模型配置

### 工具函数 (src/utils/)
- `config_manager.py` - 配置管理
- `performance_optimizer.py` - 性能优化
- `backup_manager.py` - 备份管理

## 📋 数据库文件

### 主数据库
- `data/exam_system.db` - SQLite数据库
- 包含考试、题目、错题等数据

### 数据库结构
- `database_schema.sql` - 数据库结构定义
- `database_setup.py` - 数据库初始化脚本

## ⚙️ 配置文件

### 主配置
- `config.ini` - 主配置文件
- `config/config.ini` - 备用配置

### API配置
- API密钥和端点配置
- 模型参数设置
- 系统行为配置

## 📦 扩展包

### API管理包
- `api_manager_package/` - 独立的API管理工具
- 可以单独使用或集成到其他项目

## 🗑️ 已清理的文件

### 删除的测试文件
- 所有 `test_*.py` 文件
- 调试脚本 `debug_*.py`
- 临时测试文件

### 删除的重复文件
- `main.py`, `main_optimized.py`, `main_stable.py`
- 多余的启动脚本和批处理文件
- 临时工具和配置文件

## 💡 使用建议

1. **日常使用**: 推荐使用 `python main_simple.py`
2. **美观界面**: 需要现代化界面时使用 `python run_modern_ui.py`
3. **开发调试**: 使用 `tests/` 目录下的测试文件
4. **API管理**: 使用 `api_manager_package/` 中的工具

## 🔄 备份恢复

- 系统会自动创建备份到 `backups/` 目录
- 可以从备份文件恢复完整系统
- 建议定期检查备份文件

现在项目结构更加清晰，易于维护和使用！
