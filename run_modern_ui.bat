@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🎨 现代化AI考试生成系统 - 灵动风格UI
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ 检测到Python版本：
python --version
echo.

REM 检查PyQt6是否安装
echo 🔍 检查PyQt6依赖...
python -c "import PyQt6; print('✅ PyQt6 已安装')" 2>nul
if errorlevel 1 (
    echo ⚠️ PyQt6 未安装，正在安装...
    echo.
    pip install PyQt6
    if errorlevel 1 (
        echo ❌ PyQt6 安装失败
        echo 请手动运行: pip install PyQt6
        echo.
        pause
        exit /b 1
    )
    echo ✅ PyQt6 安装完成！
) else (
    echo ✅ PyQt6 依赖检查完成
)

echo.
echo 🚀 启动现代化AI考试生成系统...
echo.
echo 💡 功能特色：
echo • 🎨 灵动风格UI设计
echo • 🌈 半透明毛玻璃背景  
echo • 💫 涟漪动效按钮
echo • 📊 实时预览更新
echo.
echo 程序窗口将在几秒钟后出现...
echo.

REM 启动程序
python demo_modern_ui.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo.
    echo 🔧 可能的解决方案：
    echo 1. 确保Python版本 >= 3.8
    echo 2. 重新安装PyQt6: pip install --upgrade PyQt6
    echo 3. 检查文件路径是否正确
    echo 4. 手动运行: python demo_modern_ui.py
    echo.
    pause
)

echo.
echo 👋 程序已退出，感谢使用！
pause
