#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快捷键管理器
提供全局快捷键支持和快捷键提示功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os

class ShortcutManager:
    def __init__(self, root):
        """初始化快捷键管理器"""
        self.root = root
        self.shortcuts = {}
        self.contexts = {}  # 不同上下文的快捷键
        self.current_context = "global"
        self.help_window = None
        
        # 加载默认快捷键
        self.load_default_shortcuts()
        
        # 绑定全局快捷键
        self.bind_global_shortcuts()
        
    def load_default_shortcuts(self):
        """加载默认快捷键配置"""
        self.shortcuts = {
            # 全局快捷键
            "global": {
                "<Control-n>": {"action": "new_exam", "desc": "新建试卷"},
                "<Control-o>": {"action": "open_exam", "desc": "开始考试"},
                "<Control-h>": {"action": "exam_history", "desc": "考试记录"},
                "<Control-w>": {"action": "wrong_questions", "desc": "错题本"},
                "<Control-s>": {"action": "settings", "desc": "系统设置"},
                "<Control-q>": {"action": "quit", "desc": "退出程序"},
                "<F1>": {"action": "help", "desc": "帮助"},
                "<F11>": {"action": "fullscreen", "desc": "全屏切换"},
                "<Control-t>": {"action": "theme_toggle", "desc": "主题切换"},
                "<Control-b>": {"action": "backup", "desc": "数据备份"},
                "<Control-r>": {"action": "refresh", "desc": "刷新界面"},
                "<Escape>": {"action": "cancel", "desc": "取消/返回"}
            },
            
            # 考试界面快捷键
            "exam": {
                "<space>": {"action": "next_question", "desc": "下一题"},
                "<BackSpace>": {"action": "prev_question", "desc": "上一题"},
                "<Return>": {"action": "submit_exam", "desc": "提交试卷"},
                "<Control-Return>": {"action": "force_submit", "desc": "强制提交"},
                "<F1>": {"action": "mark_question", "desc": "标记题目"},
                "<F2>": {"action": "show_navigation", "desc": "显示导航"},
                "<F3>": {"action": "show_timer", "desc": "显示计时器"},
                "<Control-1>": {"action": "jump_to_1", "desc": "跳转到第1题"},
                "<Control-2>": {"action": "jump_to_2", "desc": "跳转到第2题"},
                "<Control-3>": {"action": "jump_to_3", "desc": "跳转到第3题"},
                "<Control-4>": {"action": "jump_to_4", "desc": "跳转到第4题"},
                "<Control-5>": {"action": "jump_to_5", "desc": "跳转到第5题"},
                "<Left>": {"action": "prev_question", "desc": "上一题"},
                "<Right>": {"action": "next_question", "desc": "下一题"},
                "<Up>": {"action": "prev_option", "desc": "上一选项"},
                "<Down>": {"action": "next_option", "desc": "下一选项"}
            },
            
            # 错题本快捷键
            "wrong_questions": {
                "<Delete>": {"action": "delete_question", "desc": "删除错题"},
                "<Control-a>": {"action": "select_all", "desc": "全选"},
                "<Control-d>": {"action": "deselect_all", "desc": "取消全选"},
                "<F5>": {"action": "refresh_list", "desc": "刷新列表"},
                "<Control-f>": {"action": "find", "desc": "查找"},
                "<Control-e>": {"action": "export", "desc": "导出"},
                "<Enter>": {"action": "view_detail", "desc": "查看详情"},
                "<Control-c>": {"action": "copy", "desc": "复制"},
                "<Control-v>": {"action": "paste", "desc": "粘贴"}
            },
            
            # 材料管理快捷键
            "material": {
                "<Control-i>": {"action": "import_material", "desc": "导入材料"},
                "<Control-e>": {"action": "export_material", "desc": "导出材料"},
                "<Delete>": {"action": "delete_material", "desc": "删除材料"},
                "<F2>": {"action": "rename", "desc": "重命名"},
                "<Control-d>": {"action": "duplicate", "desc": "复制"},
                "<Control-f>": {"action": "search", "desc": "搜索"}
            }
        }
        
    def bind_global_shortcuts(self):
        """绑定全局快捷键"""
        for shortcut, config in self.shortcuts["global"].items():
            self.root.bind_all(shortcut, lambda e, action=config["action"]: self.handle_shortcut(action, e))
            
    def set_context(self, context):
        """设置当前上下文"""
        # 清除之前上下文的快捷键
        if self.current_context in self.shortcuts and self.current_context != "global":
            for shortcut in self.shortcuts[self.current_context]:
                try:
                    self.root.unbind_all(shortcut)
                except:
                    pass
        
        self.current_context = context
        
        # 绑定新上下文的快捷键
        if context in self.shortcuts:
            for shortcut, config in self.shortcuts[context].items():
                self.root.bind_all(shortcut, lambda e, action=config["action"]: self.handle_shortcut(action, e))
                
    def handle_shortcut(self, action, event=None):
        """处理快捷键动作"""
        try:
            # 查找注册的回调函数
            if hasattr(self, f"on_{action}"):
                callback = getattr(self, f"on_{action}")
                callback(event)
            elif action in self.contexts:
                self.contexts[action](event)
            else:
                print(f"未处理的快捷键动作: {action}")
        except Exception as e:
            print(f"快捷键处理错误: {e}")
            
    def register_action(self, action, callback):
        """注册快捷键动作回调"""
        self.contexts[action] = callback
        
    def unregister_action(self, action):
        """取消注册快捷键动作"""
        if action in self.contexts:
            del self.contexts[action]
            
    def show_help(self):
        """显示快捷键帮助"""
        if self.help_window and self.help_window.winfo_exists():
            self.help_window.lift()
            return
            
        self.help_window = tk.Toplevel(self.root)
        self.help_window.title("快捷键帮助")
        self.help_window.geometry("600x500")
        self.help_window.transient(self.root)
        self.help_window.grab_set()
        
        # 居中显示
        self.help_window.geometry("+{}+{}".format(
            self.root.winfo_x() + 100,
            self.root.winfo_y() + 50
        ))
        
        # 创建笔记本控件
        notebook = ttk.Notebook(self.help_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 为每个上下文创建标签页
        context_names = {
            "global": "全局快捷键",
            "exam": "考试界面",
            "wrong_questions": "错题本",
            "material": "材料管理"
        }
        
        for context, shortcuts in self.shortcuts.items():
            if context in context_names:
                frame = ttk.Frame(notebook)
                notebook.add(frame, text=context_names[context])
                
                # 创建快捷键列表
                self.create_shortcut_list(frame, shortcuts)
                
        # 底部按钮
        button_frame = ttk.Frame(self.help_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        close_btn = ttk.Button(button_frame, text="关闭", command=self.help_window.destroy)
        close_btn.pack(side=tk.RIGHT)
        
    def create_shortcut_list(self, parent, shortcuts):
        """创建快捷键列表"""
        # 创建树形视图
        tree = ttk.Treeview(parent, columns=("shortcut", "description"), show="headings", height=15)
        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 设置列标题
        tree.heading("shortcut", text="快捷键")
        tree.heading("description", text="功能描述")
        
        # 设置列宽
        tree.column("shortcut", width=150)
        tree.column("description", width=300)
        
        # 添加快捷键数据
        for shortcut, config in shortcuts.items():
            # 格式化快捷键显示
            display_shortcut = self.format_shortcut(shortcut)
            tree.insert("", tk.END, values=(display_shortcut, config["desc"]))
            
        # 添加滚动条
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.configure(yscrollcommand=scrollbar.set)
        
    def format_shortcut(self, shortcut):
        """格式化快捷键显示"""
        # 移除尖括号并替换键名
        formatted = shortcut.strip("<>")
        
        replacements = {
            "Control": "Ctrl",
            "Return": "Enter",
            "BackSpace": "Backspace",
            "space": "Space"
        }
        
        for old, new in replacements.items():
            formatted = formatted.replace(old, new)
            
        return formatted
        
    def create_shortcut_tooltip(self, widget, shortcuts_list):
        """为控件创建快捷键提示"""
        def show_tooltip(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            
            frame = tk.Frame(tooltip, bg="#FFFFDD", relief=tk.SOLID, borderwidth=1)
            frame.pack()
            
            label = tk.Label(frame, text=shortcuts_list, bg="#FFFFDD", font=("Microsoft YaHei", 9))
            label.pack(padx=5, pady=3)
            
            def hide_tooltip():
                tooltip.destroy()
                
            tooltip.after(3000, hide_tooltip)  # 3秒后自动隐藏
            
        widget.bind("<Control-h>", show_tooltip)
        
    def get_shortcuts_for_context(self, context):
        """获取指定上下文的快捷键"""
        return self.shortcuts.get(context, {})
        
    def add_shortcut(self, context, shortcut, action, description):
        """添加自定义快捷键"""
        if context not in self.shortcuts:
            self.shortcuts[context] = {}
            
        self.shortcuts[context][shortcut] = {
            "action": action,
            "desc": description
        }
        
        # 如果是当前上下文，立即绑定
        if context == self.current_context:
            self.root.bind_all(shortcut, lambda e: self.handle_shortcut(action, e))
            
    def remove_shortcut(self, context, shortcut):
        """移除快捷键"""
        if context in self.shortcuts and shortcut in self.shortcuts[context]:
            del self.shortcuts[context][shortcut]
            
            # 如果是当前上下文，解除绑定
            if context == self.current_context:
                try:
                    self.root.unbind_all(shortcut)
                except:
                    pass
                    
    # 默认动作处理函数
    def on_help(self, event=None):
        """显示帮助"""
        self.show_help()
        
    def on_cancel(self, event=None):
        """取消/返回"""
        # 如果有帮助窗口，关闭它
        if self.help_window and self.help_window.winfo_exists():
            self.help_window.destroy()
            return
            
        # 其他取消逻辑可以通过回调实现
        if "cancel" in self.contexts:
            self.contexts["cancel"](event)
