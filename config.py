#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
材料导入系统配置文件
包含系统的各种配置选项
"""

import os
from typing import Dict, List


class MaterialImportConfig:
    """材料导入系统配置类"""
    
    # 数据库配置
    DATABASE = {
        'type': 'sqlite',  # 数据库类型: sqlite, mysql, postgresql
        'path': 'data/materials.db',  # SQLite数据库文件路径
        'host': 'localhost',  # 其他数据库主机
        'port': 3306,  # 数据库端口
        'username': '',  # 数据库用户名
        'password': '',  # 数据库密码
        'database': 'materials'  # 数据库名
    }
    
    # 文件处理配置
    FILE_PROCESSING = {
        'max_file_size': 50 * 1024 * 1024,  # 最大文件大小 (50MB)
        'supported_text_encodings': ['utf-8', 'gbk', 'gb2312', 'big5'],  # 支持的文本编码
        'supported_file_types': {
            'text': ['.txt', '.md', '.rst'],
            'pdf': ['.pdf'],
            'doc': ['.doc', '.docx'],  # 需要额外依赖
            'excel': ['.xls', '.xlsx'],  # 需要额外依赖
            'ppt': ['.ppt', '.pptx']  # 需要额外依赖
        },
        'pdf_extraction_options': {
            'extract_images': False,  # 是否提取图片中的文字
            'preserve_layout': False,  # 是否保持布局
            'password_protected': True  # 是否支持密码保护的PDF
        }
    }
    
    # UI配置
    UI_CONFIG = {
        'window_size': {
            'main': '800x600',
            'import_dialog': '500x400',
            'detail_dialog': '600x500'
        },
        'theme': 'default',  # 界面主题
        'font': {
            'family': 'Arial',
            'size': 10,
            'title_size': 12
        },
        'colors': {
            'primary': '#007ACC',
            'secondary': '#F0F0F0',
            'success': '#28A745',
            'warning': '#FFC107',
            'error': '#DC3545'
        }
    }
    
    # 搜索配置
    SEARCH_CONFIG = {
        'enable_full_text_search': True,  # 启用全文搜索
        'search_in_content': True,  # 在内容中搜索
        'search_in_title': True,  # 在标题中搜索
        'case_sensitive': False,  # 大小写敏感
        'max_results': 100,  # 最大搜索结果数
        'highlight_matches': True  # 高亮匹配结果
    }
    
    # 导入配置
    IMPORT_CONFIG = {
        'auto_generate_title': True,  # 自动生成标题
        'title_max_length': 100,  # 标题最大长度
        'content_max_length': 1000000,  # 内容最大长度 (1MB)
        'duplicate_handling': 'ask',  # 重复处理: 'skip', 'overwrite', 'ask'
        'backup_original': True,  # 备份原始文件
        'validate_content': True  # 验证内容
    }
    
    # 导出配置
    EXPORT_CONFIG = {
        'default_format': 'txt',  # 默认导出格式
        'supported_formats': ['txt', 'md', 'html', 'json'],
        'include_metadata': True,  # 包含元数据
        'encoding': 'utf-8'  # 导出编码
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',  # 日志级别: DEBUG, INFO, WARNING, ERROR
        'file': 'logs/material_import.log',  # 日志文件
        'max_size': 10 * 1024 * 1024,  # 最大日志文件大小 (10MB)
        'backup_count': 5,  # 备份文件数量
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    }
    
    # 性能配置
    PERFORMANCE_CONFIG = {
        'batch_size': 100,  # 批处理大小
        'cache_size': 1000,  # 缓存大小
        'connection_pool_size': 5,  # 连接池大小
        'timeout': 30  # 操作超时时间（秒）
    }
    
    @classmethod
    def get_database_url(cls) -> str:
        """获取数据库连接URL"""
        db_config = cls.DATABASE
        
        if db_config['type'] == 'sqlite':
            # 确保目录存在
            db_dir = os.path.dirname(db_config['path'])
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir)
            return db_config['path']
        
        elif db_config['type'] == 'mysql':
            return f"mysql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
        
        elif db_config['type'] == 'postgresql':
            return f"postgresql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
        
        else:
            raise ValueError(f"不支持的数据库类型: {db_config['type']}")
    
    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """获取所有支持的文件扩展名"""
        extensions = []
        for file_types in cls.FILE_PROCESSING['supported_file_types'].values():
            extensions.extend(file_types)
        return extensions
    
    @classmethod
    def is_supported_file(cls, file_path: str) -> bool:
        """检查文件是否支持"""
        _, ext = os.path.splitext(file_path.lower())
        return ext in cls.get_supported_extensions()
    
    @classmethod
    def get_file_type(cls, file_path: str) -> str:
        """根据文件路径获取文件类型"""
        _, ext = os.path.splitext(file_path.lower())
        
        for file_type, extensions in cls.FILE_PROCESSING['supported_file_types'].items():
            if ext in extensions:
                return file_type
        
        return 'unknown'
    
    @classmethod
    def validate_file_size(cls, file_path: str) -> bool:
        """验证文件大小"""
        try:
            file_size = os.path.getsize(file_path)
            return file_size <= cls.FILE_PROCESSING['max_file_size']
        except OSError:
            return False
    
    @classmethod
    def setup_logging(cls):
        """设置日志配置"""
        import logging
        import logging.handlers
        
        log_config = cls.LOGGING_CONFIG
        
        # 确保日志目录存在
        log_dir = os.path.dirname(log_config['file'])
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, log_config['level']),
            format=log_config['format'],
            handlers=[
                logging.handlers.RotatingFileHandler(
                    log_config['file'],
                    maxBytes=log_config['max_size'],
                    backupCount=log_config['backup_count']
                ),
                logging.StreamHandler()
            ]
        )
        
        return logging.getLogger('MaterialImport')


# 创建全局配置实例
config = MaterialImportConfig()

# 环境变量覆盖配置
def load_config_from_env():
    """从环境变量加载配置"""
    # 数据库配置
    if os.getenv('DB_TYPE'):
        config.DATABASE['type'] = os.getenv('DB_TYPE')
    if os.getenv('DB_PATH'):
        config.DATABASE['path'] = os.getenv('DB_PATH')
    if os.getenv('DB_HOST'):
        config.DATABASE['host'] = os.getenv('DB_HOST')
    if os.getenv('DB_PORT'):
        config.DATABASE['port'] = int(os.getenv('DB_PORT'))
    if os.getenv('DB_USERNAME'):
        config.DATABASE['username'] = os.getenv('DB_USERNAME')
    if os.getenv('DB_PASSWORD'):
        config.DATABASE['password'] = os.getenv('DB_PASSWORD')
    if os.getenv('DB_DATABASE'):
        config.DATABASE['database'] = os.getenv('DB_DATABASE')
    
    # 文件处理配置
    if os.getenv('MAX_FILE_SIZE'):
        config.FILE_PROCESSING['max_file_size'] = int(os.getenv('MAX_FILE_SIZE'))
    
    # 日志配置
    if os.getenv('LOG_LEVEL'):
        config.LOGGING_CONFIG['level'] = os.getenv('LOG_LEVEL')
    if os.getenv('LOG_FILE'):
        config.LOGGING_CONFIG['file'] = os.getenv('LOG_FILE')


# 自动加载环境变量配置
load_config_from_env()


if __name__ == "__main__":
    # 配置演示
    print("=== 材料导入系统配置 ===")
    print(f"数据库类型: {config.DATABASE['type']}")
    print(f"数据库路径: {config.get_database_url()}")
    print(f"支持的文件扩展名: {config.get_supported_extensions()}")
    print(f"最大文件大小: {config.FILE_PROCESSING['max_file_size'] / 1024 / 1024:.1f}MB")
    print(f"日志级别: {config.LOGGING_CONFIG['level']}")
    
    # 测试文件类型检测
    test_files = ['test.txt', 'document.pdf', 'data.xlsx', 'unknown.xyz']
    print("\n=== 文件类型检测测试 ===")
    for file_path in test_files:
        file_type = config.get_file_type(file_path)
        supported = config.is_supported_file(file_path)
        print(f"{file_path}: {file_type} ({'支持' if supported else '不支持'})")
