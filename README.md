# 考试系统 (Kaoshi)

一个基于Tkinter的智能考试系统，支持多种AI API生成试卷，具备完整的考试、评分、错题本等功能。

## 功能特性

- 📚 **材料导入**: 支持文本和PDF格式的学习材料导入
- 🤖 **AI试卷生成**: 集成OpenAI、DeepSeek、Gemini、Doubao等AI API自动生成试卷
- 📝 **多题型支持**: 选择题、判断题、简答题、案例分析题
- ⏱️ **考试功能**: 计时考试、自动评分、详细解析
- 📖 **错题本**: 收藏错题，重点复习
- 🕸️ **知识图谱**: 可视化知识点关系
- 💾 **数据管理**: 备份、恢复、清除数据
- 📄 **导出功能**: 支持Word、HTML格式导出

## 项目结构

```
Kaoshi/
├── main.py                 # 主程序入口
├── config.ini             # 配置文件
├── requirements.txt       # 依赖包列表
├── README.md             # 项目说明
├── src/                  # 源代码目录
│   ├── ui/              # 用户界面模块
│   ├── core/            # 核心功能模块
│   ├── utils/           # 工具模块
│   └── api/             # API接口模块
├── data/                # 数据存储目录
└── tests/               # 测试文件目录
```

## 安装和运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置API密钥：
编辑 `config.ini` 文件，填入相应的API密钥

3. 运行程序：
```bash
python main.py
```

## 开发进度

- [x] 项目初始化和基础结构
- [x] 步骤1：设置主窗口和基本布局
- [x] 步骤2：实现各个功能模块
- [x] 步骤3：集成AI API
- [x] 步骤4：实现考试界面
- [x] 步骤5：实现知识图谱可视化
- [x] 步骤6：测试和优化

## 功能详情

### 1. 材料管理
- 支持导入文本文件和PDF文件
- 材料搜索和编辑功能
- 材料统计和管理

### 2. AI试卷生成
- 支持OpenAI、DeepSeek、Gemini、Doubao等多种AI API
- 支持模型选择：可为每个AI服务选择不同的模型
- 支持中转API：可配置自定义API端点
- 可选择题目类型：单选题、多选题、判断题、简答题、案例分析题
- 自定义试卷标题、时间限制和题目数量

### 3. 在线考试
- 实时计时功能
- 题目导航和标记
- 自动保存答案
- 考试结果统计和分析

### 4. 错题本
- 自动收集错题
- 错题分类和搜索
- 收藏重要题目
- 错题统计分析

### 5. 知识图谱
- 从材料自动提取知识点
- 可视化知识点关系
- 多种布局算法
- 交互式图谱浏览

### 6. 系统管理
- API配置和测试
- 数据备份和恢复
- 测试数据生成
- 系统设置管理

## 使用说明

### 首次使用

1. **配置API密钥**
   - 点击菜单栏"设置" -> "系统设置" -> "API设置"
   - 支持的AI服务：
     - OpenAI：支持官方API和中转API，可选择GPT模型
     - DeepSeek：国产AI服务
     - Gemini：Google AI服务
     - Doubao：字节跳动AI服务
   - 填入相应AI服务的API密钥和端点
   - 选择合适的模型
   - 测试连接确保配置正确

2. **生成测试数据**
   - 点击菜单栏"设置" -> "系统设置"
   - 切换到"测试工具"标签页
   - 点击"生成所有测试数据"

### 基本流程

1. **导入学习材料**
   - 点击"材料管理"或菜单"文件" -> "导入材料"
   - 选择文本文件或PDF文件导入

2. **生成试卷**
   - 点击"生成试卷"
   - 选择学习材料和AI模型
   - 配置题目类型和数量
   - 点击"生成试卷"

3. **参加考试**
   - 点击"开始考试"
   - 选择要参加的考试
   - 在规定时间内完成答题
   - 查看考试结果和详细解析

4. **复习错题**
   - 点击"错题本"查看错题
   - 点击"收藏题目"查看重点题目
   - 使用搜索功能查找特定题目

5. **查看知识图谱**
   - 点击"知识图谱"
   - 选择构建方式（从数据库或材料）
   - 调整显示选项和布局
   - 点击节点查看详细信息

## 注意事项

- 首次运行需要配置AI API密钥才能使用试卷生成功能
- PDF导入功能需要安装PyPDF2库
- 知识图谱功能需要matplotlib和networkx库
- 建议定期备份数据库文件

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本（建议3.7+）
   - 安装所需依赖：`pip install -r requirements.txt`

2. **AI生成失败**
   - 检查API密钥是否正确
   - 测试网络连接
   - 查看错误信息

3. **PDF导入失败**
   - 确保PDF文件未加密
   - 检查文件路径是否包含中文
   - 尝试其他PDF文件

4. **知识图谱显示异常**
   - 检查matplotlib中文字体配置
   - 确保数据库中有知识点数据

## 许可证

MIT License
