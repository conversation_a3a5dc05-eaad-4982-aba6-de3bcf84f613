# 🧠 AI考试生成系统

一个基于人工智能的智能考试生成系统，支持多种题型、自动出题、错题管理等功能。

## ✨ 主要功能

- **🎯 智能出题**：基于AI的多种题型自动生成
- **📊 考试管理**：完整的考试创建、监控和分析
- **📚 错题本**：智能错题收集和复习管理
- **🎨 现代界面**：传统和现代化界面可选

## 🚀 快速启动

### 环境要求
- Python 3.8+
- SQLite 3
- 网络连接（用于AI API）

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动系统

#### 传统界面（推荐）
```bash
python main_simple.py
```

#### 现代化界面
```bash
python run_modern_ui.py
```

## ⚙️ 配置

在 `config.ini` 中配置API信息：
```ini
[API]
api_key = your_api_key_here
base_url = https://ark.cn-beijing.volces.com/api/v3
model = ep-20240611152207-xxxxx
```

## 📁 项目结构

```
Kaoshi/
├── main_simple.py              # 传统界面启动器
├── run_modern_ui.py           # 现代界面启动器
├── src/                       # 源代码
├── data/                      # 数据库
├── api_manager_package/       # API管理工具
└── backups/                   # 自动备份
```

## 📖 文档

- [启动指南](STARTUP_GUIDE.md)
- [API配置](API_CONFIG_EXAMPLE.md)
- [项目结构](项目结构说明.md)

## 🆘 支持

遇到问题请查看文档或提交Issue。

---

**享受智能化的考试体验！** 🎓
