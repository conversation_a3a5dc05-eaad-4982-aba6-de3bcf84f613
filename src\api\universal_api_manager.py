#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用API管理器
支持多种API提供商和自动模型检测
"""

import requests
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class APIProvider:
    """API提供商配置"""
    name: str
    display_name: str
    base_url: str
    api_key: str
    api_type: str  # openai, claude, gemini, custom
    headers: Dict[str, str]
    models: List[str]
    test_model: str  # 用于测试的默认模型
    active: bool = True
    last_tested: Optional[str] = None
    status: str = "未测试"  # 未测试, 正常, 异常
    error_message: str = ""

class UniversalAPIManager:
    """通用API管理器"""
    
    def __init__(self):
        self.providers: Dict[str, APIProvider] = {}
        self.load_default_providers()
    
    def load_default_providers(self):
        """加载默认API提供商"""
        
        # OpenAI官方
        self.add_provider(APIProvider(
            name="openai_official",
            display_name="OpenAI官方",
            base_url="https://api.openai.com/v1",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=[],
            test_model="gpt-3.5-turbo"
        ))
        
        # 豆包API
        self.add_provider(APIProvider(
            name="doubao",
            display_name="豆包API",
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=["doubao-seed-1-6-flash-250615", "doubao-seed-1-6-250615", "doubao-pro-4k", "doubao-pro-32k"],
            test_model="doubao-seed-1-6-flash-250615"
        ))
        
        # DeepSeek
        self.add_provider(APIProvider(
            name="deepseek",
            display_name="DeepSeek",
            base_url="https://api.deepseek.com/v1",
            api_key="",
            api_type="openai",
            headers={"Content-Type": "application/json"},
            models=[],
            test_model="deepseek-chat"
        ))
        
        # Gemini
        self.add_provider(APIProvider(
            name="gemini",
            display_name="Google Gemini",
            base_url="https://generativelanguage.googleapis.com/v1beta",
            api_key="",
            api_type="gemini",
            headers={"Content-Type": "application/json"},
            models=[],
            test_model="gemini-pro"
        ))
    
    def add_provider(self, provider: APIProvider):
        """添加API提供商"""
        self.providers[provider.name] = provider
    
    def add_custom_provider(self, name: str, display_name: str, base_url: str, 
                           api_key: str, api_type: str = "openai", test_model: str = ""):
        """添加自定义API提供商"""
        
        headers = {"Content-Type": "application/json"}
        if api_key:
            if api_type == "openai":
                headers["Authorization"] = f"Bearer {api_key}"
            elif api_type == "gemini":
                # Gemini使用URL参数传递API key
                pass
        
        provider = APIProvider(
            name=name,
            display_name=display_name,
            base_url=base_url.rstrip('/'),
            api_key=api_key,
            api_type=api_type,
            headers=headers,
            models=[],
            test_model=test_model or "gpt-3.5-turbo"
        )
        
        self.add_provider(provider)
        return provider
    
    def update_provider(self, name: str, **kwargs):
        """更新API提供商配置"""
        if name in self.providers:
            provider = self.providers[name]
            for key, value in kwargs.items():
                if hasattr(provider, key):
                    setattr(provider, key, value)
            
            # 更新headers
            if 'api_key' in kwargs and kwargs['api_key']:
                if provider.api_type == "openai":
                    provider.headers["Authorization"] = f"Bearer {kwargs['api_key']}"
    
    def test_provider_connection(self, name: str) -> bool:
        """测试API提供商连接"""
        if name not in self.providers:
            return False
        
        provider = self.providers[name]
        
        try:
            if provider.api_type == "openai":
                return self._test_openai_connection(provider)
            elif provider.api_type == "gemini":
                return self._test_gemini_connection(provider)
            else:
                return self._test_custom_connection(provider)
                
        except Exception as e:
            provider.status = "异常"
            provider.error_message = str(e)
            provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return False
    
    def _test_openai_connection(self, provider: APIProvider) -> bool:
        """测试OpenAI兼容API连接"""
        
        headers = provider.headers.copy()
        if provider.api_key:
            headers["Authorization"] = f"Bearer {provider.api_key}"
        
        # 先尝试获取模型列表
        models_url = f"{provider.base_url}/models"
        
        try:
            response = requests.get(models_url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                models_data = response.json()
                if 'data' in models_data:
                    provider.models = [model['id'] for model in models_data['data']]
                    print(f"✓ {provider.display_name} 获取到 {len(provider.models)} 个模型")
                
        except Exception as e:
            print(f"获取模型列表失败: {e}")
        
        # 测试聊天接口
        chat_url = f"{provider.base_url}/chat/completions"
        
        test_data = {
            "model": provider.test_model,
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        try:
            response = requests.post(chat_url, headers=headers, json=test_data, timeout=30)
            
            if response.status_code == 200:
                provider.status = "正常"
                provider.error_message = ""
                provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"✓ {provider.display_name} 连接测试成功")
                return True
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                provider.status = "异常"
                provider.error_message = error_msg
                provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"✗ {provider.display_name} 连接测试失败: {error_msg}")
                return False
                
        except requests.exceptions.Timeout:
            provider.status = "异常"
            provider.error_message = "连接超时"
            provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"✗ {provider.display_name} 连接超时")
            return False
        except Exception as e:
            provider.status = "异常"
            provider.error_message = str(e)
            provider.last_tested = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"✗ {provider.display_name} 连接失败: {e}")
            return False
    
    def _test_gemini_connection(self, provider: APIProvider) -> bool:
        """测试Gemini API连接"""
        # Gemini API测试逻辑
        # 这里可以根据需要实现
        return True
    
    def _test_custom_connection(self, provider: APIProvider) -> bool:
        """测试自定义API连接"""
        # 自定义API测试逻辑
        return self._test_openai_connection(provider)
    
    def get_available_providers(self) -> List[APIProvider]:
        """获取可用的API提供商"""
        return [p for p in self.providers.values() if p.active]
    
    def get_provider_models(self, name: str) -> List[str]:
        """获取指定提供商的模型列表"""
        if name in self.providers:
            return self.providers[name].models
        return []
    
    def chat_completion(self, provider_name: str, model: str, messages: List[Dict], **kwargs) -> Dict:
        """通用聊天完成接口"""
        if provider_name not in self.providers:
            raise ValueError(f"未找到API提供商: {provider_name}")
        
        provider = self.providers[provider_name]
        
        if provider.api_type == "openai":
            return self._openai_chat_completion(provider, model, messages, **kwargs)
        elif provider.api_type == "gemini":
            return self._gemini_chat_completion(provider, model, messages, **kwargs)
        else:
            return self._custom_chat_completion(provider, model, messages, **kwargs)
    
    def _openai_chat_completion(self, provider: APIProvider, model: str, messages: List[Dict], **kwargs) -> Dict:
        """OpenAI兼容的聊天完成"""
        
        headers = provider.headers.copy()
        if provider.api_key:
            headers["Authorization"] = f"Bearer {provider.api_key}"
        
        data = {
            "model": model,
            "messages": messages,
            **kwargs
        }
        
        response = requests.post(
            f"{provider.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=120
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"API请求失败: {response.status_code} - {response.text}")
    
    def _gemini_chat_completion(self, provider: APIProvider, model: str, messages: List[Dict], **kwargs) -> Dict:
        """Gemini聊天完成"""
        # Gemini API实现
        pass
    
    def _custom_chat_completion(self, provider: APIProvider, model: str, messages: List[Dict], **kwargs) -> Dict:
        """自定义API聊天完成"""
        return self._openai_chat_completion(provider, model, messages, **kwargs)
    
    def save_config(self, config_path: str):
        """保存配置到文件"""
        config_data = {}
        for name, provider in self.providers.items():
            config_data[name] = {
                'display_name': provider.display_name,
                'base_url': provider.base_url,
                'api_key': provider.api_key,
                'api_type': provider.api_type,
                'test_model': provider.test_model,
                'active': provider.active,
                'models': provider.models
            }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    def load_config(self, config_path: str):
        """从文件加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            for name, config in config_data.items():
                if name in self.providers:
                    # 更新现有提供商
                    self.update_provider(name, **config)
                else:
                    # 添加新的提供商
                    self.add_custom_provider(
                        name=name,
                        display_name=config.get('display_name', name),
                        base_url=config.get('base_url', ''),
                        api_key=config.get('api_key', ''),
                        api_type=config.get('api_type', 'openai'),
                        test_model=config.get('test_model', 'gpt-3.5-turbo')
                    )
                    
                    # 设置其他属性
                    provider = self.providers[name]
                    provider.active = config.get('active', True)
                    provider.models = config.get('models', [])
                    
        except FileNotFoundError:
            print("配置文件不存在，使用默认配置")
        except Exception as e:
            print(f"加载配置失败: {e}")

    def remove_provider(self, name: str):
        """删除API提供商"""
        if name in self.providers:
            del self.providers[name]

    def test_all_providers(self):
        """测试所有活跃的API提供商"""
        results = {}
        for name, provider in self.providers.items():
            if provider.active and provider.api_key:
                print(f"正在测试 {provider.display_name}...")
                results[name] = self.test_provider_connection(name)
        return results
