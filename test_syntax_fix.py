#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试语法修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块导入"""
    try:
        print("=== 测试模块导入 ===")
        
        # 测试主要模块
        from src.ui.exam_generation_window import ExamGenerationWindow
        print("✓ ExamGenerationWindow 导入成功")
        
        from src.ui.main_window import MainWindow
        print("✓ MainWindow 导入成功")
        
        from src.api.universal_api_manager import UniversalAPIManager
        print("✓ UniversalAPIManager 导入成功")
        
        from src.api.enhanced_doubao_client import EnhancedDoubaoClient
        print("✓ EnhancedDoubaoClient 导入成功")
        
        print("\n🎉 所有模块导入成功！")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\n=== 测试基本功能 ===")
        
        # 测试API管理器
        from src.api.universal_api_manager import UniversalAPIManager
        api_manager = UniversalAPIManager()
        print(f"✓ API管理器创建成功，提供商数量: {len(api_manager.providers)}")
        
        # 测试豆包客户端
        from src.api.enhanced_doubao_client import EnhancedDoubaoClient
        client = EnhancedDoubaoClient(
            api_key="test_key",
            model="doubao-seed-1-6-flash-250615"
        )
        print("✓ 增强豆包客户端创建成功")
        
        models = client.get_available_models()
        print(f"✓ 获取模型列表成功，模型数量: {len(models)}")
        
        print("\n🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试语法修复...\n")
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试基本功能
        func_success = test_basic_functionality()
        
        if func_success:
            print("\n✅ 所有测试通过！")
            print("\n现在您可以：")
            print("1. 正常启动考试系统")
            print("2. 使用试卷生成功能")
            print("3. 看到保存试卷按钮")
            print("4. 使用高级API管理器")
            print("5. 切换豆包模型")
        else:
            print("\n⚠️ 导入成功但功能测试失败")
    else:
        print("\n❌ 导入测试失败，请检查语法错误")

if __name__ == "__main__":
    main()
