#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版错题本窗口
按试卷分类显示错题，提供更好的组织和分析功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
import json

class EnhancedWrongQuestionsWindow:
    def __init__(self, parent, wrong_question_manager):
        self.parent = parent
        self.wrong_question_manager = wrong_question_manager
        self.window = None
        self.current_view = "by_exam"  # by_exam, all, favorites
        
    def show(self):
        """显示增强版错题本窗口"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("错题本 - 按试卷分类")
        self.window.geometry("1000x700")
        self.window.transient(self.parent)
        
        self.create_interface()
        self.load_exam_summary()
        
    def create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题和工具栏
        self.create_header(main_frame)
        
        # 创建左右分栏
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 左侧：试卷列表
        self.create_exam_list_panel(paned_window)
        
        # 右侧：错题详情
        self.create_question_detail_panel(paned_window)
        
    def create_header(self, parent):
        """创建标题和工具栏"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 标题
        title_label = ttk.Label(header_frame, text="📚 错题本", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # 统计信息
        self.stats_label = ttk.Label(header_frame, text="", 
                                    font=("Microsoft YaHei", 10))
        self.stats_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 工具栏
        toolbar = ttk.Frame(header_frame)
        toolbar.pack(side=tk.RIGHT)
        
        # 视图切换按钮
        ttk.Button(toolbar, text="📋 按试卷", 
                  command=lambda: self.switch_view("by_exam")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="📝 全部错题", 
                  command=lambda: self.switch_view("all")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="⭐ 收藏题目", 
                  command=lambda: self.switch_view("favorites")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="🔄 刷新", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        
    def create_exam_list_panel(self, parent):
        """创建试卷列表面板"""
        # 左侧框架
        left_frame = ttk.Frame(parent)
        parent.add(left_frame, weight=1)
        
        # 试卷列表标题
        list_title = ttk.Label(left_frame, text="试卷列表", 
                              font=("Microsoft YaHei", 12, "bold"))
        list_title.pack(pady=(0, 10))
        
        # 试卷列表
        list_frame = ttk.Frame(left_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 滚动条
        list_scrollbar = ttk.Scrollbar(list_frame)
        list_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 列表框
        self.exam_listbox = tk.Listbox(list_frame, 
                                      yscrollcommand=list_scrollbar.set,
                                      font=("Microsoft YaHei", 10))
        self.exam_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        list_scrollbar.config(command=self.exam_listbox.yview)
        
        # 绑定选择事件
        self.exam_listbox.bind('<<ListboxSelect>>', self.on_exam_select)
        
        # 底部统计
        self.exam_stats_label = ttk.Label(left_frame, text="", 
                                         font=("Microsoft YaHei", 9))
        self.exam_stats_label.pack(pady=(10, 0))
        
    def create_question_detail_panel(self, parent):
        """创建错题详情面板"""
        # 右侧框架
        right_frame = ttk.Frame(parent)
        parent.add(right_frame, weight=2)
        
        # 详情标题
        detail_title_frame = ttk.Frame(right_frame)
        detail_title_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.detail_title_label = ttk.Label(detail_title_frame, text="选择试卷查看错题", 
                                           font=("Microsoft YaHei", 12, "bold"))
        self.detail_title_label.pack(side=tk.LEFT)
        
        # 操作按钮
        detail_toolbar = ttk.Frame(detail_title_frame)
        detail_toolbar.pack(side=tk.RIGHT)
        
        ttk.Button(detail_toolbar, text="📝 查看解析", 
                  command=self.view_question_explanation).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(detail_toolbar, text="⭐ 切换收藏", 
                  command=self.toggle_favorite).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(detail_toolbar, text="🗑️ 删除错题",
                  command=self.delete_question).pack(side=tk.LEFT, padx=(0, 5))

        # 批量操作按钮
        batch_frame = ttk.Frame(right_frame)
        batch_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(batch_frame, text="批量操作:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(batch_frame, text="☑️ 全选", command=self.select_all_questions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="❌ 取消选择", command=self.deselect_all_questions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="⭐ 批量收藏", command=self.batch_toggle_favorite).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_frame, text="🗑️ 批量删除", command=self.batch_delete_questions).pack(side=tk.LEFT, padx=(0, 5))

        # 错题列表
        columns = ('ID', '题目', '类型', '正确答案', '我的答案', '收藏', '时间')
        self.question_tree = ttk.Treeview(right_frame, columns=columns, show='headings', height=20)
        
        # 设置列
        for col in columns:
            self.question_tree.heading(col, text=col)
            if col == 'ID':
                self.question_tree.column(col, width=50)
            elif col in ['类型', '收藏']:
                self.question_tree.column(col, width=80)
            elif col == '题目':
                self.question_tree.column(col, width=300)
            elif col == '时间':
                self.question_tree.column(col, width=120)
        
        # 滚动条
        tree_scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=self.question_tree.yview)
        self.question_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        self.question_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.question_tree.bind('<Double-Button-1>', lambda e: self.view_question_explanation())
        
    def switch_view(self, view_type):
        """切换视图"""
        self.current_view = view_type
        
        if view_type == "by_exam":
            self.load_exam_summary()
        elif view_type == "all":
            self.load_all_questions()
        elif view_type == "favorites":
            self.load_favorite_questions()
            
    def load_exam_summary(self):
        """加载试卷摘要"""
        try:
            # 清空列表
            self.exam_listbox.delete(0, tk.END)
            self.clear_question_tree()
            
            # 获取试卷错题统计
            exam_summary = self.wrong_question_manager.get_exam_wrong_question_summary()
            
            total_questions = 0
            total_exams = len(exam_summary)
            
            for exam in exam_summary:
                exam_name = exam[0]
                question_count = exam[1]
                favorite_count = exam[2]
                latest_time = exam[3]
                
                total_questions += question_count
                
                # 格式化显示
                display_text = f"{exam_name} ({question_count}题"
                if favorite_count > 0:
                    display_text += f", {favorite_count}⭐"
                display_text += ")"
                
                self.exam_listbox.insert(tk.END, display_text)
            
            # 更新统计信息
            self.stats_label.config(text=f"共 {total_exams} 套试卷，{total_questions} 道错题")
            self.exam_stats_label.config(text=f"选择试卷查看详细错题")
            self.detail_title_label.config(text="选择试卷查看错题")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载试卷摘要失败: {str(e)}")
            
    def load_all_questions(self):
        """加载所有错题"""
        try:
            self.exam_listbox.delete(0, tk.END)
            self.exam_listbox.insert(tk.END, "全部错题")
            self.exam_listbox.selection_set(0)
            
            questions = self.wrong_question_manager.get_all_wrong_questions()
            self.populate_question_tree(questions)
            
            self.stats_label.config(text=f"共 {len(questions)} 道错题")
            self.detail_title_label.config(text="全部错题")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载所有错题失败: {str(e)}")
            
    def load_favorite_questions(self):
        """加载收藏的错题"""
        try:
            self.exam_listbox.delete(0, tk.END)
            self.exam_listbox.insert(tk.END, "收藏的错题")
            self.exam_listbox.selection_set(0)
            
            questions = self.wrong_question_manager.get_favorite_questions()
            self.populate_question_tree(questions)
            
            self.stats_label.config(text=f"共 {len(questions)} 道收藏错题")
            self.detail_title_label.config(text="收藏的错题")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载收藏错题失败: {str(e)}")
            
    def on_exam_select(self, event):
        """试卷选择事件"""
        if self.current_view != "by_exam":
            return
            
        selection = self.exam_listbox.curselection()
        if not selection:
            return
            
        try:
            # 获取选中的试卷
            selected_index = selection[0]
            exam_summary = self.wrong_question_manager.get_exam_wrong_question_summary()
            
            if selected_index < len(exam_summary):
                exam_info = exam_summary[selected_index]
                exam_name = exam_info[0]
                
                # 根据试卷名称获取错题
                if exam_name == "未分类错题":
                    # 获取没有试卷信息的错题
                    questions = self.get_uncategorized_questions()
                else:
                    # 根据试卷标题获取错题
                    questions = self.get_questions_by_exam_title(exam_name)
                
                self.populate_question_tree(questions)
                self.detail_title_label.config(text=f"{exam_name} - 错题详情")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载试卷错题失败: {str(e)}")
            
    def get_uncategorized_questions(self):
        """获取未分类的错题"""
        query = """
            SELECT id, question_text, question_type, correct_answer, user_answer, 
                   explanation, is_favorite, created_at, exam_id, exam_title, exam_record_id
            FROM wrong_questions 
            WHERE exam_id IS NULL OR exam_title IS NULL
            ORDER BY created_at DESC
        """
        return self.wrong_question_manager.db.execute_query(query)
        
    def get_questions_by_exam_title(self, exam_title):
        """根据试卷标题获取错题"""
        query = """
            SELECT id, question_text, question_type, correct_answer, user_answer, 
                   explanation, is_favorite, created_at, exam_id, exam_title, exam_record_id
            FROM wrong_questions 
            WHERE exam_title = ?
            ORDER BY created_at DESC
        """
        return self.wrong_question_manager.db.execute_query(query, (exam_title,))
        
    def populate_question_tree(self, questions):
        """填充错题列表"""
        self.clear_question_tree()
        
        for question in questions:
            question_id = question[0]
            question_text = question[1]
            question_type = question[2]
            correct_answer = question[3]
            user_answer = question[4]
            is_favorite = question[6]
            created_at = question[7]
            
            # 截断长题目
            display_question = question_text[:50] + "..." if len(question_text) > 50 else question_text
            favorite_text = "⭐" if is_favorite else ""
            
            # 格式化时间
            try:
                if isinstance(created_at, str):
                    time_str = created_at[:16]  # 只显示到分钟
                else:
                    time_str = str(created_at)[:16]
            except:
                time_str = "未知"
            
            self.question_tree.insert('', tk.END, values=(
                question_id, display_question, question_type, 
                correct_answer, user_answer, favorite_text, time_str
            ))
            
    def clear_question_tree(self):
        """清空错题列表"""
        for item in self.question_tree.get_children():
            self.question_tree.delete(item)
            
    def view_question_explanation(self):
        """查看错题解析"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题")
            return
            
        try:
            item = self.question_tree.item(selection[0])
            question_id = item['values'][0]
            
            # 获取错题详情
            question_data = self.wrong_question_manager.get_wrong_question_by_id(question_id)
            if question_data:
                self.show_question_explanation_window(question_data)
            else:
                messagebox.showerror("错误", "无法找到错题数据")
                
        except Exception as e:
            messagebox.showerror("错误", f"查看解析失败: {str(e)}")
            
    def show_question_explanation_window(self, question_data):
        """显示错题解析窗口"""
        explanation_window = tk.Toplevel(self.window)
        explanation_window.title("错题解析")
        explanation_window.geometry("600x500")
        explanation_window.transient(self.window)
        
        main_frame = ttk.Frame(explanation_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题信息
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(title_frame, text="错题解析", 
                 font=("Microsoft YaHei", 16, "bold")).pack()
        
        # 试卷信息
        if len(question_data) > 9 and question_data[9]:  # exam_title
            exam_info = f"来源试卷: {question_data[9]}"
            ttk.Label(title_frame, text=exam_info, 
                     font=("Microsoft YaHei", 10), foreground="gray").pack()
        
        # 题目内容
        question_frame = ttk.LabelFrame(main_frame, text="题目", padding=10)
        question_frame.pack(fill=tk.X, pady=(0, 10))
        
        question_text = scrolledtext.ScrolledText(question_frame, wrap=tk.WORD,
                                                 font=("Microsoft YaHei", 11), height=4)
        question_text.pack(fill=tk.X)
        question_text.insert(tk.END, question_data[1])  # question_text
        question_text.config(state=tk.DISABLED)
        
        # 答案对比
        answer_frame = ttk.LabelFrame(main_frame, text="答案对比", padding=10)
        answer_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 我的答案
        my_answer_frame = ttk.Frame(answer_frame)
        my_answer_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(my_answer_frame, text="我的答案：", 
                 font=("Microsoft YaHei", 10, "bold")).pack(side=tk.LEFT)
        ttk.Label(my_answer_frame, text=f"✗ {question_data[4]}", 
                 foreground="red", font=("Microsoft YaHei", 10)).pack(side=tk.LEFT, padx=(5, 0))
        
        # 正确答案
        correct_answer_frame = ttk.Frame(answer_frame)
        correct_answer_frame.pack(fill=tk.X)
        
        ttk.Label(correct_answer_frame, text="正确答案：", 
                 font=("Microsoft YaHei", 10, "bold")).pack(side=tk.LEFT)
        ttk.Label(correct_answer_frame, text=f"✓ {question_data[3]}", 
                 foreground="green", font=("Microsoft YaHei", 10)).pack(side=tk.LEFT, padx=(5, 0))
        
        # 解析内容
        explanation_frame = ttk.LabelFrame(main_frame, text="详细解析", padding=10)
        explanation_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        explanation_text = scrolledtext.ScrolledText(explanation_frame, wrap=tk.WORD,
                                                   font=("Microsoft YaHei", 10), height=8)
        explanation_text.pack(fill=tk.BOTH, expand=True)
        
        explanation_content = question_data[5] if question_data[5] else "暂无解析"
        explanation_text.insert(tk.END, explanation_content)
        explanation_text.config(state=tk.DISABLED)
        
        # 关闭按钮
        ttk.Button(main_frame, text="关闭", 
                  command=explanation_window.destroy).pack(pady=(10, 0))
        
    def toggle_favorite(self):
        """切换收藏状态"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题")
            return
            
        try:
            item = self.question_tree.item(selection[0])
            question_id = item['values'][0]
            
            self.wrong_question_manager.toggle_favorite(question_id)
            self.refresh_current_view()
            messagebox.showinfo("成功", "收藏状态已更新")
            
        except Exception as e:
            messagebox.showerror("错误", f"更新收藏状态失败: {str(e)}")
            
    def delete_question(self):
        """删除错题"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一道错题")
            return
            
        if messagebox.askyesno("确认", "确定要删除这道错题吗？"):
            try:
                item = self.question_tree.item(selection[0])
                question_id = item['values'][0]
                
                self.wrong_question_manager.delete_wrong_question(question_id)
                self.refresh_current_view()
                messagebox.showinfo("成功", "错题已删除")
                
            except Exception as e:
                messagebox.showerror("错误", f"删除错题失败: {str(e)}")

    # 批量操作方法
    def select_all_questions(self):
        """全选所有错题"""
        try:
            for item in self.question_tree.get_children():
                self.question_tree.selection_add(item)

            selected_count = len(self.question_tree.selection())
            messagebox.showinfo("提示", f"已选择 {selected_count} 道错题")

        except Exception as e:
            messagebox.showerror("错误", f"全选失败: {str(e)}")

    def deselect_all_questions(self):
        """取消选择所有错题"""
        try:
            self.question_tree.selection_remove(self.question_tree.selection())
            messagebox.showinfo("提示", "已取消所有选择")

        except Exception as e:
            messagebox.showerror("错误", f"取消选择失败: {str(e)}")

    def batch_toggle_favorite(self):
        """批量切换收藏状态"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要操作的错题")
            return

        if not messagebox.askyesno("确认操作", f"确定要切换 {len(selection)} 道错题的收藏状态吗？"):
            return

        try:
            success_count = 0
            for item in selection:
                question_id = self.question_tree.item(item)['values'][0]
                self.wrong_question_manager.toggle_favorite(question_id)
                success_count += 1

            self.refresh_current_view()
            messagebox.showinfo("成功", f"已切换 {success_count} 道错题的收藏状态")

        except Exception as e:
            messagebox.showerror("错误", f"批量收藏操作失败: {str(e)}")

    def batch_delete_questions(self):
        """批量删除错题"""
        selection = self.question_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的错题")
            return

        if not messagebox.askyesno("确认删除",
                                  f"确定要删除 {len(selection)} 道错题吗？\n此操作不可撤销！"):
            return

        try:
            success_count = 0
            for item in selection:
                question_id = self.question_tree.item(item)['values'][0]
                self.wrong_question_manager.delete_wrong_question(question_id)
                success_count += 1

            self.refresh_current_view()
            messagebox.showinfo("成功", f"已删除 {success_count} 道错题")

        except Exception as e:
            messagebox.showerror("错误", f"批量删除失败: {str(e)}")

    def refresh_current_view(self):
        """刷新当前视图"""
        if self.current_view == "by_exam":
            self.load_exam_summary()
        elif self.current_view == "all":
            self.load_all_questions()
        elif self.current_view == "favorites":
            self.load_favorite_questions()
                
    def refresh_data(self):
        """刷新数据"""
        self.switch_view(self.current_view)
        
    def refresh_current_view(self):
        """刷新当前视图"""
        if self.current_view == "by_exam":
            # 保持当前选择的试卷
            selection = self.exam_listbox.curselection()
            self.load_exam_summary()
            if selection:
                self.exam_listbox.selection_set(selection[0])
                self.on_exam_select(None)
        else:
            self.switch_view(self.current_view)
