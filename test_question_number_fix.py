#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试题号修复功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.database_manager import DatabaseManager
from src.core.wrong_question_manager import WrongQuestionManager
from src.core.exam_manager import ExamManager

def test_question_number_fix():
    """测试题号修复功能"""
    print("🧪 开始测试题号修复功能...")
    
    try:
        # 初始化管理器
        db_manager = DatabaseManager()
        wrong_question_manager = WrongQuestionManager(db_manager)
        exam_manager = ExamManager(db_manager)
        
        print("✅ 管理器初始化成功")
        
        # 创建测试试卷
        test_questions = [
            {
                'type': 'single_choice',
                'question': '测试题目1：Python中哪个关键字用于定义函数？',
                'options': ['function', 'def', 'func', 'define'],
                'correct_answer': 'B',
                'explanation': 'Python中使用def关键字来定义函数。',
                'score': 1
            },
            {
                'type': 'single_choice',
                'question': '测试题目2：Python是什么类型的编程语言？',
                'options': ['编译型语言', '解释型语言', '汇编语言', '机器语言'],
                'correct_answer': 'B',
                'explanation': 'Python是一种解释型编程语言。',
                'score': 1
            },
            {
                'type': 'true_false',
                'question': '测试题目3：Python是开源的编程语言。',
                'correct_answer': '对',
                'explanation': 'Python确实是开源的编程语言。',
                'score': 1
            },
            {
                'type': 'single_choice',
                'question': '测试题目4：Python的文件扩展名是什么？',
                'options': ['.py', '.python', '.pyt', '.pt'],
                'correct_answer': 'A',
                'explanation': 'Python文件的标准扩展名是.py。',
                'score': 1
            },
            {
                'type': 'single_choice',
                'question': '测试题目5：Python中用于输出的函数是什么？',
                'options': ['print()', 'output()', 'display()', 'show()'],
                'correct_answer': 'A',
                'explanation': 'Python中使用print()函数进行输出。',
                'score': 1
            }
        ]
        
        # 创建测试试卷
        exam_id = exam_manager.create_exam(
            title="题号测试试卷",
            description="用于测试题号显示功能的试卷",
            questions=test_questions,
            time_limit=30
        )
        
        print(f"✅ 创建测试试卷成功，ID: {exam_id}")
        
        # 模拟考试记录（故意答错第2题和第4题）
        test_answers = {
            '0': 'B',  # 第1题：正确
            '1': 'A',  # 第2题：错误（正确答案是B）
            '2': '对', # 第3题：正确
            '3': 'B',  # 第4题：错误（正确答案是A）
            '4': 'A'   # 第5题：正确
        }
        
        # 保存考试记录
        from datetime import datetime
        record_id = exam_manager.save_exam_record(
            exam_id=exam_id,
            answers=test_answers,
            score=3.0,
            total_score=5.0,
            start_time=datetime.now(),
            end_time=datetime.now()
        )
        
        print(f"✅ 保存考试记录成功，ID: {record_id}")
        
        # 获取考试记录并添加错题
        exam_record = exam_manager.get_exam_record_by_id(record_id)
        if exam_record:
            print("✅ 获取考试记录成功")
            
            # 批量添加错题
            added_count = wrong_question_manager.batch_add_wrong_questions_from_exam(exam_record)
            print(f"✅ 添加了 {added_count} 道错题")
            
            # 验证题号是否正确
            print("\n🔍 验证题号显示...")
            
            # 获取刚添加的错题
            wrong_questions = wrong_question_manager.get_wrong_questions_by_exam(exam_id)
            
            print(f"📊 错题验证结果:")
            for wq in wrong_questions:
                question_text = wq[1][:30] + "..." if len(wq[1]) > 30 else wq[1]
                question_number = wq[13] if len(wq) > 13 else "未知"  # question_number字段
                print(f"  • {question_text} → 题号: {question_number}")
                
            # 预期结果：应该显示"第2题"和"第4题"
            expected_numbers = [2, 4]
            actual_numbers = []
            
            for wq in wrong_questions:
                if len(wq) > 13 and wq[13] is not None:
                    actual_numbers.append(wq[13])
                    
            print(f"\n📈 测试结果:")
            print(f"  预期题号: {expected_numbers}")
            print(f"  实际题号: {actual_numbers}")
            
            if sorted(actual_numbers) == sorted(expected_numbers):
                print("✅ 题号显示正确！")
            else:
                print("❌ 题号显示不正确！")
                
        else:
            print("❌ 无法获取考试记录")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_question_number_fix()
