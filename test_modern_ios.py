#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试现代iOS风格考试界面
真正的2024年现代设计，告别90年代风格！
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_ios():
    """测试现代iOS界面"""
    try:
        print("🚀 测试现代iOS风格考试界面...")
        print("💎 2024年最新设计，深色主题，毛玻璃效果")
        
        # 导入必要的模块
        from src.core.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.core.wrong_question_manager import WrongQuestionManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        exam_manager = ExamManager(db_manager)
        wrong_question_manager = WrongQuestionManager(db_manager)
        
        # 创建现代化测试数据
        test_questions = [
            {
                'type': 'single_choice',
                'question': '这个现代iOS风格界面相比传统界面有什么优势？',
                'options': [
                    '深色主题更护眼，现代感十足',
                    '全屏沉浸式体验更专业',
                    '毛玻璃效果和渐变色彩更美观',
                    '以上都是'
                ],
                'correct_answer': 'D',
                'explanation': '现代iOS界面在各个方面都有显著提升！',
                'score': 2
            },
            {
                'type': 'multiple_choice',
                'question': '现代iOS设计语言包含哪些元素？（多选）',
                'options': [
                    '深色主题适配',
                    '毛玻璃背景效果',
                    '动态模糊和渐变',
                    '现代化字体排版',
                    '流畅的动画过渡'
                ],
                'correct_answer': 'ABCDE',
                'explanation': '这些都是现代iOS设计的核心元素',
                'score': 3
            },
            {
                'type': 'true_false',
                'question': '现代iOS界面采用了2024年最新的设计趋势。',
                'correct_answer': '对',
                'explanation': '确实采用了最新的设计理念和技术',
                'score': 1
            },
            {
                'type': 'short_answer',
                'question': '请描述您对这个现代iOS风格界面的第一印象。',
                'correct_answer': '现代、专业、美观、流畅',
                'explanation': '希望您喜欢这个全新的现代化界面！',
                'score': 4
            },
            {
                'type': 'single_choice',
                'question': '相比90年代风格的传统界面，现代iOS界面最大的改进是什么？',
                'options': [
                    '视觉设计更现代化',
                    '用户体验更流畅',
                    '功能更加丰富',
                    '整体感受更专业'
                ],
                'correct_answer': 'A',
                'explanation': '视觉设计的现代化是最显著的改进',
                'score': 2
            }
        ]
        
        # 创建测试考试
        try:
            exam_id = exam_manager.create_exam(
                title="🚀 现代iOS界面体验测试",
                description="体验2024年最新的现代iOS风格考试界面",
                questions=test_questions,
                time_limit=20  # 20分钟
            )
            print(f"✅ 创建现代化测试考试成功，ID: {exam_id}")
            
            # 获取考试数据
            exam_data = exam_manager.get_exam_by_id(exam_id)
            if not exam_data:
                print("❌ 无法获取考试数据")
                return
                
            print(f"📊 考试数据: {exam_data['title']}, {len(exam_data['questions'])}题")
            
            # 创建主窗口（隐藏）
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            
            # 显示启动提示
            messagebox.showinfo("🚀 现代iOS界面", 
                               "即将启动现代iOS风格考试界面！\n\n"
                               "✨ 特色功能：\n"
                               "• 深色主题，护眼舒适\n"
                               "• 毛玻璃效果，现代美观\n"
                               "• 全屏沉浸式体验\n"
                               "• 流畅动画效果\n"
                               "• 2024年最新设计\n\n"
                               "告别90年代风格，拥抱现代化设计！")
            
            # 启动现代iOS风格考试界面
            print("🚀 启动现代iOS风格考试界面...")
            from src.ui.modern_ios_exam_window import ModerniOSExamWindow
            
            modern_window = ModerniOSExamWindow(root, exam_manager, wrong_question_manager, exam_data)
            
            print("✅ 现代iOS界面创建成功！")
            print("💎 享受2024年最新的现代化考试体验！")
            
            # 运行界面
            root.mainloop()
            
        except Exception as e:
            print(f"❌ 创建考试失败: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误对话框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", f"现代iOS界面测试失败：\n{str(e)}\n\n"
                                   "可能需要检查依赖或系统兼容性。")
        root.destroy()

if __name__ == "__main__":
    print("🚀 现代iOS风格考试界面测试")
    print("💎 2024年最新设计，告别传统90年代风格")
    print("=" * 50)
    test_modern_ios()
