-- 材料导入系统数据库结构
-- 适用于SQLite数据库

-- 学习材料表
CREATE TABLE IF NOT EXISTS materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,                    -- 材料标题
    content TEXT NOT NULL,                  -- 材料内容
    file_type TEXT NOT NULL,                -- 文件类型 (text, pdf, manual)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- 更新时间
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_materials_title ON materials(title);
CREATE INDEX IF NOT EXISTS idx_materials_file_type ON materials(file_type);
CREATE INDEX IF NOT EXISTS idx_materials_created_at ON materials(created_at);

-- 创建全文搜索索引（如果数据库支持）
-- CREATE VIRTUAL TABLE IF NOT EXISTS materials_fts USING fts5(title, content, content='materials', content_rowid='id');

-- 插入示例数据（可选）
INSERT OR IGNORE INTO materials (title, content, file_type) VALUES 
('示例文本材料', '这是一个示例文本材料的内容。可以包含学习资料、教程、笔记等。', 'text'),
('示例PDF材料', '这是从PDF文件中提取的文本内容。通常包含更复杂的文档结构。', 'pdf'),
('手动输入材料', '这是用户手动输入的学习材料内容。', 'manual');
