#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试考试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_exam():
    """创建测试考试"""
    try:
        from src.utils.database_manager import DatabaseManager
        from src.core.exam_manager import ExamManager
        from src.core.material_manager import MaterialManager
        from src.utils.config_manager import ConfigManager
        
        print("=== 创建测试考试 ===")
        
        # 初始化
        config = ConfigManager()
        db = DatabaseManager(config)
        exam_manager = ExamManager(db)
        material_manager = MaterialManager(db)
        
        # 先创建一些测试材料
        print("1. 创建测试材料...")
        try:
            material_id = material_manager.save_material(
                "Python编程基础",
                """Python是一种高级编程语言，具有以下特点：
1. 简洁易读的语法
2. 跨平台兼容性
3. 丰富的标准库和第三方库
4. 支持面向对象编程
5. 动态类型系统

Python广泛应用于：
- Web开发（Django、Flask）
- 数据科学（NumPy、Pandas）
- 人工智能（TensorFlow、PyTorch）
- 自动化脚本
- 科学计算

Python的基本语法包括变量定义、条件语句、循环语句、函数定义等。""",
                "text"
            )
            print(f"✓ 测试材料创建成功，ID: {material_id}")
        except Exception as e:
            print(f"创建材料失败: {e}")
        
        # 创建测试考试
        print("2. 创建测试考试...")
        
        test_questions = [
            {
                "type": "single_choice",
                "question": "Python是什么类型的编程语言？",
                "options": ["编译型语言", "解释型语言", "汇编语言", "机器语言"],
                "correct_answer": "B",
                "explanation": "Python是解释型语言，代码在运行时被解释器逐行执行。",
                "score": 2
            },
            {
                "type": "multiple_choice",
                "question": "Python的主要特点包括哪些？",
                "options": ["简洁易读", "跨平台", "丰富的库", "静态类型"],
                "correct_answer": ["A", "B", "C"],
                "explanation": "Python具有简洁易读、跨平台、丰富的库等特点，但它是动态类型而非静态类型。",
                "score": 3
            },
            {
                "type": "true_false",
                "question": "Python支持面向对象编程。",
                "correct_answer": "对",
                "explanation": "Python完全支持面向对象编程，包括类、继承、多态等特性。",
                "score": 1
            },
            {
                "type": "short_answer",
                "question": "请列举Python的三个主要应用领域。",
                "correct_answer": "Web开发、数据科学、人工智能",
                "explanation": "Python在Web开发、数据科学、人工智能等领域都有广泛应用。",
                "score": 3
            },
            {
                "type": "case_analysis",
                "question": "假设你要开发一个数据分析项目，请分析为什么选择Python，并说明会用到哪些库。",
                "correct_answer": "选择Python因为其在数据科学领域有丰富的库支持，如NumPy用于数值计算，Pandas用于数据处理，Matplotlib用于数据可视化，scikit-learn用于机器学习等。Python语法简洁，学习成本低，社区活跃，文档完善。",
                "explanation": "这是一个综合性问题，需要从语言特性、库生态、社区支持等多个角度分析。",
                "score": 5
            }
        ]
        
        try:
            exam_id = exam_manager.create_exam(
                title="Python编程基础测试",
                description="测试Python基础知识的综合考试，包含单选、多选、判断、简答和案例分析题",
                questions=test_questions,
                time_limit=60
            )
            
            print(f"✓ 测试考试创建成功，ID: {exam_id}")
            
            # 验证考试
            saved_exam = exam_manager.get_exam_by_id(exam_id)
            if saved_exam:
                print(f"✓ 考试验证成功")
                print(f"  标题: {saved_exam['title']}")
                print(f"  题目数量: {len(saved_exam['questions'])}")
                print(f"  时间限制: {saved_exam['time_limit']}分钟")
            
            # 检查考试列表
            all_exams = exam_manager.get_all_exams()
            print(f"✓ 数据库中共有 {len(all_exams)} 个考试")
            
            return True
            
        except Exception as e:
            print(f"✗ 创建考试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"✗ 初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_test_exam()
    
    if success:
        print("\n🎉 测试考试创建成功！")
        print("现在可以在考试系统中看到测试考试了。")
        print("请在考试系统中点击'开始考试'查看。")
    else:
        print("\n❌ 测试考试创建失败。")
