#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通用API管理系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_universal_api_manager():
    """测试通用API管理器"""
    try:
        from src.api.universal_api_manager import UniversalAPIManager
        
        print("=== 通用API管理器测试 ===")
        
        # 创建管理器
        api_manager = UniversalAPIManager()
        print("✓ API管理器创建成功")
        
        # 显示默认提供商
        print(f"\n默认提供商数量: {len(api_manager.providers)}")
        for name, provider in api_manager.providers.items():
            print(f"  - {provider.display_name} ({name})")
        
        # 添加自定义提供商
        print("\n添加自定义提供商...")
        api_manager.add_custom_provider(
            name="test_provider",
            display_name="测试提供商",
            base_url="https://api.example.com/v1",
            api_key="test_key",
            api_type="openai",
            test_model="gpt-3.5-turbo"
        )
        print("✓ 自定义提供商添加成功")
        
        # 更新豆包配置
        print("\n更新豆包配置...")
        api_manager.update_provider(
            "doubao",
            api_key="2f62a42c-6ea7-4c2e-8b51-9d2e93f6e9c6",
            test_model="doubao-seed-1-6-flash-250615"
        )
        print("✓ 豆包配置更新成功")
        
        # 保存配置
        print("\n保存配置...")
        if not os.path.exists("config"):
            os.makedirs("config")
        
        api_manager.save_config("config/api_providers.json")
        print("✓ 配置保存成功")
        
        # 测试豆包连接
        print("\n测试豆包连接...")
        success = api_manager.test_provider_connection("doubao")
        if success:
            print("✓ 豆包连接测试成功")
            provider = api_manager.providers["doubao"]
            print(f"  状态: {provider.status}")
            print(f"  模型数量: {len(provider.models)}")
            if provider.models:
                print(f"  可用模型: {provider.models[:3]}...")
        else:
            print("✗ 豆包连接测试失败")
            provider = api_manager.providers["doubao"]
            print(f"  错误: {provider.error_message}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_universal_client():
    """测试通用AI客户端"""
    try:
        from src.api.universal_ai_client import UniversalAIClient, get_available_providers
        
        print("\n=== 通用AI客户端测试 ===")
        
        # 获取可用提供商
        providers = get_available_providers()
        print(f"可用提供商: {providers}")
        
        if not providers:
            print("⚠ 没有可用的提供商，请先配置API")
            return True
        
        # 使用第一个可用提供商
        provider_name = providers[0]
        print(f"使用提供商: {provider_name}")
        
        # 创建客户端
        if provider_name == "doubao":
            client = UniversalAIClient("doubao", "doubao-seed-1-6-flash-250615")
        else:
            client = UniversalAIClient(provider_name, "gpt-3.5-turbo")
        
        print("✓ 通用客户端创建成功")
        
        # 测试连接
        print("测试连接...")
        if client.test_connection():
            print("✓ 连接测试成功")
        else:
            print("✗ 连接测试失败")
            return False
        
        # 测试题目生成
        print("测试题目生成...")
        material = "Python是一种高级编程语言，具有简洁的语法和强大的功能。"
        
        try:
            questions = client.generate_questions(
                material=material,
                question_types=['single_choice', 'true_false'],
                num_questions=2
            )
            
            if questions:
                print(f"✓ 成功生成 {len(questions)} 道题目")
                for i, q in enumerate(questions, 1):
                    print(f"  题目{i}: {q.get('question', 'N/A')[:50]}...")
            else:
                print("✗ 题目生成失败")
                
        except Exception as e:
            print(f"✗ 题目生成出错: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 客户端测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试通用API管理系统...\n")
    
    # 测试API管理器
    manager_success = test_universal_api_manager()
    
    if manager_success:
        # 测试AI客户端
        client_success = test_universal_client()
        
        if client_success:
            print("\n🎉 所有测试通过！")
            print("\n现在您可以：")
            print("1. 启动考试系统")
            print("2. 在菜单中选择'设置' -> '高级API管理'")
            print("3. 配置和管理您的API提供商")
            print("4. 自动检测可用模型")
            print("5. 一键切换不同的API服务")
        else:
            print("\n⚠ API管理器测试通过，但客户端测试失败")
    else:
        print("\n❌ API管理器测试失败")

if __name__ == "__main__":
    main()
