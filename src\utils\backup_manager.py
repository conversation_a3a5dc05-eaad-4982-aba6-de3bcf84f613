#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据备份管理器
提供自动备份、手动备份和数据恢复功能
"""

import os
import shutil
import sqlite3
import json
import zipfile
import threading
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional

class BackupManager:
    def __init__(self, db_path: str, backup_dir: str = "backups"):
        """初始化备份管理器"""
        self.db_path = db_path
        self.backup_dir = backup_dir
        self.auto_backup_enabled = True
        self.backup_interval = 24  # 小时
        self.max_backups = 30  # 保留最多30个备份
        
        # 创建备份目录
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # 启动自动备份线程
        self.start_auto_backup()
        
    def create_backup(self, backup_name: str = None) -> str:
        """创建数据备份"""
        try:
            # 生成备份文件名
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
                
            backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
            
            # 创建备份ZIP文件
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 备份数据库文件
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, "database.db")
                    
                # 备份配置文件
                config_files = ["config.ini", "user_settings.json"]
                for config_file in config_files:
                    if os.path.exists(config_file):
                        zipf.write(config_file, config_file)
                        
                # 备份用户数据目录
                data_dirs = ["data", "materials", "exports"]
                for data_dir in data_dirs:
                    if os.path.exists(data_dir):
                        for root, dirs, files in os.walk(data_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path)
                                zipf.write(file_path, arc_path)
                                
                # 添加备份元数据
                metadata = {
                    "backup_time": datetime.now().isoformat(),
                    "backup_type": "manual",
                    "database_size": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                    "version": "2.0"
                }
                zipf.writestr("backup_metadata.json", json.dumps(metadata, indent=2))
                
            print(f"✅ 备份创建成功: {backup_path}")
            
            # 清理旧备份
            self.cleanup_old_backups()
            
            return backup_path
            
        except Exception as e:
            print(f"❌ 创建备份失败: {e}")
            raise Exception(f"创建备份失败: {e}")
            
    def restore_backup(self, backup_path: str) -> bool:
        """恢复数据备份"""
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")
                
            # 创建恢复前的安全备份
            safety_backup = self.create_backup("before_restore")
            print(f"📦 已创建安全备份: {safety_backup}")
            
            # 解压备份文件
            temp_dir = "temp_restore"
            os.makedirs(temp_dir, exist_ok=True)
            
            try:
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                    
                # 验证备份完整性
                if not self.verify_backup(temp_dir):
                    raise Exception("备份文件损坏或不完整")
                    
                # 恢复数据库
                db_backup_path = os.path.join(temp_dir, "database.db")
                if os.path.exists(db_backup_path):
                    # 关闭数据库连接（如果有的话）
                    self.close_database_connections()
                    
                    # 备份当前数据库
                    if os.path.exists(self.db_path):
                        shutil.copy2(self.db_path, f"{self.db_path}.backup")
                        
                    # 恢复数据库
                    shutil.copy2(db_backup_path, self.db_path)
                    print("✅ 数据库恢复成功")
                    
                # 恢复配置文件
                config_files = ["config.ini", "user_settings.json"]
                for config_file in config_files:
                    config_backup_path = os.path.join(temp_dir, config_file)
                    if os.path.exists(config_backup_path):
                        shutil.copy2(config_backup_path, config_file)
                        print(f"✅ 配置文件恢复成功: {config_file}")
                        
                # 恢复数据目录
                data_dirs = ["data", "materials", "exports"]
                for data_dir in data_dirs:
                    data_backup_path = os.path.join(temp_dir, data_dir)
                    if os.path.exists(data_backup_path):
                        if os.path.exists(data_dir):
                            shutil.rmtree(data_dir)
                        shutil.copytree(data_backup_path, data_dir)
                        print(f"✅ 数据目录恢复成功: {data_dir}")
                        
                print("🎉 数据恢复完成！")
                return True
                
            finally:
                # 清理临时目录
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    
        except Exception as e:
            print(f"❌ 恢复备份失败: {e}")
            return False
            
    def verify_backup(self, backup_dir: str) -> bool:
        """验证备份完整性"""
        try:
            # 检查必要文件
            required_files = ["database.db", "backup_metadata.json"]
            for file in required_files:
                if not os.path.exists(os.path.join(backup_dir, file)):
                    print(f"❌ 缺少必要文件: {file}")
                    return False
                    
            # 验证数据库文件
            db_path = os.path.join(backup_dir, "database.db")
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                conn.close()
                
                if len(tables) == 0:
                    print("❌ 数据库文件为空")
                    return False
                    
            except sqlite3.Error as e:
                print(f"❌ 数据库文件损坏: {e}")
                return False
                
            # 验证元数据
            metadata_path = os.path.join(backup_dir, "backup_metadata.json")
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    
                required_keys = ["backup_time", "backup_type", "version"]
                for key in required_keys:
                    if key not in metadata:
                        print(f"❌ 元数据缺少字段: {key}")
                        return False
                        
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"❌ 元数据文件错误: {e}")
                return False
                
            print("✅ 备份验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 备份验证失败: {e}")
            return False
            
    def get_backup_list(self) -> List[Dict]:
        """获取备份列表"""
        backups = []
        
        try:
            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.zip'):
                    backup_path = os.path.join(self.backup_dir, filename)
                    
                    # 获取文件信息
                    stat = os.stat(backup_path)
                    size = stat.st_size
                    mtime = datetime.fromtimestamp(stat.st_mtime)
                    
                    # 尝试读取元数据
                    metadata = {}
                    try:
                        with zipfile.ZipFile(backup_path, 'r') as zipf:
                            if "backup_metadata.json" in zipf.namelist():
                                metadata_content = zipf.read("backup_metadata.json")
                                metadata = json.loads(metadata_content.decode('utf-8'))
                    except:
                        pass
                        
                    backup_info = {
                        "filename": filename,
                        "path": backup_path,
                        "size": size,
                        "size_mb": round(size / (1024 * 1024), 2),
                        "created_time": mtime,
                        "backup_type": metadata.get("backup_type", "unknown"),
                        "version": metadata.get("version", "unknown")
                    }
                    
                    backups.append(backup_info)
                    
            # 按创建时间排序
            backups.sort(key=lambda x: x["created_time"], reverse=True)
            
        except Exception as e:
            print(f"❌ 获取备份列表失败: {e}")
            
        return backups
        
    def delete_backup(self, backup_path: str) -> bool:
        """删除备份文件"""
        try:
            if os.path.exists(backup_path):
                os.remove(backup_path)
                print(f"✅ 备份删除成功: {backup_path}")
                return True
            else:
                print(f"❌ 备份文件不存在: {backup_path}")
                return False
        except Exception as e:
            print(f"❌ 删除备份失败: {e}")
            return False
            
    def cleanup_old_backups(self):
        """清理旧备份"""
        try:
            backups = self.get_backup_list()
            
            # 如果备份数量超过限制，删除最旧的
            if len(backups) > self.max_backups:
                excess_backups = backups[self.max_backups:]
                for backup in excess_backups:
                    self.delete_backup(backup["path"])
                    
            # 删除超过保留期限的备份
            cutoff_date = datetime.now() - timedelta(days=90)  # 保留90天
            for backup in backups:
                if backup["created_time"] < cutoff_date:
                    self.delete_backup(backup["path"])
                    
        except Exception as e:
            print(f"❌ 清理旧备份失败: {e}")
            
    def start_auto_backup(self):
        """启动自动备份"""
        def auto_backup_worker():
            while self.auto_backup_enabled:
                try:
                    # 检查是否需要备份
                    if self.should_create_backup():
                        backup_name = f"auto_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        self.create_backup(backup_name)
                        print(f"🔄 自动备份完成: {backup_name}")
                        
                    # 等待下次检查
                    time.sleep(3600)  # 每小时检查一次
                    
                except Exception as e:
                    print(f"❌ 自动备份错误: {e}")
                    time.sleep(3600)
                    
        # 启动后台线程
        backup_thread = threading.Thread(target=auto_backup_worker, daemon=True)
        backup_thread.start()
        
    def should_create_backup(self) -> bool:
        """检查是否需要创建备份"""
        try:
            backups = self.get_backup_list()
            
            # 如果没有备份，需要创建
            if not backups:
                return True
                
            # 检查最新备份的时间
            latest_backup = backups[0]
            time_diff = datetime.now() - latest_backup["created_time"]
            
            # 如果超过备份间隔，需要创建
            return time_diff.total_seconds() > (self.backup_interval * 3600)
            
        except Exception as e:
            print(f"❌ 检查备份需求失败: {e}")
            return False
            
    def close_database_connections(self):
        """关闭数据库连接"""
        # 这里可以添加关闭数据库连接的逻辑
        # 具体实现取决于数据库管理器的设计
        pass
        
    def export_backup_info(self) -> str:
        """导出备份信息"""
        try:
            backups = self.get_backup_list()
            
            info = {
                "backup_directory": self.backup_dir,
                "total_backups": len(backups),
                "auto_backup_enabled": self.auto_backup_enabled,
                "backup_interval_hours": self.backup_interval,
                "max_backups": self.max_backups,
                "backups": backups
            }
            
            return json.dumps(info, indent=2, default=str, ensure_ascii=False)

        except Exception as e:
            print(f"❌ 导出备份信息失败: {e}")
            return "{}"

    def set_auto_backup(self, enabled: bool, interval_hours: int = 24):
        """设置自动备份"""
        self.auto_backup_enabled = enabled
        self.backup_interval = interval_hours

    def get_backup_stats(self) -> Dict:
        """获取备份统计信息"""
        try:
            backups = self.get_backup_list()

            total_size = sum(backup["size"] for backup in backups)
            auto_backups = [b for b in backups if b["backup_type"] == "auto"]
            manual_backups = [b for b in backups if b["backup_type"] == "manual"]

            return {
                "total_backups": len(backups),
                "auto_backups": len(auto_backups),
                "manual_backups": len(manual_backups),
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "latest_backup": backups[0]["created_time"] if backups else None,
                "oldest_backup": backups[-1]["created_time"] if backups else None
            }

        except Exception as e:
            print(f"❌ 获取备份统计失败: {e}")
            return {}
