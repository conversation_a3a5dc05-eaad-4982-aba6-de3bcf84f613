#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API客户端
"""

import requests
import json
from typing import List, Dict, Any
from src.api.base_ai_client import BaseAIClient

class DeepSeekClient(BaseAIClient):
    """DeepSeek API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com"):
        """初始化DeepSeek客户端"""
        super().__init__(api_key)
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def generate_questions(self, material: str, question_types: List[str], 
                          num_questions: int = 20) -> List[Dict[str, Any]]:
        """使用DeepSeek生成题目"""
        try:
            prompt = self.create_prompt_for_questions(material, question_types, num_questions)
            
            # 构建请求数据
            data = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的教育工作者，擅长根据学习材料生成高质量的考试题目。请严格按照要求的JSON格式返回题目。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 4000
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 解析题目
                questions = self.parse_questions_response(content)
                return self.validate_questions(questions)
            else:
                raise Exception(f"API请求失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"DeepSeek生成题目失败: {str(e)}")
    
    def evaluate_answer(self, question: str, answer: str, correct_answer: str) -> Dict[str, Any]:
        """使用DeepSeek评估主观题答案"""
        try:
            prompt = f"""
请评估以下主观题的答案：

题目：{question}

参考答案：{correct_answer}

学生答案：{answer}

请从以下几个方面进行评估：
1. 内容准确性（是否回答了问题的核心）
2. 完整性（是否涵盖了主要要点）
3. 逻辑性（答案是否有条理）
4. 表达清晰度

请给出0-10分的评分，并提供具体的反馈意见。

请按照以下JSON格式返回：
{{
    "score": 实际得分,
    "max_score": 10,
    "feedback": "详细的反馈意见，包括优点和需要改进的地方"
}}
"""
            
            data = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的教师，擅长评估学生的答案并给出建设性的反馈。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                try:
                    evaluation = json.loads(content)
                    return evaluation
                except json.JSONDecodeError:
                    # 如果无法解析JSON，返回默认评估
                    return {
                        "score": 5,
                        "max_score": 10,
                        "feedback": "评估失败，请人工评分"
                    }
            else:
                raise Exception(f"API请求失败: {response.status_code}")
                
        except Exception as e:
            return {
                "score": 5,
                "max_score": 10,
                "feedback": f"评估失败: {str(e)}"
            }
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            data = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello"
                    }
                ],
                "max_tokens": 10
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=data,
                timeout=10
            )
            
            return response.status_code == 200
            
        except:
            return False
