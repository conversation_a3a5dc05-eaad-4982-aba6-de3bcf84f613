#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

def setup_test_wrong_questions():
    """设置测试错题"""
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 连接数据库
    db_path = "data/exam_system.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("🔧 设置测试错题...")
        
        # 创建错题表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS wrong_questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                question_text TEXT NOT NULL,
                question_type TEXT NOT NULL,
                correct_answer TEXT NOT NULL,
                user_answer TEXT NOT NULL,
                explanation TEXT,
                is_favorite BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 清空现有错题
        cursor.execute("DELETE FROM wrong_questions")
        print("✓ 清空现有错题")
        
        # 添加测试错题
        test_questions = [
            (
                "测试错题1：Python中哪个关键字用于定义函数？",
                "single_choice",
                "B",
                "A", 
                "Python中使用def关键字来定义函数，而不是function。",
                0
            ),
            (
                "测试错题2：Python是开源的编程语言。",
                "true_false",
                "对",
                "错",
                "Python确实是开源的编程语言，由Python软件基金会维护。",
                1
            ),
            (
                "测试错题3：列表和元组的主要区别是什么？",
                "short_answer",
                "列表可变，元组不可变",
                "不知道",
                "列表(list)是可变的数据结构，可以修改元素；元组(tuple)是不可变的数据结构，创建后不能修改。",
                0
            ),
            (
                "测试错题4：以下哪个是Python的Web框架？",
                "single_choice",
                "C",
                "A",
                "Django是Python最流行的Web框架之一，Flask也是常用的轻量级框架。",
                0
            ),
            (
                "测试错题5：Python支持面向对象编程。",
                "true_false",
                "对",
                "错",
                "Python完全支持面向对象编程，包括类、继承、多态等特性。",
                1
            )
        ]
        
        # 插入测试错题
        for i, (question, q_type, correct, user, explanation, favorite) in enumerate(test_questions, 1):
            cursor.execute('''
                INSERT INTO wrong_questions 
                (question_text, question_type, correct_answer, user_answer, explanation, is_favorite, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (question, q_type, correct, user, explanation, favorite, datetime.now()))
            print(f"✓ 添加错题 {i}: {question[:30]}...")
        
        conn.commit()
        
        # 验证结果
        cursor.execute("SELECT COUNT(*) FROM wrong_questions")
        count = cursor.fetchone()[0]
        print(f"✅ 成功添加 {count} 道测试错题")
        
        # 显示错题列表
        cursor.execute("SELECT id, question_text, question_type, correct_answer, user_answer, is_favorite FROM wrong_questions")
        questions = cursor.fetchall()
        
        print("\n📋 错题列表:")
        for q in questions:
            favorite_mark = "⭐" if q[5] else "  "
            print(f"  {favorite_mark} ID:{q[0]} - {q[1][:40]}...")
            print(f"      类型:{q[2]} | 正确:{q[3]} | 我的:{q[4]}")
        
        print(f"\n🎉 测试错题设置完成！")
        print(f"现在您可以：")
        print(f"1. 启动程序: python main.py")
        print(f"2. 点击'错题本'")
        print(f"3. 应该能看到 {count} 道测试错题")
        print(f"4. 可以双击错题查看解析")
        print(f"5. 可以使用工具栏按钮操作错题")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_test_wrong_questions()
