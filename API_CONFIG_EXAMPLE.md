# API配置示例

本文档提供各种AI服务的配置示例，帮助您快速配置考试系统。

## OpenAI API配置

### 官方API
```
API Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Base URL: https://api.openai.com
模型: gpt-3.5-turbo
```

### 中转API示例
```
API Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Base URL: https://api.openai-proxy.com
模型: gpt-3.5-turbo
```

### 常用模型选择
- `gpt-4`: 最强模型，适合复杂题目生成
- `gpt-4-turbo`: 性价比较高的GPT-4版本
- `gpt-3.5-turbo`: 速度快，成本低，适合大量题目生成
- `gpt-3.5-turbo-16k`: 支持更长上下文

## DeepSeek API配置

```
API Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Base URL: https://api.deepseek.com
模型: deepseek-chat
```

## Gemini API配置

```
API Key: AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
模型: gemini-pro
```

## Doubao API配置

豆包使用OpenAI兼容的API格式，支持多种模型。

```
API Key: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
Endpoint: https://ark.cn-beijing.volces.com/api/v3
模型: doubao-pro-4k
```

### 可用模型
- `doubao-pro-4k`: 专业版4K上下文
- `doubao-pro-32k`: 专业版32K上下文
- `doubao-pro-128k`: 专业版128K上下文
- `doubao-lite-4k`: 轻量版4K上下文
- `doubao-lite-32k`: 轻量版32K上下文
- `doubao-seed-1-6-250615`: 特定版本模型

### 注意事项
- 豆包API使用OpenAI兼容格式
- 不同模型的上下文长度和性能不同
- 请根据实际需求选择合适的模型

## 配置步骤

1. 打开考试系统
2. 点击菜单栏"设置" -> "系统设置"
3. 切换到"API设置"标签页
4. 选择要配置的AI服务
5. 填入相应的API密钥、端点和模型
6. 点击"测试连接"验证配置
7. 点击"保存设置"

## 注意事项

- API密钥请妥善保管，不要泄露给他人
- 中转API的稳定性和安全性请自行评估
- 不同模型的费用和性能差异较大，请根据需求选择
- 建议配置多个AI服务作为备选方案

## 故障排除

### 连接失败
1. 检查API密钥是否正确
2. 检查网络连接
3. 检查API端点是否可访问
4. 确认账户余额充足

### 生成失败
1. 检查模型是否支持
2. 检查输入内容是否过长
3. 尝试更换其他模型
4. 查看详细错误信息

### 中转API问题
1. 确认中转服务是否正常
2. 检查中转API的格式要求
3. 联系中转服务提供商
